/**
 * 测试合同编号分配功能
 */

const { ContractModel } = require('./src/utils/database');

async function testAssignNumber() {
  try {
    console.log('🔍 测试合同编号分配功能...');
    
    // 1. 检查是否有待分配编号的合同
    console.log('\n1. 查找待分配编号的合同:');
    const pendingContracts = await ContractModel.findPendingContractNumber();
    console.log('待分配编号的合同:', pendingContracts);
    
    if (!pendingContracts.contracts || pendingContracts.contracts.length === 0) {
      console.log('❌ 没有找到待分配编号的合同');
      return;
    }
    
    const contract = pendingContracts.contracts[0];
    console.log('测试合同:', contract);
    
    // 2. 测试查找合同编号是否存在
    console.log('\n2. 测试合同编号查找:');
    const testNumber = 'TEST-2025-001';
    const existingContract = await ContractModel.findByContractNumber(testNumber);
    console.log(`合同编号 ${testNumber} 是否存在:`, existingContract ? '是' : '否');
    
    // 3. 测试合同查找
    console.log('\n3. 测试合同查找:');
    const foundContract = await ContractModel.findById(contract.id);
    console.log('找到的合同:', foundContract);
    
    console.log('\n✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testAssignNumber().then(() => {
  console.log('\n🎉 测试结束');
  process.exit(0);
}).catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
