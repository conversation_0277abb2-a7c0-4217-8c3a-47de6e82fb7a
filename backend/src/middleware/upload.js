/**
 * 文件上传中间件
 * 使用 Multer 处理文件上传，支持 PDF 文件
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { FILE_CONFIG, RESPONSE_MESSAGES } = require('../utils/constants');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 存储配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名：时间戳 + 随机数 + 原始扩展名
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const ext = path.extname(file.originalname);
    const filename = `${timestamp}_${random}${ext}`;
    cb(null, filename);
  }
});

// 清理文件名，将非法字符替换为下划线
const sanitizeFilename = (filename) => {

  // 尝试修复编码问题
  let decodedFilename = filename;
  try {
    // 如果文件名是乱码，尝试重新解码
    if (filename.includes('æ') || filename.includes('ï¼') || filename.includes('è')) {
      // 这些是UTF-8被错误解码为Latin-1的典型特征
      const buffer = Buffer.from(filename, 'latin1');
      decodedFilename = buffer.toString('utf8');
    }
  } catch (error) {
  }

  // 获取文件扩展名
  const ext = path.extname(decodedFilename);
  const basename = path.basename(decodedFilename, ext);


  // 将非法字符替换为下划线
  // 保留中文字符、英文字母、数字、常用符号，其他字符替换为下划线
  // 允许的字符：
  // - 英文字母和数字：a-zA-Z0-9
  // - 中文字符：\u4e00-\u9fa5
  // - 常用符号：- . () （） 【】 [] ： : ， , ； ; ！ ! ？ ? " " ' ' 《 》 〈 〉
  const cleanBasename = basename.replace(/[^a-zA-Z0-9\u4e00-\u9fa5\-. ()（）【】\[\]：:，,；;！!？?""''《》〈〉]/g, '_');

  // 避免连续的下划线，将多个连续下划线替换为单个下划线
  const finalBasename = cleanBasename.replace(/_+/g, '_').replace(/^_+|_+$/g, '');


  const result = finalBasename + ext;

  return result;
};

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const ext = path.extname(file.originalname).toLowerCase();
  const mimeType = file.mimetype;

  if (!FILE_CONFIG.ALLOWED_TYPES.includes(ext)) {
    return cb(new Error(RESPONSE_MESSAGES.INVALID_FILE_TYPE), false);
  }

  if (!FILE_CONFIG.ALLOWED_MIME_TYPES.includes(mimeType)) {
    return cb(new Error(RESPONSE_MESSAGES.INVALID_FILE_TYPE), false);
  }

  // 自动清理文件名，不再拒绝包含特殊字符的文件
  file.originalname = sanitizeFilename(file.originalname);

  cb(null, true);
};

// Multer 配置
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: FILE_CONFIG.MAX_SIZE,
    files: 1 // 一次只能上传一个文件
  }
});

// 单文件上传中间件
const uploadSingle = upload.single('file');

// 包装上传中间件，提供更好的错误处理
const uploadMiddleware = (req, res, next) => {
  uploadSingle(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          success: false,
          message: RESPONSE_MESSAGES.FILE_TOO_LARGE,
          maxSize: `${FILE_CONFIG.MAX_SIZE / 1024 / 1024}MB`
        });
      }

      if (err.code === 'LIMIT_FILE_COUNT') {
        return res.status(400).json({
          success: false,
          message: '一次只能上传一个文件'
        });
      }

      return res.status(400).json({
        success: false,
        message: '文件上传失败',
        error: err.message
      });
    }

    if (err) {
      return res.status(400).json({
        success: false,
        message: err.message || RESPONSE_MESSAGES.UPLOAD_FAILED
      });
    }

    // 检查是否有文件上传
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    // 添加文件信息到请求对象
    req.fileInfo = {
      originalName: req.file.originalname,
      filename: req.file.filename,
      path: req.file.path,
      size: req.file.size,
      mimetype: req.file.mimetype
    };

    next();
  });
};

// 删除文件的工具函数
const deleteFile = (filePath) => {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, (err) => {
      if (err && err.code !== 'ENOENT') {
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

// 检查文件是否存在
const fileExists = (filePath) => {
  return fs.existsSync(filePath);
};

// 获取文件信息
const getFileInfo = (filePath) => {
  return new Promise((resolve, reject) => {
    fs.stat(filePath, (err, stats) => {
      if (err) {
        reject(err);
      } else {
        resolve({
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          isFile: stats.isFile()
        });
      }
    });
  });
};

// 生成安全的文件访问URL - 修复安全漏洞
// 不再返回直接的 /uploads 路径，而是返回需要权限验证的API路径
const generateFileUrl = (filename) => {
  // ⚠️ 安全修复：不再使用无权限控制的 /uploads 路径
  // 返回需要权限验证的临时预览API路径
  return `/api/files/temp-preview/${filename}`;
};

// 验证文件访问权限
const validateFileAccess = async (req, res, next) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(uploadDir, filename);

    // 检查文件是否存在
    if (!fileExists(filePath)) {
      return res.status(404).json({
        success: false,
        message: RESPONSE_MESSAGES.FILE_NOT_FOUND
      });
    }

    // 防止路径遍历攻击
    const resolvedPath = path.resolve(filePath);
    const resolvedUploadDir = path.resolve(uploadDir);

    if (!resolvedPath.startsWith(resolvedUploadDir)) {
      return res.status(403).json({
        success: false,
        message: '非法的文件访问'
      });
    }

    req.filePath = filePath;
    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      message: RESPONSE_MESSAGES.INTERNAL_ERROR
    });
  }
};

module.exports = {
  uploadMiddleware,
  deleteFile,
  fileExists,
  getFileInfo,
  generateFileUrl,
  validateFileAccess,
  uploadDir,
  sanitizeFilename
};
