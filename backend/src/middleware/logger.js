/**
 * 日志记录中间件
 * 自动记录API调用和重要操作
 */

const { logOperation, ACTIONS, RESOURCE_TYPES } = require('../utils/logger');

/**
 * 获取客户端IP地址
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null);
}

/**
 * 创建日志记录中间件
 * @param {string} action - 操作类型
 * @param {string} resourceType - 资源类型
 * @param {Object} options - 可选配置
 * @param {Function} [options.getResourceId] - 获取资源ID的函数
 * @param {Function} [options.getDetails] - 获取详细信息的函数
 * @param {boolean} [options.logSuccess=true] - 是否记录成功操作
 * @param {boolean} [options.logError=true] - 是否记录错误操作
 */
function createLogMiddleware(action, resourceType, options = {}) {
  const {
    getResourceId = null,
    getDetails = null,
    logSuccess = true,
    logError = true
  } = options;

  return async (req, res, next) => {
    const originalSend = res.send;
    const originalJson = res.json;
    
    // 记录请求开始时间
    const startTime = Date.now();
    
    // 重写响应方法来捕获响应
    res.send = function(data) {
      recordLog(req, res, data, 'send');
      return originalSend.call(this, data);
    };
    
    res.json = function(data) {
      recordLog(req, res, data, 'json');
      return originalJson.call(this, data);
    };
    
    // 记录日志的内部函数
    async function recordLog(req, res, responseData, method) {
      try {
        const userId = req.user?.id;
        if (!userId) return; // 没有用户信息则不记录
        
        const isSuccess = res.statusCode >= 200 && res.statusCode < 300;
        const shouldLog = (isSuccess && logSuccess) || (!isSuccess && logError);
        
        if (!shouldLog) return;
        
        const resourceId = getResourceId ? getResourceId(req, responseData) : null;
        const details = getDetails ? getDetails(req, responseData) : null;
        const ipAddress = getClientIP(req);
        const userAgent = req.headers['user-agent'];
        const processingTime = Date.now() - startTime;
        
        // 构建详细信息
        let logDetails = {
          method: req.method,
          url: req.path,
          processingTime: `${processingTime}ms`,
          statusCode: res.statusCode
        };
        
        if (details) {
          logDetails.customDetails = details;
        }
        
        // 记录请求参数（敏感信息除外）
        if (req.body && Object.keys(req.body).length > 0) {
          const sanitizedBody = sanitizeRequestBody(req.body);
          if (Object.keys(sanitizedBody).length > 0) {
            logDetails.requestBody = sanitizedBody;
          }
        }
        
        await logOperation({
          userId,
          action,
          resourceType,
          resourceId,
          details: JSON.stringify(logDetails),
          ipAddress,
          userAgent,
          status: isSuccess ? 'success' : 'failed'
        });
        
      } catch (error) {
        console.error('❌ 日志记录失败:', error.message);
      }
    }
    
    next();
  };
}

/**
 * 清理请求体中的敏感信息
 */
function sanitizeRequestBody(body) {
  const sanitized = { ...body };
  const sensitiveFields = ['password', 'token', 'secret', 'key'];
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '***';
    }
  }
  
  return sanitized;
}

/**
 * 认证操作日志中间件
 */
const authLogMiddleware = createLogMiddleware(ACTIONS.LOGIN, RESOURCE_TYPES.USER, {
  getResourceId: (req, res) => req.user?.id,
  getDetails: (req, res) => ({
    username: req.body?.username,
    loginTime: new Date().toISOString()
  })
});

/**
 * 合同操作日志中间件
 */
const contractLogMiddleware = (action) => {
  return createLogMiddleware(action, RESOURCE_TYPES.CONTRACT, {
    getResourceId: (req, res) => req.params?.id || res.contractId,
    getDetails: (req, res) => {
      const details = {};
      
      if (req.body?.filename) details.filename = req.body.filename;
      if (req.body?.status) details.status = req.body.status;
      if (req.body?.result) details.reviewResult = req.body.result;
      if (req.body?.comment) details.comment = req.body.comment;
      
      return details;
    }
  });
};

/**
 * 用户管理日志中间件
 */
const userLogMiddleware = (action) => {
  return createLogMiddleware(action, RESOURCE_TYPES.USER, {
    getResourceId: (req, res) => req.params?.id || res.userId,
    getDetails: (req, res) => {
      const details = {};
      
      if (req.body?.username) details.username = req.body.username;
      if (req.body?.role) details.role = req.body.role;
      if (req.body?.status) details.status = req.body.status;
      
      return details;
    }
  });
};

/**
 * 文件操作日志中间件
 */
const fileLogMiddleware = (action) => {
  return createLogMiddleware(action, RESOURCE_TYPES.FILE, {
    getResourceId: (req, res) => req.params?.filename || req.file?.filename,
    getDetails: (req, res) => {
      const details = {};
      
      if (req.file) {
        details.filename = req.file.filename;
        details.originalName = req.file.originalname;
        details.size = req.file.size;
        details.mimeType = req.file.mimetype;
      }
      
      return details;
    }
  });
};

/**
 * 系统操作日志中间件
 */
const systemLogMiddleware = (action) => {
  return createLogMiddleware(action, RESOURCE_TYPES.SYSTEM, {
    getDetails: (req, res) => ({
      operation: action,
      timestamp: new Date().toISOString()
    })
  });
};

module.exports = {
  createLogMiddleware,
  authLogMiddleware,
  contractLogMiddleware,
  userLogMiddleware,
  fileLogMiddleware,
  systemLogMiddleware
};