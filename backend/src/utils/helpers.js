/**
 * 辅助函数工具集
 * 提供常用的工具函数和业务逻辑辅助方法
 */

const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { JWT_CONFIG, SERIAL_NUMBER, CONTRACT_STATUS, STATUS_TRANSITIONS } = require('./constants');

/**
 * 密码相关工具
 */
const PasswordUtils = {
  // 加密密码
  async hash(password) {
    // 根据环境调整saltRounds：开发环境4，生产环境6（降低CPU负载）
    const defaultRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || defaultRounds;
    return await bcrypt.hash(password, saltRounds);
  },

  // 验证密码
  async verify(password, hashedPassword) {
    return await bcrypt.compare(password, hashedPassword);
  },

  // 生成随机密码
  generate(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }
};

/**
 * JWT 令牌工具
 */
const TokenUtils = {
  // 生成 JWT 令牌
  generate(payload) {
    return jwt.sign(payload, JWT_CONFIG.SECRET, {
      expiresIn: JWT_CONFIG.EXPIRES_IN,
      algorithm: JWT_CONFIG.ALGORITHM
    });
  },

  // 验证 JWT 令牌
  verify(token) {
    return jwt.verify(token, JWT_CONFIG.SECRET);
  },

  // 解码 JWT 令牌（不验证）
  decode(token) {
    return jwt.decode(token);
  }
};

/**
 * 响应格式化工具
 */
const ResponseUtils = {
  // 成功响应
  success(data = null, message = '操作成功') {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  },

  // 错误响应
  error(message = '操作失败', code = 500, details = null) {
    return {
      success: false,
      message,
      code,
      details,
      timestamp: new Date().toISOString()
    };
  },

  // 分页响应
  paginated(data, pagination, message = '查询成功') {
    return {
      success: true,
      message,
      data,
      pagination,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 数据验证工具
 */
const ValidationUtils = {
  // 验证邮箱格式
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 验证用户名格式
  isValidUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  },

  // 验证密码强度
  isValidPassword(password) {
    // 至少6位，包含字母和数字
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/;
    return passwordRegex.test(password);
  },

  // 验证流水号格式
  isValidSerialNumber(serialNumber) {
    return SERIAL_NUMBER.PATTERN.test(serialNumber);
  },

  // 验证必填字段
  validateRequired(obj, requiredFields) {
    const missing = [];
    requiredFields.forEach(field => {
      if (!obj[field] || (typeof obj[field] === 'string' && obj[field].trim() === '')) {
        missing.push(field);
      }
    });
    return missing;
  }
};

/**
 * 业务逻辑工具
 */
const BusinessUtils = {
  // 生成流水号
  generateSerialNumber(count) {
    const number = String(count + 1).padStart(SERIAL_NUMBER.LENGTH, '0');
    return `${SERIAL_NUMBER.PREFIX}${number}`;
  },

  // 验证状态流转是否合法
  isValidStatusTransition(currentStatus, newStatus) {
    const allowedTransitions = STATUS_TRANSITIONS[currentStatus] || [];
    return allowedTransitions.includes(newStatus);
  },

  // 检查用户是否可以修改合同
  canModifyContract(contract, user) {
    // 只有提交人可以修改
    if (contract.submitter_id !== user.id) {
      return false;
    }

    // 只有 pending 或 rejected 状态可以修改
    return [CONTRACT_STATUS.PENDING, CONTRACT_STATUS.REJECTED].includes(contract.status);
  },

  // 检查用户是否可以审核合同
  canReviewContract(contract, user) {
    // 只有待审核状态的合同可以审核
    const reviewableStatuses = [CONTRACT_STATUS.PENDING, CONTRACT_STATUS.PENDING_CITY_REVIEW];
    if (!reviewableStatuses.includes(contract.status)) {
      return false;
    }

    // 不能审核自己提交的合同
    if (contract.submitter_id === user.id) {
      return false;
    }

    // 管理员可以审核任何合同
    if (user.role === 'admin') {
      return true;
    }

    // 审核员只能审核分配给自己的合同
    if ((user.role === 'county_reviewer' || user.role === 'city_reviewer') && contract.reviewer_id === user.id) {
      // 额外检查：市局审核员只能审核待市局审核的合同
      if (user.role === 'city_reviewer' && contract.status !== CONTRACT_STATUS.PENDING_CITY_REVIEW) {
        return false;
      }
      // 县局审核员只能审核待审核的合同
      if (user.role === 'county_reviewer' && contract.status !== CONTRACT_STATUS.PENDING) {
        return false;
      }
      return true;
    }

    return false;
  },

  // 检查用户是否可以查看合同
  async canViewContract(contract, user) {
    if (!contract || !user) {
      return false;
    }

    // 管理员可以查看所有合同
    if (user.role === 'admin') {
      return true;
    }

    // 提交人可以查看自己的合同
    if (contract.submitter_id === user.id) {
      return true;
    }

    // 审核员可以查看分配给自己的合同
    if ((user.role === 'reviewer' || user.role === 'county_reviewer' || user.role === 'city_reviewer') && contract.reviewer_id === user.id) {
      return true;
    }

    // 审核员可以查看自己曾经审核过的合同
    if (user.role === 'county_reviewer' || user.role === 'city_reviewer') {
      try {
        const { ContractReviewModel } = require('./database');
        const hasReviewed = await ContractReviewModel.hasUserReviewedContract(contract.id, user.id);
        if (hasReviewed) {
          return true;
        }
      } catch (error) {
        console.error('检查审核历史错误:', error);
      }
    }

    // 法规员可以查看已通过审核的合同（用于分配合同编号）
    if (user.role === 'legal_officer') {
      const { CONTRACT_STATUS } = require('./constants');
      // 法规员可以查看待分配编号和已完成的合同
      return [CONTRACT_STATUS.PENDING_CONTRACT_NUMBER, CONTRACT_STATUS.COMPLETED].includes(contract.status);
    }

    return false;
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 生成唯一文件名
  generateUniqueFilename(originalName) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const ext = originalName.substring(originalName.lastIndexOf('.'));
    return `${timestamp}_${random}${ext}`;
  }
};

// 引入统一时间处理工具
const DateTimeUtils = require('./dateTime');

/**
 * 日期时间工具（使用统一的北京时间处理）
 * @deprecated 建议直接使用 DateTimeUtils
 */
const DateUtils = {
  // 格式化日期时间
  format(date, format = 'YYYY-MM-DD HH:mm:ss') {
    return DateTimeUtils.format(date, format);
  },

  // 获取相对时间描述
  getRelativeTime(date) {
    return DateTimeUtils.getRelativeTime(date);
  },

  // 检查是否为今天
  isToday(date) {
    return DateTimeUtils.isToday(date);
  }
};

/**
 * 错误处理工具
 */
const ErrorUtils = {
  // 创建自定义错误
  createError(message, status = 500, code = null) {
    const error = new Error(message);
    error.status = status;
    error.code = code;
    return error;
  },

  // 处理数据库错误
  handleDatabaseError(error) {
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      return this.createError('数据已存在', 409, 'DUPLICATE_ENTRY');
    }

    if (error.code === 'SQLITE_CONSTRAINT_FOREIGNKEY') {
      return this.createError('关联数据不存在', 400, 'FOREIGN_KEY_CONSTRAINT');
    }

    return this.createError('数据库操作失败', 500, 'DATABASE_ERROR');
  }
};

module.exports = {
  PasswordUtils,
  TokenUtils,
  ResponseUtils,
  ValidationUtils,
  BusinessUtils,
  DateUtils,
  DateTimeUtils, // 新的统一时间处理工具
  ErrorUtils
};
