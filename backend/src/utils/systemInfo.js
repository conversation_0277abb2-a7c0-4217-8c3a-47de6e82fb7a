/**
 * 系统信息获取工具
 * 提供真实的系统运行状态信息
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { SYSTEM_CONFIG } = require('./constants');

/**
 * 系统信息工具类
 */
class SystemInfo {
  /**
   * 获取系统版本信息
   * @returns {string} 系统版本
   */
  static getVersion() {
    try {
      // 尝试从package.json读取版本
      const packagePath = path.join(__dirname, '../../package.json');
      if (fs.existsSync(packagePath)) {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        return packageJson.version || SYSTEM_CONFIG.VERSION;
      }
      return SYSTEM_CONFIG.VERSION;
    } catch (error) {
      console.warn('获取系统版本失败:', error.message);
      return SYSTEM_CONFIG.VERSION;
    }
  }

  /**
   * 获取系统运行时间
   * @returns {string} 格式化的运行时间
   */
  static getUptime() {
    try {
      const uptimeSeconds = process.uptime();
      return this.formatUptime(uptimeSeconds);
    } catch (error) {
      console.warn('获取系统运行时间失败:', error.message);
      return '未知';
    }
  }

  /**
   * 获取系统运行时间（秒数）
   * @returns {number} 运行时间秒数
   */
  static getUptimeSeconds() {
    try {
      return process.uptime();
    } catch (error) {
      console.warn('获取系统运行时间失败:', error.message);
      return 0;
    }
  }

  /**
   * 获取磁盘使用情况
   * @returns {string} 磁盘使用百分比
   */
  static getDiskUsage() {
    try {
      // 获取当前目录所在磁盘的使用情况
      const currentDir = process.cwd();

      if (process.platform === 'win32') {
        // Windows系统
        return this.getWindowsDiskUsage(currentDir);
      } else {
        // Linux/Unix系统
        return this.getUnixDiskUsage(currentDir);
      }
    } catch (error) {
      console.warn('获取磁盘使用情况失败:', error.message);
      return '未知';
    }
  }

  /**
   * 获取内存使用情况
   * @returns {Object} 内存使用信息
   */
  static getMemoryUsage() {
    try {
      const memUsage = process.memoryUsage();
      const totalMem = require('os').totalmem();
      const freeMem = require('os').freemem();
      const usedMem = totalMem - freeMem;

      return {
        // 前端期望的格式：used为字节数，用于formatMemory函数
        used: usedMem,
        total: totalMem,
        free: freeMem,
        // 百分比
        usedPercent: Math.round((usedMem / totalMem) * 100),
        // 进程内存信息
        process: {
          rss: Math.round(memUsage.rss / 1024 / 1024), // MB
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        },
        // 系统内存信息（GB）
        system: {
          total: Math.round(totalMem / 1024 / 1024 / 1024), // GB
          used: Math.round(usedMem / 1024 / 1024 / 1024), // GB
          free: Math.round(freeMem / 1024 / 1024 / 1024), // GB
        }
      };
    } catch (error) {
      console.warn('获取内存使用情况失败:', error.message);
      return {
        used: 0,
        total: 0,
        free: 0,
        usedPercent: 0,
        process: { rss: 0, heapUsed: 0, heapTotal: 0 },
        system: { total: 0, used: 0, free: 0 }
      };
    }
  }

  /**
   * 获取最后备份时间（模拟实现）
   * @returns {string} 最后备份时间
   */
  static getLastBackup() {
    try {
      // 检查是否有备份目录或文件
      const backupDir = path.join(__dirname, '../../backups');
      if (fs.existsSync(backupDir)) {
        const files = fs.readdirSync(backupDir);
        if (files.length > 0) {
          // 获取最新的备份文件时间
          const latestFile = files
            .map(file => ({
              name: file,
              time: fs.statSync(path.join(backupDir, file)).mtime
            }))
            .sort((a, b) => b.time - a.time)[0];

          return this.formatDate(latestFile.time);
        }
      }

      // 如果没有备份记录，返回提示
      return '未进行备份';
    } catch (error) {
      console.warn('获取备份信息失败:', error.message);
      return '未知';
    }
  }

  /**
   * 获取完整的系统信息
   * @returns {Object} 系统信息对象
   */
  static getSystemInfo() {
    return {
      version: this.getVersion(),
      uptime: this.getUptime(),
      diskUsage: this.getDiskUsage(),
      memory: this.getMemoryUsage(),
      lastBackup: this.getLastBackup(),
      platform: process.platform,
      nodeVersion: process.version,
      pid: process.pid
    };
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 格式化运行时间
   * @param {number} seconds 运行秒数
   * @returns {string} 格式化的时间字符串
   */
  static formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}天${hours}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  }

  /**
   * 格式化日期
   * @param {Date} date 日期对象
   * @returns {string} 格式化的日期字符串
   */
  static formatDate(date) {
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  }

  /**
   * 获取Windows系统磁盘使用情况
   * @param {string} path 路径
   * @returns {string} 磁盘使用百分比
   */
  static getWindowsDiskUsage(path) {
    try {
      const drive = path.split(':')[0] + ':';
      const output = execSync(`wmic logicaldisk where caption="${drive}" get size,freespace /value`,
        { encoding: 'utf8', timeout: 5000 });

      const lines = output.split('\n').filter(line => line.includes('='));
      let freeSpace = 0;
      let totalSpace = 0;

      lines.forEach(line => {
        if (line.startsWith('FreeSpace=')) {
          freeSpace = parseInt(line.split('=')[1]);
        } else if (line.startsWith('Size=')) {
          totalSpace = parseInt(line.split('=')[1]);
        }
      });

      if (totalSpace > 0) {
        const usedSpace = totalSpace - freeSpace;
        const usagePercent = Math.round((usedSpace / totalSpace) * 100);
        return `${usagePercent}%`;
      }

      return '未知';
    } catch (error) {
      console.warn('获取Windows磁盘使用情况失败:', error.message);
      return '未知';
    }
  }

  /**
   * 获取Unix/Linux系统磁盘使用情况
   * @param {string} path 路径
   * @returns {string} 磁盘使用百分比
   */
  static getUnixDiskUsage(path) {
    try {
      const output = execSync(`df -h "${path}" | tail -1`,
        { encoding: 'utf8', timeout: 5000 });

      const parts = output.trim().split(/\s+/);
      if (parts.length >= 5) {
        // df命令输出格式: Filesystem Size Used Avail Use% Mounted
        const usagePercent = parts[4];
        return usagePercent;
      }

      return '未知';
    } catch (error) {
      console.warn('获取Unix磁盘使用情况失败:', error.message);
      return '未知';
    }
  }
}

module.exports = SystemInfo;
