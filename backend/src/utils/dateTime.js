/**
 * 统一时间处理工具类
 * 提供北京时间（UTC+8）的统一处理方法
 * 数据库存储UTC时间，显示时转换为北京时间
 */

const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const relativeTime = require('dayjs/plugin/relativeTime');
const customParseFormat = require('dayjs/plugin/customParseFormat');

// 加载插件
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);
dayjs.extend(customParseFormat);

// 设置中文相对时间
dayjs.locale('zh-cn', {
  relativeTime: {
    future: '%s后',
    past: '%s前',
    s: '几秒',
    m: '1分钟',
    mm: '%d分钟',
    h: '1小时',
    hh: '%d小时',
    d: '1天',
    dd: '%d天',
    M: '1个月',
    MM: '%d个月',
    y: '1年',
    yy: '%d年'
  }
});

// 北京时区常量
const BEIJING_TIMEZONE = 'Asia/Shanghai';
const DEFAULT_FORMAT = 'YYYY-MM-DD HH:mm:ss';

/**
 * 统一时间处理工具类
 */
class DateTimeUtils {
  /**
   * 获取当前北京时间
   * @param {string} format - 时间格式，默认 'YYYY-MM-DD HH:mm:ss'
   * @returns {string} 格式化的北京时间字符串
   */
  static now(format = DEFAULT_FORMAT) {
    return dayjs().tz(BEIJING_TIMEZONE).format(format);
  }

  /**
   * 获取当前UTC时间（用于数据库存储）
   * @returns {string} ISO格式的UTC时间字符串
   * @deprecated 建议使用 nowBeijing() 直接存储北京时间
   */
  static nowUTC() {
    return dayjs().utc().toISOString();
  }

  /**
   * 获取当前北京时间（用于数据库存储）
   * @param {string} format - 时间格式，默认 'YYYY-MM-DD HH:mm:ss'
   * @returns {string} 格式化的北京时间字符串
   */
  static nowBeijing(format = DEFAULT_FORMAT) {
    return dayjs().tz(BEIJING_TIMEZONE).format(format);
  }

  /**
   * 将任意时间转换为北京时间显示
   * @param {string|Date|dayjs} date - 输入时间
   * @param {string} format - 输出格式，默认 'YYYY-MM-DD HH:mm:ss'
   * @returns {string} 格式化的北京时间字符串
   */
  static format(date, format = DEFAULT_FORMAT) {
    if (!date) return '';

    try {
      // 如果输入是UTC时间字符串，先转换为dayjs对象
      const dayjsObj = dayjs(date).utc();
      if (!dayjsObj.isValid()) return '';

      // 转换为北京时间并格式化
      return dayjsObj.tz(BEIJING_TIMEZONE).format(format);
    } catch (error) {
      console.error('时间格式化失败:', error);
      return '';
    }
  }

  /**
   * 将北京时间转换为UTC时间（用于数据库存储）
   * @param {string|Date|dayjs} date - 北京时间
   * @returns {string} ISO格式的UTC时间字符串
   */
  static toUTC(date) {
    if (!date) return null;

    try {
      // 将输入时间视为北京时间，转换为UTC
      return dayjs.tz(date, BEIJING_TIMEZONE).utc().toISOString();
    } catch (error) {
      console.error('时间转换为UTC失败:', error);
      return null;
    }
  }

  /**
   * 从UTC时间转换为北京时间显示
   * @param {string} utcDate - UTC时间字符串
   * @param {string} format - 输出格式，默认 'YYYY-MM-DD HH:mm:ss'
   * @returns {string} 格式化的北京时间字符串
   */
  static fromUTC(utcDate, format = DEFAULT_FORMAT) {
    if (!utcDate) return '';

    try {
      return dayjs(utcDate).utc().tz(BEIJING_TIMEZONE).format(format);
    } catch (error) {
      console.error('UTC时间转换失败:', error);
      return '';
    }
  }

  /**
   * 获取相对时间描述（如：3分钟前、2小时前）
   * @param {string|Date|dayjs} date - 输入时间
   * @returns {string} 相对时间描述
   */
  static getRelativeTime(date) {
    if (!date) return '';

    try {
      const inputTime = dayjs(date).utc().tz(BEIJING_TIMEZONE);
      const now = dayjs().tz(BEIJING_TIMEZONE);

      if (!inputTime.isValid()) return '';

      return inputTime.from(now);
    } catch (error) {
      console.error('相对时间计算失败:', error);
      return '';
    }
  }

  /**
   * 检查是否为今天（北京时间）
   * @param {string|Date|dayjs} date - 输入时间
   * @returns {boolean} 是否为今天
   */
  static isToday(date) {
    if (!date) return false;

    try {
      const inputDate = dayjs(date).utc().tz(BEIJING_TIMEZONE);
      const today = dayjs().tz(BEIJING_TIMEZONE);

      return inputDate.format('YYYY-MM-DD') === today.format('YYYY-MM-DD');
    } catch (error) {
      console.error('判断是否为今天失败:', error);
      return false;
    }
  }

  /**
   * 检查是否为昨天（北京时间）
   * @param {string|Date|dayjs} date - 输入时间
   * @returns {boolean} 是否为昨天
   */
  static isYesterday(date) {
    if (!date) return false;

    try {
      const inputDate = dayjs(date).utc().tz(BEIJING_TIMEZONE);
      const yesterday = dayjs().tz(BEIJING_TIMEZONE).subtract(1, 'day');

      return inputDate.format('YYYY-MM-DD') === yesterday.format('YYYY-MM-DD');
    } catch (error) {
      console.error('判断是否为昨天失败:', error);
      return false;
    }
  }

  /**
   * 获取今天的日期字符串（北京时间）
   * @returns {string} YYYY-MM-DD格式的今天日期
   */
  static getToday() {
    return dayjs().tz(BEIJING_TIMEZONE).format('YYYY-MM-DD');
  }

  /**
   * 获取昨天的日期字符串（北京时间）
   * @returns {string} YYYY-MM-DD格式的昨天日期
   */
  static getYesterday() {
    return dayjs().tz(BEIJING_TIMEZONE).subtract(1, 'day').format('YYYY-MM-DD');
  }

  /**
   * 计算两个日期之间的天数差
   * @param {string|Date|dayjs} date1 - 日期1
   * @param {string|Date|dayjs} date2 - 日期2
   * @returns {number} 天数差（绝对值）
   */
  static daysBetween(date1, date2) {
    if (!date1 || !date2) return 0;

    try {
      const d1 = dayjs(date1).utc().tz(BEIJING_TIMEZONE);
      const d2 = dayjs(date2).utc().tz(BEIJING_TIMEZONE);

      if (!d1.isValid() || !d2.isValid()) return 0;

      return Math.abs(d1.diff(d2, 'day'));
    } catch (error) {
      console.error('计算日期差失败:', error);
      return 0;
    }
  }

  /**
   * 格式化日期范围
   * @param {string|Date|dayjs} startDate - 开始日期
   * @param {string|Date|dayjs} endDate - 结束日期
   * @returns {string} 日期范围字符串
   */
  static formatDateRange(startDate, endDate) {
    const start = this.format(startDate, 'YYYY-MM-DD');
    const end = this.format(endDate, 'YYYY-MM-DD');

    if (!start && !end) return '';
    if (!start) return `至 ${end}`;
    if (!end) return `${start} 至今`;

    return `${start} 至 ${end}`;
  }

  /**
   * 获取本周开始日期（北京时间）
   * @returns {string} YYYY-MM-DD格式的本周开始日期
   */
  static getWeekStart() {
    return dayjs().tz(BEIJING_TIMEZONE).startOf('week').format('YYYY-MM-DD');
  }

  /**
   * 获取本月开始日期（北京时间）
   * @returns {string} YYYY-MM-DD格式的本月开始日期
   */
  static getMonthStart() {
    return dayjs().tz(BEIJING_TIMEZONE).startOf('month').format('YYYY-MM-DD');
  }

  /**
   * 验证时间字符串是否有效
   * @param {string} dateString - 时间字符串
   * @returns {boolean} 是否有效
   */
  static isValid(dateString) {
    if (!dateString) return false;
    return dayjs(dateString).isValid();
  }

  /**
   * 为导出功能格式化时间（兼容旧格式）
   * @param {string|Date|dayjs} dateTime - 输入时间
   * @returns {string} 格式化的时间字符串
   */
  static formatForExport(dateTime) {
    if (!dateTime) return '-';
    return this.format(dateTime, DEFAULT_FORMAT);
  }
}

module.exports = DateTimeUtils;
