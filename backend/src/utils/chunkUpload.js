/**
 * 文件分片上传处理器
 * 支持大文件分片上传、断点续传、文件完整性校验
 */

const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const { promisify } = require('util');
const fsAsync = {
  writeFile: promisify(fs.writeFile),
  readFile: promisify(fs.readFile),
  mkdir: promisify(fs.mkdir),
  unlink: promisify(fs.unlink),
  access: promisify(fs.access),
  readdir: promisify(fs.readdir),
  stat: promisify(fs.stat),
  rm: promisify(fs.rm)
};

const CHUNK_UPLOAD_CONFIG = {
  chunkSize: 1024 * 1024 * 2, // 2MB per chunk
  maxFileSize: 1024 * 1024 * 100, // 100MB max file size
  chunkExpiration: 24 * 60 * 60 * 1000, // 24 hours
  tempDir: path.join(__dirname, '../../temp/chunks'),
  uploadDir: path.join(__dirname, '../../uploads'),
  allowedTypes: ['.pdf', '.doc', '.docx'],
  allowedMimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
};

class ChunkUploadManager {
  constructor() {
    this.uploadSessions = new Map(); // 存储上传会话信息
    this.initDirectories();
    this.startCleanupSchedule();
  }

  /**
   * 初始化必要的目录
   */
  async initDirectories() {
    try {
      await fsAsync.mkdir(CHUNK_UPLOAD_CONFIG.tempDir, { recursive: true });
      await fsAsync.mkdir(CHUNK_UPLOAD_CONFIG.uploadDir, { recursive: true });
    } catch (error) {
      console.error('Failed to initialize chunk upload directories:', error);
    }
  }

  /**
   * 生成上传会话ID
   */
  generateUploadId() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 计算文件哈希值
   */
  calculateHash(buffer) {
    return crypto.createHash('sha256').update(buffer).digest('hex');
  }

  /**
   * 初始化分片上传
   */
  async initializeUpload(fileInfo) {
    const { fileName, fileSize, fileMd5: fileHash, totalChunks, userId } = fileInfo;
    
    // 验证文件信息
    if (!fileName || !fileSize || !totalChunks || !userId) {
      throw new Error('Missing required file information');
    }

    if (fileSize > CHUNK_UPLOAD_CONFIG.maxFileSize) {
      throw new Error('File size exceeds maximum limit');
    }

    // 验证文件类型
    const ext = path.extname(fileName).toLowerCase();
    if (!CHUNK_UPLOAD_CONFIG.allowedTypes.includes(ext)) {
      throw new Error('File type not allowed');
    }

    const uploadId = this.generateUploadId();
    const uploadDir = path.join(CHUNK_UPLOAD_CONFIG.tempDir, uploadId);
    
    await fsAsync.mkdir(uploadDir, { recursive: true });

    const session = {
      uploadId,
      fileName,
      fileSize,
      fileHash,
      totalChunks,
      userId,
      uploadDir,
      uploadedChunks: new Set(),
      createdAt: new Date(),
      lastActivity: new Date()
    };

    this.uploadSessions.set(uploadId, session);

    return {
      uploadId,
      chunkSize: CHUNK_UPLOAD_CONFIG.chunkSize,
      totalChunks,
      message: 'Upload session initialized'
    };
  }

  /**
   * 处理分片上传
   */
  async uploadChunk(uploadId, chunkIndex, chunkData) {
    const session = this.uploadSessions.get(uploadId);
    
    if (!session) {
      throw new Error('Upload session not found');
    }

    // 更新最后活动时间
    session.lastActivity = new Date();

    // 验证分片索引
    if (chunkIndex < 0 || chunkIndex >= session.totalChunks) {
      throw new Error('Invalid chunk index');
    }

    // 检查分片是否已经上传
    if (session.uploadedChunks.has(chunkIndex)) {
      return {
        success: true,
        message: 'Chunk already uploaded',
        uploadedChunks: session.uploadedChunks.size,
        totalChunks: session.totalChunks
      };
    }

    // 保存分片到临时文件
    const chunkPath = path.join(session.uploadDir, `chunk_${chunkIndex}`);
    await fsAsync.writeFile(chunkPath, chunkData);

    // 记录已上传的分片
    session.uploadedChunks.add(chunkIndex);


    return {
      success: true,
      message: 'Chunk uploaded successfully',
      uploadedChunks: session.uploadedChunks.size,
      totalChunks: session.totalChunks,
      progress: (session.uploadedChunks.size / session.totalChunks * 100).toFixed(2)
    };
  }

  /**
   * 合并分片文件
   */
  async mergeChunks(uploadId) {
    const session = this.uploadSessions.get(uploadId);
    
    if (!session) {
      throw new Error('Upload session not found');
    }

    // 检查是否所有分片都已上传
    if (session.uploadedChunks.size !== session.totalChunks) {
      throw new Error('Not all chunks uploaded');
    }

    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const ext = path.extname(session.fileName);
    const finalFileName = `${timestamp}_${random}${ext}`;
    const finalFilePath = path.join(CHUNK_UPLOAD_CONFIG.uploadDir, finalFileName);

    // 创建写入流
    const writeStream = fs.createWriteStream(finalFilePath);

    try {
      // 按顺序读取并合并分片
      for (let i = 0; i < session.totalChunks; i++) {
        const chunkPath = path.join(session.uploadDir, `chunk_${i}`);
        const chunkData = await fsAsync.readFile(chunkPath);
        
        await new Promise((resolve, reject) => {
          writeStream.write(chunkData, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });
      }

      // 关闭写入流
      await new Promise((resolve, reject) => {
        writeStream.end((err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // 验证文件完整性
      if (session.fileHash) {
        const finalFileData = await fsAsync.readFile(finalFilePath);
        const actualHash = this.calculateHash(finalFileData);
        
        if (actualHash !== session.fileHash) {
          await fsAsync.unlink(finalFilePath);
          throw new Error('File integrity check failed');
        }
      }

      // 清理临时文件
      await this.cleanupSession(uploadId);

      const stats = await fsAsync.stat(finalFilePath);

      return {
        success: true,
        message: 'File merged successfully',
        fileName: finalFileName,
        originalName: session.fileName,
        filePath: finalFilePath,
        fileSize: stats.size,
        uploadedAt: new Date()
      };

    } catch (error) {
      // 清理失败的合并文件
      try {
        await fsAsync.unlink(finalFilePath);
      } catch (cleanupError) {
        console.error('Failed to cleanup failed merge file:', cleanupError);
      }
      throw error;
    }
  }

  /**
   * 获取上传状态
   */
  getUploadStatus(uploadId) {
    const session = this.uploadSessions.get(uploadId);
    
    if (!session) {
      return null;
    }

    return {
      uploadId,
      fileName: session.fileName,
      fileSize: session.fileSize,
      totalChunks: session.totalChunks,
      uploadedChunks: session.uploadedChunks.size,
      progress: (session.uploadedChunks.size / session.totalChunks * 100).toFixed(2),
      createdAt: session.createdAt,
      lastActivity: session.lastActivity
    };
  }

  /**
   * 取消上传
   */
  async cancelUpload(uploadId) {
    const session = this.uploadSessions.get(uploadId);
    
    if (!session) {
      throw new Error('Upload session not found');
    }

    await this.cleanupSession(uploadId);
    return { success: true, message: 'Upload cancelled' };
  }

  /**
   * 清理上传会话
   */
  async cleanupSession(uploadId) {
    const session = this.uploadSessions.get(uploadId);
    
    if (session) {
      try {
        // 删除临时目录
        await fsAsync.rm(session.uploadDir, { recursive: true, force: true });
      } catch (error) {
        console.error(`Failed to cleanup session ${uploadId}:`, error);
      }
      
      // 从内存中移除会话
      this.uploadSessions.delete(uploadId);
    }
  }

  /**
   * 获取所有活跃会话
   */
  getActiveSessions() {
    const sessions = [];
    this.uploadSessions.forEach((session, uploadId) => {
      sessions.push({
        uploadId,
        fileName: session.fileName,
        fileSize: session.fileSize,
        progress: (session.uploadedChunks.size / session.totalChunks * 100).toFixed(2),
        createdAt: session.createdAt,
        lastActivity: session.lastActivity
      });
    });
    return sessions;
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions() {
    const now = new Date();
    const expiredSessions = [];

    this.uploadSessions.forEach((session, uploadId) => {
      if (now - session.lastActivity > CHUNK_UPLOAD_CONFIG.chunkExpiration) {
        expiredSessions.push(uploadId);
      }
    });

    for (const uploadId of expiredSessions) {
      await this.cleanupSession(uploadId);
    }

    if (expiredSessions.length > 0) {
    }
  }

  /**
   * 启动清理计划任务
   */
  startCleanupSchedule() {
    // 每小时清理一次过期会话
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000);
  }
}

// 创建单例实例
const chunkUploadManager = new ChunkUploadManager();

module.exports = {
  ChunkUploadManager,
  chunkUploadManager,
  CHUNK_UPLOAD_CONFIG
};