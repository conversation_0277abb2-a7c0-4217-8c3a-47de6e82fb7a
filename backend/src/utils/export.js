/**
 * 数据导出服务
 * 支持Excel和PDF格式的数据导出
 */

const ExcelJS = require('exceljs');
const { jsPDF } = require('jspdf');
const path = require('path');
const fs = require('fs');

/**
 * Excel导出服务
 */
class ExcelExportService {
  /**
   * 导出合同数据为Excel
   * @param {Array} contracts - 合同数据
   * @param {Object} options - 导出选项
   * @returns {Promise<Buffer>} Excel文件缓冲区
   */
  static async exportContracts(contracts, options = {}) {
    const { title = '合同数据导出', sheetName = '合同列表' } = options;

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(sheetName);

    // 设置表头
    worksheet.columns = [
      { header: '合同编号', key: 'serial_number', width: 15 },
      { header: '文件名', key: 'filename', width: 30 },
      { header: '提交人', key: 'submitter_name', width: 12 },
      { header: '审核人', key: 'reviewer_name', width: 12 },
      { header: '状态', key: 'status', width: 12 },
      { header: '文件大小', key: 'file_size', width: 12 },
      { header: '提交时间', key: 'submitted_at', width: 20 },
      { header: '审核时间', key: 'reviewed_at', width: 20 },
      { header: '审核意见', key: 'review_comment', width: 40 }
    ];

    // 设置表头样式
    worksheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // 添加数据
    contracts.forEach((contract, index) => {
      const row = worksheet.addRow({
        serial_number: contract.serial_number,
        filename: contract.filename,
        submitter_name: contract.submitter_name,
        reviewer_name: contract.reviewer_name || '-',
        status: this.getStatusText(contract.status),
        file_size: this.formatFileSize(contract.file_size),
        submitted_at: this.formatDateTime(contract.submitted_at),
        reviewed_at: this.formatDateTime(contract.reviewed_at),
        review_comment: contract.review_comment || '-'
      });

      // 设置数据行样式
      row.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        // 状态列着色
        if (colNumber === 5) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: this.getStatusColor(contract.status) }
          };
        }
      });
    });

    // 添加标题
    worksheet.insertRow(1, [title]);
    worksheet.mergeCells('A1:I1');
    const titleCell = worksheet.getCell('A1');
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD0D0D0' }
    };

    // 添加空行
    worksheet.insertRow(2, []);

    // 调整行高
    worksheet.getRow(1).height = 25;
    worksheet.getRow(3).height = 20;

    // 返回缓冲区
    return await workbook.xlsx.writeBuffer();
  }

  /**
   * 导出用户数据为Excel
   * @param {Array} users - 用户数据
   * @param {Object} options - 导出选项
   * @returns {Promise<Buffer>} Excel文件缓冲区
   */
  static async exportUsers(users, options = {}) {
    const { title = '用户数据导出', sheetName = '用户列表' } = options;

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(sheetName);

    // 设置表头
    worksheet.columns = [
      { header: '用户ID', key: 'id', width: 8 },
      { header: '用户名', key: 'username', width: 15 },
      { header: '真实姓名', key: 'real_name', width: 15 },
      { header: '角色', key: 'role', width: 12 },
      { header: '状态', key: 'status', width: 12 },
      { header: '邮箱', key: 'email', width: 25 },
      { header: '电话', key: 'phone', width: 15 },
      { header: '创建时间', key: 'created_at', width: 20 },
      { header: '更新时间', key: 'updated_at', width: 20 }
    ];

    // 设置表头样式
    worksheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // 添加数据
    users.forEach((user) => {
      const row = worksheet.addRow({
        id: user.id,
        username: user.username,
        real_name: user.real_name || '-',
        role: this.getRoleText(user.role),
        status: this.getUserStatusText(user.status),
        email: user.email || '-',
        phone: user.phone || '-',
        created_at: this.formatDateTime(user.created_at),
        updated_at: this.formatDateTime(user.updated_at)
      });

      // 设置数据行样式
      row.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        // 状态列着色
        if (colNumber === 5) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: this.getUserStatusColor(user.status) }
          };
        }
      });
    });

    // 添加标题
    worksheet.insertRow(1, [title]);
    worksheet.mergeCells('A1:I1');
    const titleCell = worksheet.getCell('A1');
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD0D0D0' }
    };

    // 添加空行
    worksheet.insertRow(2, []);

    // 调整行高
    worksheet.getRow(1).height = 25;
    worksheet.getRow(3).height = 20;

    // 返回缓冲区
    return await workbook.xlsx.writeBuffer();
  }

  /**
   * 导出操作日志为Excel
   * @param {Array} logs - 日志数据
   * @param {Object} options - 导出选项
   * @returns {Promise<Buffer>} Excel文件缓冲区
   */
  static async exportLogs(logs, options = {}) {
    const { title = '操作日志导出', sheetName = '操作日志' } = options;

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(sheetName);

    // 设置表头
    worksheet.columns = [
      { header: '日志ID', key: 'id', width: 8 },
      { header: '用户', key: 'username', width: 15 },
      { header: '操作类型', key: 'action', width: 15 },
      { header: '资源类型', key: 'resource_type', width: 15 },
      { header: '资源ID', key: 'resource_id', width: 10 },
      { header: '状态', key: 'status', width: 10 },
      { header: 'IP地址', key: 'ip_address', width: 15 },
      { header: '操作时间', key: 'created_at', width: 20 },
      { header: '详细信息', key: 'details', width: 40 }
    ];

    // 设置表头样式
    worksheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // 添加数据
    logs.forEach((log) => {
      const row = worksheet.addRow({
        id: log.id,
        username: log.username,
        action: this.getActionText(log.action),
        resource_type: this.getResourceTypeText(log.resource_type),
        resource_id: log.resource_id || '-',
        status: log.status === 'success' ? '成功' : '失败',
        ip_address: log.ip_address || '-',
        created_at: this.formatDateTime(log.created_at),
        details: this.formatLogDetails(log.details)
      });

      // 设置数据行样式
      row.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        // 状态列着色
        if (colNumber === 6) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: log.status === 'success' ? 'FFD4F4DD' : 'FFFFD4D4' }
          };
        }
      });
    });

    // 添加标题
    worksheet.insertRow(1, [title]);
    worksheet.mergeCells('A1:I1');
    const titleCell = worksheet.getCell('A1');
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD0D0D0' }
    };

    // 添加空行
    worksheet.insertRow(2, []);

    // 调整行高
    worksheet.getRow(1).height = 25;
    worksheet.getRow(3).height = 20;

    // 返回缓冲区
    return await workbook.xlsx.writeBuffer();
  }

  // 辅助方法
  static getStatusText(status) {
    const statusMap = {
      'pending': '待县局审核',
      'pending_city_review': '待市局审核',
      'pending_contract_number': '待分配合同编号',
      'approved': '已通过',
      'completed': '已完成',
      'rejected': '已拒绝'
    };
    return statusMap[status] || status;
  }

  static getStatusColor(status) {
    const colorMap = {
      'pending': 'FFFFEAA7',
      'pending_city_review': 'FFFFEAA7',
      'pending_contract_number': 'FF74B9FF',
      'approved': 'FF00B894',
      'completed': 'FF00B894',
      'rejected': 'FFE84393'
    };
    return colorMap[status] || 'FFFFFFFF';
  }

  static getRoleText(role) {
    const roleMap = {
      'admin': '管理员',
      'reviewer': '审核员',
      'employee': '员工'
    };
    return roleMap[role] || role;
  }

  static getUserStatusText(status) {
    const statusMap = {
      'active': '正常',
      'banned': '封禁'
    };
    return statusMap[status] || status;
  }

  static getUserStatusColor(status) {
    const colorMap = {
      'active': 'FF00B894',
      'banned': 'FFE84393'
    };
    return colorMap[status] || 'FFFFFFFF';
  }

  static getActionText(action) {
    const actionMap = {
      'login': '登录',
      'logout': '登出',
      'submit_contract': '提交合同',
      'update_contract': '更新合同',
      'delete_contract': '删除合同',
      'start_review': '开始审核',
      'submit_review': '提交审核',
      'create_user': '创建用户',
      'update_user': '更新用户',
      'delete_user': '删除用户',
      'reset_password': '重置密码',
      'upload_file': '上传文件',
      'download_file': '下载文件',
      'delete_file': '删除文件'
    };
    return actionMap[action] || action;
  }

  static getResourceTypeText(resourceType) {
    const typeMap = {
      'user': '用户',
      'contract': '合同',
      'file': '文件',
      'system': '系统'
    };
    return typeMap[resourceType] || resourceType;
  }

  static formatFileSize(bytes) {
    if (!bytes) return '-';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  static formatDateTime(dateTime) {
    if (!dateTime) return '-';
    const DateTimeUtils = require('./dateTime');
    return DateTimeUtils.formatForExport(dateTime);
  }

  static formatLogDetails(details) {
    if (!details) return '-';
    try {
      const parsed = JSON.parse(details);
      return Object.entries(parsed).map(([key, value]) => `${key}: ${value}`).join('\n');
    } catch (error) {
      return details;
    }
  }
}

/**
 * PDF导出服务
 */
class PDFExportService {
  /**
   * 导出合同数据为PDF
   * @param {Array} contracts - 合同数据
   * @param {Object} options - 导出选项
   * @returns {Promise<Buffer>} PDF文件缓冲区
   */
  static async exportContracts(contracts, options = {}) {
    const { title = '合同数据导出报告' } = options;

    const doc = new jsPDF();

    // 设置中文字体（如果需要）
    doc.setFont('helvetica');

    // 标题
    doc.setFontSize(18);
    doc.text(title, 20, 20);

    // 生成日期
    doc.setFontSize(12);
    doc.text(`Generated: ${new Date().toLocaleString()}`, 20, 30);

    // 统计信息
    const stats = this.calculateContractStats(contracts);
    doc.setFontSize(14);
    doc.text('Statistics:', 20, 45);
    doc.setFontSize(12);
    doc.text(`Total Contracts: ${stats.total}`, 25, 55);
    doc.text(`Pending: ${stats.pending}`, 25, 65);
    doc.text(`Approved: ${stats.approved}`, 25, 75);
    doc.text(`Completed: ${stats.completed}`, 25, 85);
    doc.text(`Rejected: ${stats.rejected}`, 25, 95);

    // 合同列表
    doc.setFontSize(14);
    doc.text('Contract List:', 20, 110);

    let yPosition = 120;
    contracts.forEach((contract, index) => {
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
      }

      doc.setFontSize(10);
      doc.text(`${index + 1}. ${contract.serial_number} - ${contract.filename}`, 25, yPosition);
      yPosition += 10;
      doc.text(`   Submitter: ${contract.submitter_name} | Status: ${contract.status}`, 25, yPosition);
      yPosition += 10;
      doc.text(`   Submitted: ${this.formatDateTime(contract.submitted_at)}`, 25, yPosition);
      yPosition += 15;
    });

    // 返回缓冲区
    return Buffer.from(doc.output('arraybuffer'));
  }

  /**
   * 导出用户数据为PDF
   * @param {Array} users - 用户数据
   * @param {Object} options - 导出选项
   * @returns {Promise<Buffer>} PDF文件缓冲区
   */
  static async exportUsers(users, options = {}) {
    const { title = '用户数据导出报告' } = options;

    const doc = new jsPDF();

    // 标题
    doc.setFontSize(18);
    doc.text(title, 20, 20);

    // 生成日期
    doc.setFontSize(12);
    doc.text(`Generated: ${new Date().toLocaleString()}`, 20, 30);

    // 统计信息
    const stats = this.calculateUserStats(users);
    doc.setFontSize(14);
    doc.text('Statistics:', 20, 45);
    doc.setFontSize(12);
    doc.text(`Total Users: ${stats.total}`, 25, 55);
    doc.text(`Active: ${stats.active}`, 25, 65);
    doc.text(`Banned: ${stats.banned}`, 25, 75);
    doc.text(`Admins: ${stats.admins}`, 25, 85);
    doc.text(`Reviewers: ${stats.reviewers}`, 25, 95);
    doc.text(`Employees: ${stats.employees}`, 25, 105);

    // 用户列表
    doc.setFontSize(14);
    doc.text('User List:', 20, 120);

    let yPosition = 130;
    users.forEach((user, index) => {
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
      }

      doc.setFontSize(10);
      doc.text(`${index + 1}. ${user.username} (${user.role})`, 25, yPosition);
      yPosition += 10;
      doc.text(`   Status: ${user.status} | Created: ${this.formatDateTime(user.created_at)}`, 25, yPosition);
      yPosition += 15;
    });

    // 返回缓冲区
    return Buffer.from(doc.output('arraybuffer'));
  }

  // 辅助方法
  static calculateContractStats(contracts) {
    const stats = {
      total: contracts.length,
      pending: 0,
      approved: 0,
      rejected: 0
    };

    contracts.forEach(contract => {
      stats[contract.status] = (stats[contract.status] || 0) + 1;
    });

    return stats;
  }

  static calculateUserStats(users) {
    const stats = {
      total: users.length,
      active: 0,
      banned: 0,
      admins: 0,
      reviewers: 0,
      employees: 0
    };

    users.forEach(user => {
      stats[user.status] = (stats[user.status] || 0) + 1;
      stats[user.role + 's'] = (stats[user.role + 's'] || 0) + 1;
    });

    return stats;
  }

  static formatDateTime(dateTime) {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleString('zh-CN');
  }
}

module.exports = {
  ExcelExportService,
  PDFExportService
};