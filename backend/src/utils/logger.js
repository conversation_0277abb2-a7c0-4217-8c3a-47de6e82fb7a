/**
 * 操作日志记录工具
 * 记录用户在系统中的所有重要操作
 */

const { database } = require('./database');
const DateTimeUtils = require('./dateTime');

/**
 * 记录操作日志
 * @param {Object} logData - 日志数据
 * @param {number} logData.userId - 用户ID
 * @param {string} logData.action - 操作类型
 * @param {string} logData.resourceType - 资源类型
 * @param {string} [logData.resourceId] - 资源ID
 * @param {string} [logData.details] - 详细信息
 * @param {string} [logData.ipAddress] - IP地址
 * @param {string} [logData.userAgent] - 用户代理
 * @param {string} [logData.status] - 操作状态 (success/failed)
 */
async function logOperation(logData) {
  try {
    const {
      userId,
      action,
      resourceType,
      resourceId = null,
      details = null,
      ipAddress = null,
      userAgent = null,
      status = 'success'
    } = logData;

    // 使用UTC时间创建操作日志
    const now = DateTimeUtils.nowUTC();

    await database.run(`
      INSERT INTO operation_logs
      (user_id, action, resource_type, resource_id, details, ip_address, user_agent, status, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [userId, action, resourceType, resourceId, details, ipAddress, userAgent, status, now]);

  } catch (error) {
    console.error('❌ 操作日志记录失败:', error.message);
  }
}

/**
 * 获取操作日志列表
 * @param {Object} options - 查询选项
 * @param {number} [options.page=1] - 页码
 * @param {number} [options.limit=20] - 每页条数
 * @param {number} [options.userId] - 用户ID筛选
 * @param {string} [options.action] - 操作类型筛选
 * @param {string} [options.resourceType] - 资源类型筛选
 * @param {string} [options.status] - 操作状态筛选
 * @param {string} [options.startDate] - 开始日期
 * @param {string} [options.endDate] - 结束日期
 * @returns {Promise<Object>} 日志列表和总数
 */
async function getOperationLogs(options = {}) {
  const {
    page = 1,
    limit = 20,
    userId,
    action,
    resourceType,
    status,
    startDate,
    endDate
  } = options;

  const offset = (page - 1) * limit;
  let whereConditions = [];
  let params = [];

  // 构建查询条件
  if (userId) {
    whereConditions.push('ol.user_id = ?');
    params.push(userId);
  }

  if (action) {
    whereConditions.push('ol.action = ?');
    params.push(action);
  }

  if (resourceType) {
    whereConditions.push('ol.resource_type = ?');
    params.push(resourceType);
  }

  if (status) {
    whereConditions.push('ol.status = ?');
    params.push(status);
  }

  if (startDate) {
    whereConditions.push('ol.created_at >= ?');
    params.push(startDate);
  }

  if (endDate) {
    whereConditions.push('ol.created_at <= ?');
    params.push(endDate);
  }

  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

  // 查询总数
  const countSql = `
    SELECT COUNT(*) as total
    FROM operation_logs ol
    ${whereClause}
  `;

  const countResult = await database.get(countSql, params);
  const total = countResult.total;

  // 查询日志列表
  const listSql = `
    SELECT
      ol.id,
      ol.user_id,
      u.username,
      u.role,
      ol.action,
      ol.resource_type,
      ol.resource_id,
      ol.details,
      ol.ip_address,
      ol.user_agent,
      ol.status,
      ol.created_at
    FROM operation_logs ol
    LEFT JOIN users u ON ol.user_id = u.id
    ${whereClause}
    ORDER BY ol.created_at DESC
    LIMIT ? OFFSET ?
  `;

  const listParams = [...params, limit, offset];
  const logs = await database.all(listSql, listParams);

  return {
    logs,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit)
  };
}

/**
 * 获取操作统计数据
 * @param {Object} options - 统计选项
 * @param {string} [options.period='7d'] - 统计周期 (1d/7d/30d)
 * @param {number} [options.userId] - 用户ID筛选
 * @returns {Promise<Object>} 统计数据
 */
async function getOperationStats(options = {}) {
  const { period = '7d', userId } = options;

  let dateCondition = '';
  switch (period) {
  case '1d':
    dateCondition = 'DATE(created_at) = DATE(\'now\')';
    break;
  case '7d':
    dateCondition = 'created_at >= DATE(\'now\', \'-7 days\')';
    break;
  case '30d':
    dateCondition = 'created_at >= DATE(\'now\', \'-30 days\')';
    break;
  default:
    dateCondition = 'created_at >= DATE(\'now\', \'-7 days\')';
  }

  let userCondition = '';
  let params = [];
  if (userId) {
    userCondition = 'AND user_id = ?';
    params.push(userId);
  }

  // 操作类型统计
  const actionStats = await database.all(`
    SELECT action, COUNT(*) as count
    FROM operation_logs
    WHERE ${dateCondition} ${userCondition}
    GROUP BY action
    ORDER BY count DESC
  `, params);

  // 资源类型统计
  const resourceStats = await database.all(`
    SELECT resource_type, COUNT(*) as count
    FROM operation_logs
    WHERE ${dateCondition} ${userCondition}
    GROUP BY resource_type
    ORDER BY count DESC
  `, params);

  // 操作状态统计
  const statusStats = await database.all(`
    SELECT status, COUNT(*) as count
    FROM operation_logs
    WHERE ${dateCondition} ${userCondition}
    GROUP BY status
  `, params);

  // 每日操作统计
  const dailyStats = await database.all(`
    SELECT DATE(created_at) as date, COUNT(*) as count
    FROM operation_logs
    WHERE ${dateCondition} ${userCondition}
    GROUP BY DATE(created_at)
    ORDER BY date DESC
  `, params);

  return {
    actionStats,
    resourceStats,
    statusStats,
    dailyStats
  };
}

/**
 * 删除过期日志
 * @param {number} days - 保留天数
 * @returns {Promise<number>} 删除的记录数
 */
async function cleanupOldLogs(days = 90) {
  try {
    const result = await database.run(`
      DELETE FROM operation_logs
      WHERE created_at < DATE('now', '-${days} days')
    `);

    return result.changes;
  } catch (error) {
    console.error('❌ 清理过期日志失败:', error.message);
    return 0;
  }
}

// 操作类型常量
const ACTIONS = {
  // 认证相关
  LOGIN: 'login',
  LOGOUT: 'logout',
  CHANGE_PASSWORD: 'change_password',

  // 合同相关
  SUBMIT_CONTRACT: 'submit_contract',
  UPDATE_CONTRACT: 'update_contract',
  DELETE_CONTRACT: 'delete_contract',
  START_REVIEW: 'start_review',
  SUBMIT_REVIEW: 'submit_review',

  // 用户管理
  CREATE_USER: 'create_user',
  UPDATE_USER: 'update_user',
  DELETE_USER: 'delete_user',
  RESET_PASSWORD: 'reset_password',

  // 文件操作
  UPLOAD_FILE: 'upload_file',
  DOWNLOAD_FILE: 'download_file',
  DELETE_FILE: 'delete_file',

  // 系统管理
  SYSTEM_CONFIG: 'system_config',
  EXPORT_DATA: 'export_data',
  IMPORT_DATA: 'import_data'
};

// 资源类型常量
const RESOURCE_TYPES = {
  USER: 'user',
  CONTRACT: 'contract',
  FILE: 'file',
  SYSTEM: 'system'
};

module.exports = {
  logOperation,
  getOperationLogs,
  getOperationStats,
  cleanupOldLogs,
  ACTIONS,
  RESOURCE_TYPES
};