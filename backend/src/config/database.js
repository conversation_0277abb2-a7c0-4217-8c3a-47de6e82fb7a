/**
 * 统一数据库配置模块
 * 提供项目中所有数据库操作的统一配置
 */

const path = require('path');

/**
 * 获取数据库文件路径
 * 支持环境变量配置，默认使用 backend/database.sqlite
 */
const getDatabasePath = () => {
  // 优先使用环境变量
  if (process.env.DB_PATH) {
    // 如果是相对路径，相对于backend目录
    if (!path.isAbsolute(process.env.DB_PATH)) {
      return path.resolve(__dirname, '../..', process.env.DB_PATH);
    }
    return process.env.DB_PATH;
  }

  // 默认路径：backend/database.sqlite
  return path.resolve(__dirname, '../..', 'database.sqlite');
};

/**
 * 数据库配置对象
 */
const DATABASE_CONFIG = {
  // 数据库文件路径
  path: getDatabasePath(),
  
  // SQLite 配置选项
  options: {
    // 启用外键约束
    foreignKeys: true,
    // 连接超时时间（毫秒）
    timeout: 10000,
    // 启用WAL模式以提高并发性能
    walMode: true
  },

  // 获取数据库路径的方法（向后兼容）
  getPath: getDatabasePath
};

module.exports = DATABASE_CONFIG;
