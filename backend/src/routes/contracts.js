/**
 * 合同相关路由
 * 处理合同提交、查询、修改、审核等功能
 */

const express = require('express');
const { body, validationResult, query } = require('express-validator');
const router = express.Router();

const { database, ContractModel, UserModel, ContractReviewModel } = require('../utils/database');
const { authenticateToken } = require('../middleware/auth');
const { PermissionMiddleware, RoleMiddleware } = require('../middleware/rbac');
const { ResponseUtils, BusinessUtils, DateUtils, DateTimeUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES, CONTRACT_STATUS, PERMISSIONS, USER_ROLES, PAGINATION } = require('../utils/constants');
const NotificationService = require('../utils/notification');
// 缓存相关导入已移除，系统直接从数据库获取数据

/**
 * 根据审核级别自动分配审核员
 * @param {string} reviewLevel - 审核级别 ('county_reviewer' 或 'city_reviewer')
 * @param {number} submitterId - 提交者ID（排除自己）
 * @returns {Object|null} 分配的审核员信息
 */
async function assignReviewerByLevel(reviewLevel, submitterId) {
  try {
    // 获取指定级别的所有活跃审核员
    const reviewers = await UserModel.getList({
      role: [reviewLevel, USER_ROLES.ADMIN], // 包括管理员
      status: 'active'
    }, 1, 100);

    // 过滤掉提交者自己
    const availableReviewers = reviewers.data.filter(reviewer => reviewer.id !== submitterId);

    if (availableReviewers.length === 0) {
      return null;
    }

    // 简单的轮询分配策略：选择第一个可用的审核员
    // 在实际生产环境中，可以实现更复杂的负载均衡策略
    return availableReviewers[0];
  } catch (error) {
    console.error('自动分配审核员失败:', error);
    return null;
  }
}

/**
 * 获取合同统计信息
 * GET /api/contracts/stats
 */
router.get('/stats',
  authenticateToken,
  async (req, res) => {
    try {
      // 获取合同总数
      const totalResult = await database.get('SELECT COUNT(*) as total FROM contracts');
      const total = totalResult.total || 0;

      // 获取各状态的合同数量
      const statusStats = await database.all(`
        SELECT status, COUNT(*) as count
        FROM contracts
        GROUP BY status
      `);

      // 获取最近创建的合同数量（最近7天）
      const recentResult = await database.get(`
        SELECT COUNT(*) as recent
        FROM contracts
        WHERE created_at >= datetime('now', '-7 days')
      `);
      const recent = recentResult.recent || 0;

      res.json(ResponseUtils.success({
        total,
        recent,
        statusBreakdown: statusStats.reduce((acc, item) => {
          acc[item.status] = item.count;
          return acc;
        }, {}),
        lastUpdated: new Date().toISOString()
      }, '获取统计信息成功'));

    } catch (error) {
      console.error('获取合同统计信息错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 提交合同
 * POST /api/contracts
 */
router.post('/',
  authenticateToken,
  PermissionMiddleware.canCreateContract,
  [
    body('filename')
      .notEmpty()
      .withMessage('文件名不能为空'),
    body('file_path')
      .notEmpty()
      .withMessage('文件路径不能为空'),
    body('file_size')
      .isInt({ min: 1 })
      .withMessage('文件大小必须大于0'),
    body('review_level')
      .isIn(['county_reviewer', 'city_reviewer'])
      .withMessage('请选择有效的审核级别'),
    body('reviewer_id')
      .isInt({ min: 1 })
      .withMessage('请选择审核员'),
    body('submit_note')
      .optional()
      .isLength({ max: 500 })
      .withMessage('提交说明不能超过500字符')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { filename, file_path, file_size, review_level, reviewer_id, submit_note } = req.body;

      // 验证选择的审核员是否存在且有效
      const reviewer = await UserModel.findById(reviewer_id);
      if (!reviewer) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('选择的审核员不存在', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 验证审核员状态是否激活
      if (reviewer.status !== 'active') {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('选择的审核员已被禁用', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 验证审核员角色是否匹配审核级别
      const validRoles = review_level === 'county_reviewer'
        ? [USER_ROLES.COUNTY_REVIEWER, USER_ROLES.ADMIN]
        : [USER_ROLES.CITY_REVIEWER, USER_ROLES.ADMIN];

      if (!validRoles.includes(reviewer.role)) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('选择的审核员级别与审核级别不匹配', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 生成流水号
      const serialNumber = await ContractModel.generateSerialNumber();

      // 创建合同记录 - 设置为待审核状态
      const contractData = {
        serial_number: serialNumber,
        submitter_id: req.user.id,
        reviewer_id: reviewer.id,
        review_level, // 保存审核级别
        filename,
        file_path,
        file_size,
        submit_note: submit_note || null,
        status: CONTRACT_STATUS.PENDING // 设置为待审核状态
      };

      const result = await ContractModel.create(contractData);

      // 获取完整的合同信息
      const contract = await ContractModel.findById(result.id);

      // 记录合同提交日志
      try {
        const { logOperation } = require('../utils/logger');

        await logOperation({
          userId: req.user.id,
          action: 'contract_submit',
          resourceType: 'contract',
          resourceId: contract.id.toString(),
          details: `提交合同 ${contract.serial_number}，审核级别：${review_level}，分配给审核员：${reviewer.username}`,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          status: 'success'
        });
      } catch (logError) {
        console.error('记录提交日志失败:', logError);
        // 日志记录失败不影响主要业务流程
      }

      // 发送系统内通知给审核员
      try {
        const submitter = await UserModel.findById(req.user.id);

        const contractDataForNotification = {
          id: contract.id,
          serial_number: contract.serial_number,
          submitter_name: submitter.username,
          filename: contract.filename
        };

        await NotificationService.notifyContractSubmitted(contractDataForNotification, reviewer.id);
      } catch (notificationError) {
        console.error('发送通知失败:', notificationError);
        // 通知发送失败不影响主要业务流程
      }

      // 缓存系统已移除，记录合同创建日志

      res.status(HTTP_STATUS.CREATED).json(
        ResponseUtils.success(contract, RESPONSE_MESSAGES.SUBMIT_SUCCESS)
      );

    } catch (error) {
      console.error('合同提交错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取合同列表
 * GET /api/contracts
 */
router.get('/',
  authenticateToken,
  PermissionMiddleware.canReadContract,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),
    query('status')
      .optional()
      .isIn(Object.values(CONTRACT_STATUS))
      .withMessage('无效的状态值'),
    query('submitter_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('提交人ID必须是正整数'),
    query('reviewer_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('审核员ID必须是正整数')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { page = 1, pageSize = 10, status, submitter_id, reviewer_id } = req.query;

      // 构建过滤条件
      const filters = {};

      // 根据用户角色过滤数据
      if (req.user.role === USER_ROLES.EMPLOYEE) {
        // 员工只能查看自己提交的合同
        filters.submitter_id = req.user.id;
      } else if ([USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(req.user.role)) {
        // 审核员可以查看：1. 当前分配给自己的合同 2. 自己曾经审核过的合同
        filters.reviewer_related = req.user.id;
      } else if (req.user.role === USER_ROLES.LEGAL_OFFICER) {
        // 法规员可以查看：1. 待分配编号的合同 2. 已完成的合同（已分配编号）
        filters.legal_officer_accessible = true;
      }
      // 管理员可以查看所有合同

      // 应用查询参数（只有管理员可以指定其他用户）
      if (status) filters.status = status;
      if (submitter_id && req.user.role === USER_ROLES.ADMIN) {
        filters.submitter_id = submitter_id;
      }
      if (reviewer_id && req.user.role === USER_ROLES.ADMIN) {
        filters.reviewer_id = reviewer_id;
      }

      // 获取合同列表
      const result = await ContractModel.getList(filters, parseInt(page), parseInt(pageSize));

      // 为每个合同添加权限标志
      const contractsWithPermissions = result.data.map(contract => ({
        ...contract,
        permissions: {
          canModify: BusinessUtils.canModifyContract(contract, req.user),
          canReview: BusinessUtils.canReviewContract(contract, req.user),
          canView: true, // 已经通过权限检查
          canDelete: req.user.role === USER_ROLES.ADMIN ||
            (req.user.role === USER_ROLES.EMPLOYEE &&
              contract.submitter_id === req.user.id &&
              contract.status === CONTRACT_STATUS.PENDING)
        }
      }));

      // 构建响应数据
      const responseData = ResponseUtils.paginated(contractsWithPermissions, result.pagination);

      // 将响应体存储到 res.locals 供缓存中间件使用
      res.locals.responseBody = responseData;

      res.json(responseData);

    } catch (error) {
      console.error('获取合同列表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取合同统计信息
 * GET /api/contracts/stats
 */
router.get('/stats',
  authenticateToken,
  PermissionMiddleware.canReadContract,
  async (req, res) => {
    try {
      console.log('📊 获取合同统计信息:', {
        userId: req.user.id,
        username: req.user.username,
        role: req.user.role
      });

      // 构建过滤条件
      const filters = {};

      // 根据用户角色获取统计数据
      let stats;
      if (req.user.role === USER_ROLES.EMPLOYEE) {
        // 员工查看自己提交的合同统计
        filters.submitter_id = req.user.id;
        stats = await ContractModel.getStats(filters);
      } else if ([USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(req.user.role)) {
        // 审核员使用基于审核历史的统计方法
        stats = await ContractModel.getReviewerStats(req.user.id);
      } else if (req.user.role === USER_ROLES.LEGAL_OFFICER) {
        // 法规员查看可访问的合同统计
        filters.legal_officer_accessible = true;
        stats = await ContractModel.getStats(filters);
      } else {
        // 管理员或其他角色使用通用统计
        stats = await ContractModel.getStats(filters);
      }


      res.json(ResponseUtils.success(stats));

    } catch (error) {
      console.error('获取合同统计错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取待审核合同列表（审核员专用）
 * GET /api/contracts/pending
 */
router.get('/pending',
  authenticateToken,
  RoleMiddleware.requireAnyReviewer,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      // 只有审核员和管理员可以访问此端点
      if (![USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER, USER_ROLES.ADMIN].includes(req.user.role)) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('只有审核员可以访问此端点', HTTP_STATUS.FORBIDDEN)
        );
      }

      const { page = 1, pageSize = 10 } = req.query;

      // 构建过滤条件 - 根据用户角色查询相应的待审核合同
      const filters = {};

      if (req.user.role === USER_ROLES.CITY_REVIEWER) {
        // 市局审核员查看待市局审核的合同
        filters.status = CONTRACT_STATUS.PENDING_CITY_REVIEW;
        filters.reviewer_id = req.user.id;
      } else if (req.user.role === USER_ROLES.COUNTY_REVIEWER) {
        // 县局审核员查看待审核的合同
        filters.status = CONTRACT_STATUS.PENDING;
        filters.reviewer_id = req.user.id;
      } else if (req.user.role === USER_ROLES.ADMIN) {
        // 管理员可以查看所有待审核的合同（包括县局和市局）
        filters.status = [CONTRACT_STATUS.PENDING, CONTRACT_STATUS.PENDING_CITY_REVIEW];
      } else {
        // 其他角色只能查看普通待审核合同
        filters.status = CONTRACT_STATUS.PENDING;
        filters.reviewer_id = req.user.id;
      }

      console.log('📋 获取待审核合同列表:', {
        userId: req.user.id,
        username: req.user.username,
        role: req.user.role,
        filters,
        pagination: { page, pageSize }
      });

      // 获取合同列表
      const result = await ContractModel.getList(filters, parseInt(page), parseInt(pageSize));

      // 为每个合同添加权限标志
      const contractsWithPermissions = result.data.map(contract => ({
        ...contract,
        permissions: {
          canModify: BusinessUtils.canModifyContract(contract, req.user),
          canReview: BusinessUtils.canReviewContract(contract, req.user),
          canView: true, // 已经通过权限检查
          canDelete: req.user.role === USER_ROLES.ADMIN ||
            (req.user.role === USER_ROLES.EMPLOYEE &&
              contract.submitter_id === req.user.id &&
              contract.status === CONTRACT_STATUS.PENDING)
        }
      }));

      // 构建响应数据
      const responseData = ResponseUtils.paginated(contractsWithPermissions, result.pagination);

      // 将响应体存储到 res.locals 供缓存中间件使用
      res.locals.responseBody = responseData;

      res.json(responseData);

    } catch (error) {
      console.error('获取待审核合同列表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取已审核合同列表（审核员专用）
 * GET /api/contracts/reviewed
 */
router.get('/reviewed',
  authenticateToken,
  RoleMiddleware.requireAnyReviewer,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      // 只有审核员和管理员可以访问此端点
      if (![USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER, USER_ROLES.ADMIN].includes(req.user.role)) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('只有审核员可以访问此端点', HTTP_STATUS.FORBIDDEN)
        );
      }

      const { page = 1, pageSize = 10 } = req.query;

      // 构建过滤条件 - 查询审核员曾经审核过的合同
      const filters = {};

      // 审核员只能查看自己审核过的合同，管理员可以查看所有
      if ([USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(req.user.role)) {
        // 查询审核历史表中该审核员参与过的合同
        filters.reviewed_by = req.user.id;
      } else {
        // 管理员查看所有已审核的合同
        filters.status = [CONTRACT_STATUS.APPROVED, CONTRACT_STATUS.REJECTED, CONTRACT_STATUS.COMPLETED];
      }

      console.log('📋 获取已审核合同列表:', {
        userId: req.user.id,
        username: req.user.username,
        role: req.user.role,
        filters,
        pagination: { page, pageSize }
      });

      // 获取合同列表
      const result = await ContractModel.getList(filters, parseInt(page), parseInt(pageSize));

      // 为每个合同添加权限标志
      const contractsWithPermissions = result.data.map(contract => ({
        ...contract,
        permissions: {
          canModify: BusinessUtils.canModifyContract(contract, req.user),
          canReview: BusinessUtils.canReviewContract(contract, req.user),
          canView: true, // 已经通过权限检查
          canDelete: req.user.role === USER_ROLES.ADMIN ||
            (req.user.role === USER_ROLES.EMPLOYEE &&
              contract.submitter_id === req.user.id &&
              contract.status === CONTRACT_STATUS.PENDING)
        }
      }));

      // 构建响应数据
      const responseData = ResponseUtils.paginated(contractsWithPermissions, result.pagination);

      // 将响应体存储到 res.locals 供缓存中间件使用
      res.locals.responseBody = responseData;

      res.json(responseData);

    } catch (error) {
      console.error('获取已审核合同列表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取审核员列表
 * GET /api/contracts/reviewers
 */
router.get('/reviewers',
  authenticateToken,
  // 员工和管理员都可以访问（管理员修改合同时需要选择审核员）
  (req, res, next) => {
    if (![USER_ROLES.EMPLOYEE, USER_ROLES.ADMIN].includes(req.user.role)) {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('角色权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }
    next();
  },
  async (req, res) => {
    try {
      // 获取级别过滤参数 - 支持多种参数格式
      let level = req.query.level;
      if (!level && req.query.params && req.query.params.level) {
        level = req.query.params.level;
      }

      console.log('👥 获取审核员列表:', {
        userId: req.user.id,
        username: req.user.username,
        role: req.user.role,
        requestedLevel: level,
        rawQuery: req.query
      });

      let roleFilter;
      if (level === 'county_reviewer') {
        // 只返回县级审核员，不包含管理员和市级审核员
        roleFilter = [USER_ROLES.COUNTY_REVIEWER];
      } else if (level === 'city_reviewer') {
        // 只返回市级审核员，不包含管理员和县级审核员
        roleFilter = [USER_ROLES.CITY_REVIEWER];
      } else {
        // 如果没有指定级别，返回所有审核员和管理员
        roleFilter = [USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER, USER_ROLES.ADMIN];
      }


      // 获取指定级别的审核员
      const reviewers = await UserModel.getList({
        role: roleFilter,
        status: 'active'
      }, 1, 100);

      // 过滤掉当前用户（不能选择自己作为审核员）
      let filteredReviewers = reviewers.data.filter(user => user.id !== req.user.id);

      // 根据级别进行二次过滤，确保角色匹配
      if (level === 'county_reviewer') {
        filteredReviewers = filteredReviewers.filter(user =>
          user.role === USER_ROLES.COUNTY_REVIEWER
        );
      } else if (level === 'city_reviewer') {
        filteredReviewers = filteredReviewers.filter(user =>
          user.role === USER_ROLES.CITY_REVIEWER
        );
      }

      console.log('👥 审核员列表过滤结果:', {
        totalFound: reviewers.data.length,
        afterFiltering: filteredReviewers.length,
        reviewers: filteredReviewers.map(r => ({ id: r.id, username: r.username, role: r.role }))
      });

      res.json(ResponseUtils.success(filteredReviewers));

    } catch (error) {
      console.error('获取审核员列表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取我的合同列表（员工专用）
 * GET /api/contracts/my
 */
router.get('/my',
  authenticateToken,
  PermissionMiddleware.canReadContract,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),
    query('status')
      .optional()
      .isIn(Object.values(CONTRACT_STATUS))
      .withMessage('无效的状态值')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { page = 1, pageSize = 10, status } = req.query;

      // 构建过滤条件 - 只查询当前用户提交的合同
      const filters = {
        submitter_id: req.user.id
      };

      // 应用状态过滤
      if (status) {
        filters.status = status;
      }

      console.log('📋 获取合同列表:', {
        userId: req.user.id,
        username: req.user.username,
        role: req.user.role,
        filters,
        pagination: { page, pageSize }
      });

      // 获取合同列表
      const result = await ContractModel.getList(filters, parseInt(page), parseInt(pageSize));

      console.log('📊 合同列表查询结果:', {
        count: result.data.length,
        total: result.pagination.total,
        page: result.pagination.page
      });

      // 为每个合同添加权限标志
      const contractsWithPermissions = result.data.map(contract => ({
        ...contract,
        permissions: {
          canModify: BusinessUtils.canModifyContract(contract, req.user),
          canReview: BusinessUtils.canReviewContract(contract, req.user),
          canView: true, // 已经通过权限检查
          canDelete: req.user.role === USER_ROLES.ADMIN ||
            (req.user.role === USER_ROLES.EMPLOYEE &&
              contract.submitter_id === req.user.id &&
              contract.status === CONTRACT_STATUS.PENDING)
        }
      }));

      res.json(ResponseUtils.paginated(contractsWithPermissions, result.pagination));

    } catch (error) {
      console.error('获取我的合同列表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取待分配编号的合同列表
 * GET /api/contracts/pending-number
 */
router.get('/pending-number',
  authenticateToken,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      // 检查用户权限
      if (req.user.role !== USER_ROLES.LEGAL_OFFICER && req.user.role !== USER_ROLES.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('只有法规员可以查看待分配编号的合同', HTTP_STATUS.FORBIDDEN)
        );
      }

      const page = parseInt(req.query.page) || PAGINATION.DEFAULT_PAGE;
      const pageSize = Math.min(parseInt(req.query.pageSize) || PAGINATION.DEFAULT_PAGE_SIZE, PAGINATION.MAX_PAGE_SIZE);

      console.log('📋 获取待分配编号合同列表:', {
        userId: req.user.id,
        username: req.user.username,
        role: req.user.role,
        pagination: { page, pageSize }
      });

      // 获取待分配编号的合同
      const result = await ContractModel.findPendingContractNumber({
        page,
        pageSize,
        legal_officer_id: req.user.role === USER_ROLES.LEGAL_OFFICER ? req.user.id : null
      });

      console.log('📊 待分配编号合同查询结果:', {
        count: result.contracts.length,
        total: result.total,
        page: result.page
      });

      res.json(ResponseUtils.success(result, '获取待分配编号合同列表成功'));

    } catch (error) {
      console.error('获取待分配编号合同列表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取合同详情
 * GET /api/contracts/:id
 */
router.get('/:id',
  authenticateToken,
  PermissionMiddleware.canReadContract,
  async (req, res) => {
    try {
      const contractId = parseInt(req.params.id);

      if (!contractId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的合同ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 获取合同信息
      const contract = await ContractModel.findById(contractId);

      if (!contract) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error(RESPONSE_MESSAGES.CONTRACT_NOT_FOUND, HTTP_STATUS.NOT_FOUND)
        );
      }

      // 检查访问权限
      const canView = await BusinessUtils.canViewContract(contract, req.user);
      if (!canView) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error(RESPONSE_MESSAGES.FORBIDDEN, HTTP_STATUS.FORBIDDEN)
        );
      }

      // 获取审核历史
      const reviewHistory = await ContractReviewModel.findByContractId(contractId);

      // 添加权限标志和审核历史
      const contractWithPermissions = {
        ...contract,
        reviewHistory: reviewHistory,
        permissions: {
          canModify: BusinessUtils.canModifyContract(contract, req.user),
          canReview: BusinessUtils.canReviewContract(contract, req.user),
          canView: true, // 已经通过权限检查
          canDelete: req.user.role === USER_ROLES.ADMIN ||
            (req.user.role === USER_ROLES.EMPLOYEE &&
              contract.submitter_id === req.user.id &&
              contract.status === CONTRACT_STATUS.PENDING)
        }
      };

      res.json(ResponseUtils.success(contractWithPermissions));

    } catch (error) {
      console.error('获取合同详情错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 修改合同
 * PUT /api/contracts/:id
 */
router.put('/:id',
  authenticateToken,
  PermissionMiddleware.canUpdateContract,
  [
    body('filename')
      .optional()
      .notEmpty()
      .withMessage('文件名不能为空'),
    body('file_path')
      .optional()
      .notEmpty()
      .withMessage('文件路径不能为空'),
    body('file_size')
      .optional()
      .isInt({ min: 1 })
      .withMessage('文件大小必须大于0'),
    body('reviewer_id')
      .optional()
      .isInt({ min: 1 })
      .withMessage('请选择审核员'),
    body('submit_note')
      .optional()
      .isLength({ max: 500 })
      .withMessage('提交说明不能超过500字符')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const contractId = parseInt(req.params.id);
      const { filename, file_path, file_size, reviewer_id, submit_note } = req.body;

      if (!contractId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的合同ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 获取合同信息
      const contract = await ContractModel.findById(contractId);

      if (!contract) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error(RESPONSE_MESSAGES.CONTRACT_NOT_FOUND, HTTP_STATUS.NOT_FOUND)
        );
      }

      // 检查修改权限
      const canModify = BusinessUtils.canModifyContract(contract, req.user);
      if (!canModify) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error(RESPONSE_MESSAGES.CANNOT_MODIFY_CONTRACT, HTTP_STATUS.FORBIDDEN)
        );
      }

      // 如果修改了审核员，需要验证
      if (reviewer_id && reviewer_id !== contract.reviewer_id) {
        const reviewer = await UserModel.findById(reviewer_id);
        if (!reviewer) {
          return res.status(HTTP_STATUS.BAD_REQUEST).json(
            ResponseUtils.error('指定的审核员不存在', HTTP_STATUS.BAD_REQUEST)
          );
        }

        if (![USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER, USER_ROLES.ADMIN].includes(reviewer.role)) {
          return res.status(HTTP_STATUS.BAD_REQUEST).json(
            ResponseUtils.error('指定的用户不是审核员', HTTP_STATUS.BAD_REQUEST)
          );
        }

        if (reviewer_id === req.user.id) {
          return res.status(HTTP_STATUS.BAD_REQUEST).json(
            ResponseUtils.error('不能选择自己作为审核员', HTTP_STATUS.BAD_REQUEST)
          );
        }
      }

      // 构建更新数据
      const updateData = {};
      if (filename) updateData.filename = filename;
      if (file_path) updateData.file_path = file_path;
      if (file_size) updateData.file_size = file_size;
      if (reviewer_id) updateData.reviewer_id = reviewer_id;
      if (submit_note !== undefined) updateData.submit_note = submit_note;

      // 如果是从 rejected 状态重新提交，重置状态为 pending
      if (contract.status === CONTRACT_STATUS.REJECTED) {
        // 验证状态转换是否合法
        if (!BusinessUtils.isValidStatusTransition(CONTRACT_STATUS.REJECTED, CONTRACT_STATUS.PENDING)) {
          return res.status(HTTP_STATUS.BAD_REQUEST).json(
            ResponseUtils.error('不允许的状态转换', HTTP_STATUS.BAD_REQUEST)
          );
        }
        updateData.status = CONTRACT_STATUS.PENDING;
        updateData.review_comment = null;
        updateData.reviewed_at = null;
      }

      // 更新合同
      await ContractModel.update(contractId, updateData);

      // 获取更新后的合同信息
      const updatedContract = await ContractModel.findById(contractId);

      // 缓存系统已移除，记录合同更新日志
      const updatedFields = Object.keys(updateData);

      res.json(ResponseUtils.success(updatedContract, RESPONSE_MESSAGES.UPDATE_SUCCESS));

    } catch (error) {
      console.error('修改合同错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 删除合同
 * DELETE /api/contracts/:id
 */
router.delete('/:id',
  authenticateToken,
  PermissionMiddleware.canDeleteContract,
  async (req, res) => {
    try {
      const contractId = parseInt(req.params.id);

      if (!contractId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的合同ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 获取合同信息
      const contract = await ContractModel.findById(contractId);

      if (!contract) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error(RESPONSE_MESSAGES.CONTRACT_NOT_FOUND, HTTP_STATUS.NOT_FOUND)
        );
      }

      // 检查删除权限
      if (req.user.role !== USER_ROLES.ADMIN) {
        // 只有管理员可以删除合同，或者提交人可以删除自己的待审核合同
        if (contract.submitter_id !== req.user.id || contract.status !== CONTRACT_STATUS.PENDING) {
          return res.status(HTTP_STATUS.FORBIDDEN).json(
            ResponseUtils.error(RESPONSE_MESSAGES.FORBIDDEN, HTTP_STATUS.FORBIDDEN)
          );
        }
      }

      // 删除合同
      await ContractModel.delete(contractId);

      // 缓存相关代码已移除，系统直接从数据库获取最新数据

      res.json(ResponseUtils.success(null, '合同删除成功'));

    } catch (error) {
      console.error('删除合同错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);






/**
 * 提交审核结果
 * PUT /api/contracts/:id/review
 */
router.put('/:id/review',
  authenticateToken,
  PermissionMiddleware.canReviewContract,
  [
    body('result')
      .isIn(['approved', 'rejected'])
      .withMessage('审核结果必须是 approved 或 rejected'),
    // 条件验证：根据审核结果决定comment字段的验证规则
    body('comment')
      .custom((value, { req }) => {
        const { result } = req.body;

        if (result === 'rejected') {
          // 审核驳回时，comment必填且至少10个字符
          if (!value || value.trim().length === 0) {
            throw new Error('审核驳回时必须填写审核意见');
          }
          if (value.trim().length < 10) {
            throw new Error('审核意见至少10个字符');
          }
        }

        // 审核通过时，comment可选，但如果填写了则检查长度
        if (value && value.length > 1000) {
          throw new Error('审核意见不能超过1000字符');
        }

        return true;
      })
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const contractId = parseInt(req.params.id);
      const { result, comment } = req.body;

      if (!contractId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的合同ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 获取合同信息
      const contract = await ContractModel.findById(contractId);

      if (!contract) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error(RESPONSE_MESSAGES.CONTRACT_NOT_FOUND, HTTP_STATUS.NOT_FOUND)
        );
      }

      // 检查用户是否可以审核这个合同
      const canReview = BusinessUtils.canReviewContract(contract, req.user);
      if (!canReview) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('您没有权限审核这个合同', HTTP_STATUS.FORBIDDEN)
        );
      }

      // 检查合同状态转换是否合法
      const allowedStatuses = [CONTRACT_STATUS.PENDING, CONTRACT_STATUS.PENDING_CITY_REVIEW];
      if (!allowedStatuses.includes(contract.status)) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('只有待审核的合同才能提交审核结果', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 根据审核结果和用户角色决定目标状态
      let targetStatus;
      let needsCityReview = false;
      let needsLegalOfficerAssignment = false;

      if (result === 'rejected') {
        // 拒绝的合同直接设为rejected状态
        targetStatus = CONTRACT_STATUS.REJECTED;
      } else {
        // 批准的合同需要根据当前审核级别和用户角色决定流转
        if (contract.review_level === 'county_reviewer' &&
          req.user.role === USER_ROLES.COUNTY_REVIEWER) {
          // 县局审核员批准县级合同，流转到市局审核
          targetStatus = CONTRACT_STATUS.PENDING_CITY_REVIEW;
          needsCityReview = true;
        } else if (contract.review_level === 'city_reviewer' &&
          req.user.role === USER_ROLES.CITY_REVIEWER) {
          // 市局审核员批准，流转到法规员分配合同编号
          targetStatus = CONTRACT_STATUS.PENDING_CONTRACT_NUMBER;
          needsLegalOfficerAssignment = true;
        } else if (contract.status === CONTRACT_STATUS.PENDING_CITY_REVIEW &&
          req.user.role === USER_ROLES.CITY_REVIEWER) {
          // 市局审核员审核待市局审核的合同，流转到法规员
          targetStatus = CONTRACT_STATUS.PENDING_CONTRACT_NUMBER;
          needsLegalOfficerAssignment = true;
        } else if (req.user.role === USER_ROLES.ADMIN) {
          // 管理员可以直接批准任何合同，但也需要分配合同编号
          targetStatus = CONTRACT_STATUS.PENDING_CONTRACT_NUMBER;
          needsLegalOfficerAssignment = true;
        } else {
          // 其他情况流转到法规员
          targetStatus = CONTRACT_STATUS.PENDING_CONTRACT_NUMBER;
          needsLegalOfficerAssignment = true;
        }
      }

      // 使用事务确保审核记录和合同状态同时更新
      const reviewData = {
        contract_id: contractId,
        reviewer_id: req.user.id,
        reviewer_role: req.user.role,
        result: result,
        comment: comment,
        review_level: req.user.role // 使用当前审核员的角色作为审核级别
      };

      // 准备合同更新数据
      const updateData = {
        status: targetStatus,
        reviewed_at: DateTimeUtils.nowUTC()
      };

      // 如果是最终审核（不需要流转），更新review_comment
      if (!needsCityReview && !needsLegalOfficerAssignment) {
        updateData.review_comment = comment;
      }

      // 如果需要流转到市局，需要分配市局审核员
      if (needsCityReview) {
        // 查找可用的市局审核员（这里简化处理，实际可能需要更复杂的分配逻辑）
        const cityReviewers = await UserModel.findByRole(USER_ROLES.CITY_REVIEWER);
        if (cityReviewers && cityReviewers.length > 0) {
          // 简单分配给第一个可用的市局审核员
          updateData.reviewer_id = cityReviewers[0].id;
        }
      }

      // 如果需要流转到法规员，需要分配法规员
      if (needsLegalOfficerAssignment) {
        // 查找可用的法规员
        const legalOfficers = await UserModel.findByRole(USER_ROLES.LEGAL_OFFICER);
        if (legalOfficers && legalOfficers.length > 0) {
          // 简单分配给第一个可用的法规员
          updateData.legal_officer_id = legalOfficers[0].id;
        }
      }

      // 在事务中执行审核记录创建和合同状态更新
      const operations = [
        {
          sql: 'INSERT INTO contract_reviews (contract_id, reviewer_id, reviewer_role, result, comment, review_level, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
          params: [reviewData.contract_id, reviewData.reviewer_id, reviewData.reviewer_role, reviewData.result, reviewData.comment, reviewData.review_level, DateTimeUtils.nowUTC()]
        }
      ];

      // 构建合同更新SQL
      const updateFields = [];
      const updateValues = [];
      Object.keys(updateData).forEach(key => {
        updateFields.push(`${key} = ?`);
        updateValues.push(updateData[key]);
      });
      // 使用UTC时间更新
      updateFields.push('updated_at = ?');
      updateValues.push(DateTimeUtils.nowUTC());
      updateValues.push(contractId);

      operations.push({
        sql: `UPDATE contracts SET ${updateFields.join(', ')} WHERE id = ?`,
        params: updateValues
      });

      await database.transaction(operations);

      // 记录审核操作日志
      try {
        const { logOperation } = require('../utils/logger');

        let logDetails = {
          contractId: contractId,
          serialNumber: contract.serial_number,
          reviewResult: result,
          comment: comment,
          oldStatus: contract.status,
          newStatus: targetStatus,
          reviewLevel: contract.review_level
        };

        // 如果是流转到市局，记录流转信息
        if (needsCityReview) {
          logDetails.transferredToCity = true;
          logDetails.assignedReviewerId = updateData.reviewer_id;

          await logOperation({
            userId: req.user.id,
            action: 'contract_transfer_to_city',
            resourceType: 'contract',
            resourceId: contractId.toString(),
            details: `县局审核员将合同 ${contract.serial_number} 流转到市局审核`,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            status: 'success'
          });
        } else {
          // 记录普通审核操作
          await logOperation({
            userId: req.user.id,
            action: result === 'approved' ? 'contract_approve' : 'contract_reject',
            resourceType: 'contract',
            resourceId: contractId.toString(),
            details: `审核合同 ${contract.serial_number}：${result === 'approved' ? '通过' : '拒绝'}${comment ? `，备注：${comment}` : ''}`,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            status: 'success'
          });
        }
      } catch (logError) {
        console.error('记录审核日志失败:', logError);
        // 日志记录失败不影响主要业务流程
      }

      // 获取更新后的合同信息
      const updatedContract = await ContractModel.findById(contractId);

      // 发送系统内通知
      try {
        const submitter = await UserModel.findById(contract.submitter_id);
        const reviewer = await UserModel.findById(req.user.id);

        const contractDataForNotification = {
          id: contract.id,
          serial_number: contract.serial_number,
          submitter_name: submitter.username,
          filename: contract.filename,
          review_comment: comment
        };

        if (result === 'approved') {
          if (needsCityReview) {
            // 县局审核通过，流转到市局 - 通知提交人和市局审核员
            await NotificationService.notifyContractTransferredToCity(contractDataForNotification, contract.submitter_id);

            // 通知市局审核员
            if (updateData.reviewer_id) {
              await NotificationService.notifyContractAssigned(contractDataForNotification, updateData.reviewer_id);
            }
          } else if (needsLegalOfficerAssignment) {
            // 审核通过，流转到法规员分配合同编号 - 通知提交人和法规员
            await NotificationService.notifyContractApproved(contractDataForNotification, contract.submitter_id);

            // 通知所有法规员有新的合同需要分配编号
            const legalOfficers = await UserModel.findByRole(USER_ROLES.LEGAL_OFFICER);
            for (const legalOfficer of legalOfficers) {
              await NotificationService.notifyContractAssignedToLegalOfficer(contractDataForNotification, legalOfficer.id);
            }
          } else {
            // 最终审核通过
            await NotificationService.notifyContractApproved(contractDataForNotification, contract.submitter_id);
          }
        } else {
          // 审核拒绝
          await NotificationService.notifyContractRejected(contractDataForNotification, contract.submitter_id, comment);
        }
      } catch (notificationError) {
        console.error('发送审核结果通知失败:', notificationError);
        // 通知发送失败不影响主要业务流程
      }

      // 获取审核历史，确保返回完整的合同信息
      const reviewHistory = await ContractReviewModel.findByContractId(contractId);

      // 构建包含审核历史的完整合同信息
      const contractWithHistory = {
        ...updatedContract,
        reviewHistory: reviewHistory
      };

      const message = result === 'approved' ? '合同审核通过' : '合同审核不通过';
      res.json(ResponseUtils.success(contractWithHistory, message));

    } catch (error) {
      console.error('提交审核结果错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 分配合同编号
 * PUT /api/contracts/:id/assign-number
 */
router.put('/:id/assign-number',
  authenticateToken,
  [
    body('contract_number')
      .notEmpty()
      .withMessage('合同编号不能为空')
      .isLength({ min: 1, max: 50 })
      .withMessage('合同编号长度应在1-50字符之间'),
    body('comment')
      .optional()
      .isLength({ max: 500 })
      .withMessage('备注不能超过500字符')
  ],
  async (req, res) => {
    try {
      const contractId = parseInt(req.params.id);
      const { contract_number, comment } = req.body;

      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('输入验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      // 检查用户权限
      if (req.user.role !== USER_ROLES.LEGAL_OFFICER && req.user.role !== USER_ROLES.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('只有法规员可以分配合同编号', HTTP_STATUS.FORBIDDEN)
        );
      }

      // 获取合同信息
      const contract = await ContractModel.findById(contractId);
      if (!contract) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error(RESPONSE_MESSAGES.CONTRACT_NOT_FOUND, HTTP_STATUS.NOT_FOUND)
        );
      }

      // 检查合同状态
      if (contract.status !== CONTRACT_STATUS.PENDING_CONTRACT_NUMBER) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('只有待分配合同编号的合同才能分配编号', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 检查合同编号是否已存在
      const existingContract = await ContractModel.findByContractNumber(contract_number);
      if (existingContract) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('合同编号已存在，请使用其他编号', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 更新合同编号和状态
      const updateData = {
        contract_number: contract_number,
        status: CONTRACT_STATUS.COMPLETED,
        legal_officer_id: req.user.id,
        contract_number_assigned_at: DateTimeUtils.nowUTC(),
        updated_at: DateTimeUtils.nowUTC()
      };

      // 使用事务确保合同更新和审核记录同时创建
      const operations = [
        // 更新合同信息
        {
          sql: `UPDATE contracts SET contract_number = ?, status = ?, legal_officer_id = ?, contract_number_assigned_at = ?, updated_at = ? WHERE id = ?`,
          params: [contract_number, CONTRACT_STATUS.COMPLETED, req.user.id, DateTimeUtils.nowUTC(), DateTimeUtils.nowUTC(), contractId]
        },
        // 创建审核记录 - 记录合同编号分配完成
        {
          sql: 'INSERT INTO contract_reviews (contract_id, reviewer_id, reviewer_role, result, comment, review_level, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
          params: [contractId, req.user.id, req.user.role, 'approved', `分配合同编号：${contract_number}${comment ? `，备注：${comment}` : ''}`, 'legal_officer', DateTimeUtils.nowUTC()]
        }
      ];

      await database.transaction(operations);

      // 记录操作日志
      try {
        const { logOperation } = require('../utils/logger');
        await logOperation({
          userId: req.user.id,
          action: 'contract_assign_number',
          resourceType: 'contract',
          resourceId: contractId.toString(),
          details: `为合同 ${contract.serial_number} 分配编号：${contract_number}${comment ? `，备注：${comment}` : ''}`,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          status: 'success'
        });
      } catch (logError) {
        console.error('记录操作日志失败:', logError);
      }

      // 获取更新后的合同信息
      const updatedContract = await ContractModel.findById(contractId);

      // 发送合同编号分配完成通知给提交人
      try {
        const contractDataForNotification = {
          id: updatedContract.id,
          serial_number: updatedContract.serial_number,
          filename: updatedContract.filename
        };

        await NotificationService.notifyContractNumberAssigned(
          contractDataForNotification,
          updatedContract.submitter_id,
          contract_number
        );
      } catch (notificationError) {
        console.error('发送合同编号分配通知失败:', notificationError);
        // 通知发送失败不影响主要业务流程
      }

      res.json(ResponseUtils.success(updatedContract, '合同编号分配成功'));

    } catch (error) {
      console.error('分配合同编号错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);



module.exports = router;
