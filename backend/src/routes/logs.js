/**
 * 操作日志路由
 * 提供日志查询、统计等功能
 */

const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { requireRole } = require('../middleware/permission');
const { 
  getOperationLogs, 
  getOperationStats, 
  cleanupOldLogs,
  ACTIONS,
  RESOURCE_TYPES 
} = require('../utils/logger');
const { systemLogMiddleware } = require('../middleware/logger');

const router = express.Router();

/**
 * 获取操作日志列表
 * GET /api/logs
 * 需要管理员权限
 */
router.get('/', 
  authenticateToken,
  requireRole(['admin']),
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页条数必须在1-100之间'),
    query('userId').optional().isInt({ min: 1 }).withMessage('用户ID必须是正整数'),
    query('action').optional().isString().withMessage('操作类型必须是字符串'),
    query('resourceType').optional().isString().withMessage('资源类型必须是字符串'),
    query('status').optional().isIn(['success', 'failed']).withMessage('状态必须是success或failed'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        limit = 20,
        userId,
        action,
        resourceType,
        status,
        startDate,
        endDate
      } = req.query;

      const result = await getOperationLogs({
        page: parseInt(page),
        limit: parseInt(limit),
        userId: userId ? parseInt(userId) : undefined,
        action,
        resourceType,
        status,
        startDate,
        endDate
      });

      res.json({
        success: true,
        data: result,
        message: '获取操作日志成功'
      });

    } catch (error) {
      console.error('获取操作日志失败:', error);
      res.status(500).json({
        success: false,
        message: '获取操作日志失败',
        error: error.message
      });
    }
  }
);

/**
 * 获取操作统计数据
 * GET /api/logs/stats
 * 需要管理员权限
 */
router.get('/stats',
  authenticateToken,
  requireRole(['admin']),
  [
    query('period').optional().isIn(['1d', '7d', '30d']).withMessage('统计周期必须是1d、7d或30d'),
    query('userId').optional().isInt({ min: 1 }).withMessage('用户ID必须是正整数')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const { period = '7d', userId } = req.query;

      const stats = await getOperationStats({
        period,
        userId: userId ? parseInt(userId) : undefined
      });

      res.json({
        success: true,
        data: stats,
        message: '获取操作统计成功'
      });

    } catch (error) {
      console.error('获取操作统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取操作统计失败',
        error: error.message
      });
    }
  }
);

/**
 * 清理过期日志
 * POST /api/logs/cleanup
 * 需要管理员权限
 */
router.post('/cleanup',
  authenticateToken,
  requireRole(['admin']),
  systemLogMiddleware('cleanup_logs'),
  [
    body('days').optional().isInt({ min: 1, max: 365 }).withMessage('保留天数必须在1-365之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const { days = 90 } = req.body;
      const deletedCount = await cleanupOldLogs(days);

      res.json({
        success: true,
        data: { deletedCount },
        message: `清理完成，删除了 ${deletedCount} 条过期日志`
      });

    } catch (error) {
      console.error('清理过期日志失败:', error);
      res.status(500).json({
        success: false,
        message: '清理过期日志失败',
        error: error.message
      });
    }
  }
);

/**
 * 获取操作类型列表
 * GET /api/logs/actions
 * 需要管理员权限
 */
router.get('/actions',
  authenticateToken,
  requireRole(['admin']),
  (req, res) => {
    try {
      const actions = Object.values(ACTIONS);
      const resourceTypes = Object.values(RESOURCE_TYPES);

      res.json({
        success: true,
        data: {
          actions,
          resourceTypes
        },
        message: '获取操作类型成功'
      });

    } catch (error) {
      console.error('获取操作类型失败:', error);
      res.status(500).json({
        success: false,
        message: '获取操作类型失败',
        error: error.message
      });
    }
  }
);

/**
 * 获取用户操作日志
 * GET /api/logs/user/:id
 * 需要管理员权限或查看自己的日志
 */
router.get('/user/:id',
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页条数必须在1-100之间'),
    query('action').optional().isString().withMessage('操作类型必须是字符串'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const targetUserId = parseInt(req.params.id);
      const currentUserId = req.user.id;
      const userRole = req.user.role;

      // 权限检查：只有管理员或用户本人可以查看
      if (userRole !== 'admin' && currentUserId !== targetUserId) {
        return res.status(403).json({
          success: false,
          message: '权限不足'
        });
      }

      const {
        page = 1,
        limit = 20,
        action,
        startDate,
        endDate
      } = req.query;

      const result = await getOperationLogs({
        page: parseInt(page),
        limit: parseInt(limit),
        userId: targetUserId,
        action,
        startDate,
        endDate
      });

      res.json({
        success: true,
        data: result,
        message: '获取用户操作日志成功'
      });

    } catch (error) {
      console.error('获取用户操作日志失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户操作日志失败',
        error: error.message
      });
    }
  }
);

module.exports = router;