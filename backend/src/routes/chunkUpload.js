/**
 * 分片上传路由
 * 处理文件分片上传、合并、状态查询等功能
 */

const express = require('express');
const multer = require('multer');
const { authenticateToken } = require('../middleware/auth');
const { chunkUploadManager } = require('../utils/chunkUpload');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS } = require('../utils/constants');
const { logOperation, ACTIONS, RESOURCE_TYPES } = require('../utils/logger');

const router = express.Router();

// 配置multer用于处理分片数据
const upload = multer({
  limits: {
    fileSize: 1024 * 1024 * 5 // 5MB per chunk max
  },
  storage: multer.memoryStorage()
});

/**
 * 初始化分片上传
 * POST /api/chunk-upload/init
 */
router.post('/init', authenticateToken, async (req, res) => {
  try {
    const { fileName, fileSize, fileMd5, totalChunks } = req.body;
    const userId = req.user.id;

    // 验证输入参数
    if (!fileName || !fileSize || !totalChunks) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('Missing required parameters', HTTP_STATUS.BAD_REQUEST)
      );
    }

    if (typeof fileSize !== 'number' || fileSize <= 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('Invalid file size', HTTP_STATUS.BAD_REQUEST)
      );
    }

    if (typeof totalChunks !== 'number' || totalChunks <= 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('Invalid total chunks', HTTP_STATUS.BAD_REQUEST)
      );
    }

    const result = await chunkUploadManager.initializeUpload({
      fileName,
      fileSize,
      fileMd5,
      totalChunks,
      userId
    });

    // 记录操作日志
    await logOperation({
      userId,
      action: ACTIONS.CREATE,
      resourceType: RESOURCE_TYPES.FILE,
      resourceId: result.uploadId,
      details: JSON.stringify({
        fileName,
        fileSize,
        totalChunks,
        action: 'init_chunk_upload'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'success'
    });

    res.json(ResponseUtils.success(result, 'Upload session initialized'));

  } catch (error) {
    console.error('Initialize chunk upload error:', error);
    
    // 记录错误日志
    await logOperation({
      userId: req.user?.id || 0,
      action: ACTIONS.CREATE,
      resourceType: RESOURCE_TYPES.FILE,
      resourceId: req.body.fileName || '',
      details: JSON.stringify({
        error: error.message,
        action: 'init_chunk_upload'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'failed'
    });

    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(error.message, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 上传分片
 * POST /api/chunk-upload/chunk
 */
router.post('/chunk', authenticateToken, upload.single('chunk'), async (req, res) => {
  try {
    const { uploadId, chunkIndex } = req.body;
    const chunkData = req.file?.buffer;

    if (!uploadId || chunkIndex === undefined || !chunkData) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('Missing required parameters', HTTP_STATUS.BAD_REQUEST)
      );
    }

    const chunkIndexNum = parseInt(chunkIndex);
    if (isNaN(chunkIndexNum) || chunkIndexNum < 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('Invalid chunk index', HTTP_STATUS.BAD_REQUEST)
      );
    }

    const result = await chunkUploadManager.uploadChunk(
      uploadId,
      chunkIndexNum,
      chunkData
    );

    res.json(ResponseUtils.success(result, 'Chunk uploaded successfully'));

  } catch (error) {
    console.error('Upload chunk error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(error.message, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 合并分片
 * POST /api/chunk-upload/merge
 */
router.post('/merge', authenticateToken, async (req, res) => {
  try {
    const { uploadId } = req.body;
    const userId = req.user.id;

    if (!uploadId) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('Missing uploadId', HTTP_STATUS.BAD_REQUEST)
      );
    }

    const result = await chunkUploadManager.mergeChunks(uploadId);

    // 记录操作日志
    await logOperation({
      userId,
      action: ACTIONS.UPDATE,
      resourceType: RESOURCE_TYPES.FILE,
      resourceId: uploadId,
      details: JSON.stringify({
        fileName: result.fileName,
        originalName: result.originalName,
        fileSize: result.fileSize,
        action: 'merge_chunks'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'success'
    });

    res.json(ResponseUtils.success(result, 'File merged successfully'));

  } catch (error) {
    console.error('Merge chunks error:', error);
    
    // 记录错误日志
    await logOperation({
      userId: req.user?.id || 0,
      action: ACTIONS.UPDATE,
      resourceType: RESOURCE_TYPES.FILE,
      resourceId: req.body.uploadId || '',
      details: JSON.stringify({
        error: error.message,
        action: 'merge_chunks'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'failed'
    });

    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(error.message, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取上传状态
 * GET /api/chunk-upload/status/:uploadId
 */
router.get('/status/:uploadId', authenticateToken, async (req, res) => {
  try {
    const { uploadId } = req.params;
    const status = chunkUploadManager.getUploadStatus(uploadId);

    if (!status) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('Upload session not found', HTTP_STATUS.NOT_FOUND)
      );
    }

    res.json(ResponseUtils.success(status, 'Upload status retrieved'));

  } catch (error) {
    console.error('Get upload status error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(error.message, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 取消上传
 * DELETE /api/chunk-upload/cancel/:uploadId
 */
router.delete('/cancel/:uploadId', authenticateToken, async (req, res) => {
  try {
    const { uploadId } = req.params;
    const userId = req.user.id;

    const result = await chunkUploadManager.cancelUpload(uploadId);

    // 记录操作日志
    await logOperation({
      userId,
      action: ACTIONS.DELETE,
      resourceType: RESOURCE_TYPES.FILE,
      resourceId: uploadId,
      details: JSON.stringify({
        action: 'cancel_upload'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'success'
    });

    res.json(ResponseUtils.success(result, 'Upload cancelled'));

  } catch (error) {
    console.error('Cancel upload error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(error.message, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取活跃会话列表 (管理员)
 * GET /api/chunk-upload/sessions
 */
router.get('/sessions', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以查看所有会话
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    const sessions = chunkUploadManager.getActiveSessions();
    res.json(ResponseUtils.success(sessions, 'Active sessions retrieved'));

  } catch (error) {
    console.error('Get active sessions error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(error.message, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 清理过期会话 (管理员)
 * POST /api/chunk-upload/cleanup
 */
router.post('/cleanup', authenticateToken, async (req, res) => {
  try {
    // 只有管理员可以执行清理
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    await chunkUploadManager.cleanupExpiredSessions();
    
    // 记录操作日志
    await logOperation({
      userId: req.user.id,
      action: ACTIONS.DELETE,
      resourceType: RESOURCE_TYPES.SYSTEM,
      resourceId: 'chunk_upload_cleanup',
      details: JSON.stringify({
        action: 'cleanup_expired_sessions'
      }),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      status: 'success'
    });

    res.json(ResponseUtils.success(null, 'Expired sessions cleaned up'));

  } catch (error) {
    console.error('Cleanup expired sessions error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(error.message, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取上传配置
 * GET /api/chunk-upload/config
 */
router.get('/config', (req, res) => {
  try {
    const config = {
      chunkSize: 1024 * 1024 * 2, // 2MB
      maxFileSize: 1024 * 1024 * 100, // 100MB
      allowedTypes: ['.pdf', '.doc', '.docx'],
      allowedMimeTypes: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ]
    };

    res.json(ResponseUtils.success(config, 'Upload configuration retrieved'));

  } catch (error) {
    console.error('Get upload config error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(error.message, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

module.exports = router;