/**
 * 文件操作路由
 * 处理文件上传、下载、预览等功能
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const router = express.Router();

const { authenticateToken, authenticateFileAccess } = require('../middleware/auth');
const { uploadMiddleware, deleteFile, getFileInfo, generateFileUrl } = require('../middleware/upload');
const {
  validateFileAccess,
  validateContractFileAccess,
  validateDownloadPermission,
  validatePreviewPermission,
  validateDeletePermission,
  fileAccessRateLimit
} = require('../middleware/fileAccess');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES, FILE_CONFIG } = require('../utils/constants');

/**
 * 智能文件路径处理函数
 * @param {string} filename - 文件名或文件路径
 * @returns {string} - 完整的文件系统路径
 */
function getFilePath(filename) {
  if (filename.startsWith('uploads/')) {
    // file_path已经包含uploads前缀，使用相对于项目根目录的路径
    return path.join(__dirname, '../..', filename);
  } else {
    // file_path不包含uploads前缀，添加uploads目录
    return path.join(__dirname, '../../uploads', filename);
  }
}

/**
 * 文件上传
 * POST /api/files/upload
 */
router.post('/upload',
  authenticateToken,
  uploadMiddleware,
  async (req, res) => {
    try {
      const fileInfo = req.fileInfo;

      // 返回文件信息
      res.json(ResponseUtils.success({
        filename: fileInfo.filename,
        originalName: fileInfo.originalName,
        size: fileInfo.size,
        mimetype: fileInfo.mimetype,
        url: generateFileUrl(fileInfo.filename),
        uploadedAt: new Date().toISOString()
      }, RESPONSE_MESSAGES.UPLOAD_SUCCESS));

    } catch (error) {
      console.error('文件上传错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.UPLOAD_FAILED, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 处理文件预览的通用函数
 */
async function handleFilePreview(req, res) {
  try {
    const contract = req.contract;
    // 使用 file_path 字段而不是 filename，因为 file_path 存储的是实际的文件名
    const actualFilename = contract.file_path || contract.filename;
    const filePath = getFilePath(actualFilename);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('文件不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

    // 获取文件信息
    const stats = await getFileInfo(filePath);

    // 设置响应头
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Length', stats.size);

    // 对中文文件名进行编码处理
    const encodedFilename = encodeURIComponent(contract.filename);
    res.setHeader('Content-Disposition', `inline; filename*=UTF-8''${encodedFilename}`);

    // 优化PDF缓存策略 - 缩短缓存时间，增强验证
    res.setHeader('Cache-Control', 'public, max-age=86400, must-revalidate'); // 1天缓存，必须验证
    res.setHeader('Vary', 'Authorization, X-Contract-ID'); // 基于认证和合同ID变化

    // 生成唯一的ETag，包含合同ID确保唯一性
    const uniqueETag = `"contract-${contract.id}-${contract.file_path}-${stats.size}-${stats.modified ? stats.modified.getTime() : Date.now()}"`;
    res.setHeader('ETag', uniqueETag);

    if (stats.modified) {
      res.setHeader('Last-Modified', stats.modified.toUTCString()); // 最后修改时间
    }

    // 添加合同ID到响应头，用于前端缓存标识
    res.setHeader('X-Contract-ID', contract.id.toString());
    res.setHeader('X-File-Path', contract.file_path);

    // 检查条件请求
    const ifNoneMatch = req.headers['if-none-match'];
    if (ifNoneMatch && ifNoneMatch === uniqueETag) {
      return res.status(304).end(); // Not Modified
    }

    res.setHeader('X-Content-Type-Options', 'nosniff'); // 安全头
    res.setHeader('Vary', 'Accept-Encoding'); // 支持压缩
    // 允许同源和指定域名的 iframe 显示
    const allowedOrigins = [
      'http://localhost:5173',
      'http://*************:5173',
      process.env.FRONTEND_URL
    ].filter(Boolean);

    const origin = req.headers.origin;
    if (allowedOrigins.includes(origin)) {
      res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    } else {
      res.removeHeader('X-Frame-Options'); // 移除限制，允许 iframe 显示
    }

    // 如果是HEAD请求，只返回头部信息
    if (req.method === 'HEAD') {
      return res.end();
    }

    // 创建文件流
    const fileStream = fs.createReadStream(filePath);

    // 处理流错误
    fileStream.on('error', (error) => {
      console.error('文件流错误:', error);
      if (!res.headersSent) {
        res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
          ResponseUtils.error('文件读取失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
        );
      }
    });

    // 发送文件
    fileStream.pipe(res);

  } catch (error) {
    console.error('文件预览错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('文件预览失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
}

/**
 * 基于合同ID的文件预览 (任务要求的端点格式)
 * GET /api/files/:id/preview
 * HEAD /api/files/:id/preview (用于获取元数据)
 */
router.get('/:id/preview',
  authenticateFileAccess,
  fileAccessRateLimit,
  validateContractFileAccess,
  handleFilePreview
);

router.head('/:id/preview',
  authenticateFileAccess,
  fileAccessRateLimit,
  validateContractFileAccess,
  handleFilePreview
);

/**
 * 基于合同ID的文件下载
 * GET /api/files/:id/download
 */
router.get('/:id/download',
  authenticateFileAccess,
  fileAccessRateLimit,
  validateContractFileAccess,
  validateDownloadPermission,
  async (req, res) => {
    try {
      const contract = req.contract;
      // 使用 file_path 字段而不是 filename，因为 file_path 存储的是实际的文件名
      const actualFilename = contract.file_path || contract.filename;
      const filePath = getFilePath(actualFilename);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('文件不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 获取文件信息
      const stats = await getFileInfo(filePath);

      // 设置响应头
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Length', stats.size);

      // 对中文文件名进行编码处理
      const encodedFilename = encodeURIComponent(contract.filename);
      res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);

      res.setHeader('X-Content-Type-Options', 'nosniff'); // 安全头

      // 创建文件流
      const fileStream = fs.createReadStream(filePath);

      // 处理流错误
      fileStream.on('error', (error) => {
        console.error('文件流错误:', error);
        if (!res.headersSent) {
          res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
            ResponseUtils.error('文件读取失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
          );
        }
      });

      // 发送文件
      fileStream.pipe(res);

    } catch (error) {
      console.error('文件下载错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('文件下载失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 文件下载 (基于文件名 - 保持向后兼容)
 * GET /api/files/download/:filename
 */
router.get('/download/:filename',
  authenticateToken,
  fileAccessRateLimit,
  validateFileAccess,
  validateDownloadPermission,
  async (req, res) => {
    try {
      const filename = req.filename || req.params.filename;
      const filePath = getFilePath(filename);

      // 获取文件信息
      const stats = await getFileInfo(filePath);

      // 设置响应头
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Length', stats.size);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      // 创建文件流
      const fileStream = fs.createReadStream(filePath);

      // 处理流错误
      fileStream.on('error', (error) => {
        console.error('文件流错误:', error);
        if (!res.headersSent) {
          res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
            ResponseUtils.error(RESPONSE_MESSAGES.FILE_NOT_FOUND, HTTP_STATUS.INTERNAL_SERVER_ERROR)
          );
        }
      });

      // 发送文件
      fileStream.pipe(res);

    } catch (error) {
      console.error('文件下载错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 临时文件预览 (用于刚上传的文件，无需合同关联)
 * GET /api/files/temp-preview/:filename
 */
router.get('/temp-preview/:filename',
  authenticateFileAccess,
  fileAccessRateLimit,
  async (req, res) => {
    try {
      const filename = req.params.filename;
      const user = req.user;


      if (!user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
        );
      }

      // 基本的文件名验证
      if (!filename || typeof filename !== 'string') {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的文件名', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 防止路径遍历攻击
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('非法的文件访问', HTTP_STATUS.FORBIDDEN)
        );
      }

      const filePath = getFilePath(filename);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('文件不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 获取文件信息
      const stats = await getFileInfo(filePath);

      // 设置响应头
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Length', stats.size);
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
      res.setHeader('Cache-Control', 'public, max-age=3600'); // 缓存1小时

      // 创建文件流
      const fileStream = fs.createReadStream(filePath);

      // 处理流错误
      fileStream.on('error', (error) => {
        console.error('文件流错误:', error);
        if (!res.headersSent) {
          res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
            ResponseUtils.error('文件读取失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
          );
        }
      });

      // 发送文件
      fileStream.pipe(res);

    } catch (error) {
      console.error('临时文件预览错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('文件预览失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 临时文件下载 (用于刚上传的文件，无需合同关联)
 * GET /api/files/temp-download/:filename
 */
router.get('/temp-download/:filename',
  authenticateFileAccess,
  fileAccessRateLimit,
  async (req, res) => {
    try {
      const filename = req.params.filename;
      const user = req.user;

      if (!user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
        );
      }

      // 基本的文件名验证
      if (!filename || typeof filename !== 'string') {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的文件名', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 防止路径遍历攻击
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('非法的文件访问', HTTP_STATUS.FORBIDDEN)
        );
      }

      const filePath = getFilePath(filename);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('文件不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 获取文件信息
      const stats = await getFileInfo(filePath);

      // 设置下载响应头
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Length', stats.size);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      // 创建文件流
      const fileStream = fs.createReadStream(filePath);

      // 处理流错误
      fileStream.on('error', (error) => {
        console.error('文件流错误:', error);
        if (!res.headersSent) {
          res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
            ResponseUtils.error('文件读取失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
          );
        }
      });

      // 发送文件
      fileStream.pipe(res);

    } catch (error) {
      console.error('临时文件下载错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('文件下载失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 文件预览 (基于文件名 - 保持向后兼容)
 * GET /api/files/preview/:filename
 */
router.get('/preview/:filename',
  authenticateToken,
  fileAccessRateLimit,
  validateFileAccess,
  validatePreviewPermission,
  async (req, res) => {
    try {
      const filename = req.filename || req.params.filename;
      const filePath = getFilePath(filename);

      // 获取文件信息
      const stats = await getFileInfo(filePath);

      // 设置响应头
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Length', stats.size);
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);

      // 优化PDF缓存策略
      res.setHeader('Cache-Control', 'public, max-age=2592000, must-revalidate'); // 30天缓存，但需要验证

      // 安全地生成ETag和Last-Modified头
      if (stats.modified) {
        res.setHeader('ETag', `"${stats.size}-${stats.modified.getTime()}"`); // 基于文件大小和修改时间的ETag
        res.setHeader('Last-Modified', stats.modified.toUTCString()); // 最后修改时间
      } else {
        res.setHeader('ETag', `"${stats.size}-${Date.now()}"`); // 降级ETag
      }

      res.setHeader('Vary', 'Accept-Encoding'); // 支持压缩

      // 创建文件流
      const fileStream = fs.createReadStream(filePath);

      // 处理流错误
      fileStream.on('error', (error) => {
        console.error('文件流错误:', error);
        if (!res.headersSent) {
          res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
            ResponseUtils.error(RESPONSE_MESSAGES.FILE_NOT_FOUND, HTTP_STATUS.INTERNAL_SERVER_ERROR)
          );
        }
      });

      // 发送文件
      fileStream.pipe(res);

    } catch (error) {
      console.error('文件预览错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 临时文件删除 (用于刚上传的文件，无需合同关联)
 * DELETE /api/files/temp-delete/:filename
 */
router.delete('/temp-delete/:filename',
  authenticateToken,
  async (req, res) => {
    try {
      const filename = req.params.filename;
      const user = req.user;

      if (!user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
        );
      }

      // 基本的文件名验证
      if (!filename || typeof filename !== 'string') {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的文件名', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 防止路径遍历攻击
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('非法的文件访问', HTTP_STATUS.FORBIDDEN)
        );
      }

      const filePath = getFilePath(filename);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('文件不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 删除文件
      await deleteFile(filePath);

      res.json(ResponseUtils.success(null, '文件删除成功'));

    } catch (error) {
      console.error('临时文件删除错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('文件删除失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 删除文件
 * DELETE /api/files/:filename
 */
router.delete('/:filename',
  authenticateToken,
  validateFileAccess,
  async (req, res) => {
    try {
      const filePath = req.filePath;

      // 删除文件
      await deleteFile(filePath);

      res.json(ResponseUtils.success(null, '文件删除成功'));

    } catch (error) {
      console.error('文件删除错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('文件删除失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取文件信息
 * GET /api/files/info/:filename
 */
router.get('/info/:filename',
  authenticateToken,
  validateFileAccess,
  async (req, res) => {
    try {
      const filePath = req.filePath;
      const filename = req.params.filename;

      // 获取文件信息
      const stats = await getFileInfo(filePath);

      res.json(ResponseUtils.success({
        filename,
        size: stats.size,
        created: stats.created,
        modified: stats.modified,
        url: generateFileUrl(filename)
      }));

    } catch (error) {
      console.error('获取文件信息错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);



/**
 * 获取上传配置
 * GET /api/files/config
 */
router.get('/config', (req, res) => {
  res.json(ResponseUtils.success({
    allowedTypes: FILE_CONFIG.ALLOWED_TYPES,
    allowedMimeTypes: FILE_CONFIG.ALLOWED_MIME_TYPES,
    maxSize: FILE_CONFIG.MAX_SIZE,
    maxSizeMB: Math.round(FILE_CONFIG.MAX_SIZE / 1024 / 1024)
  }));
});

module.exports = router;
