/**
 * 审核日志路由
 * 提供合同审核流转的详细日志查询功能
 */

const express = require('express');
const { query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { requireRole } = require('../middleware/permission');
const { USER_ROLES, HTTP_STATUS } = require('../utils/constants');
const { ResponseUtils } = require('../utils/helpers');
const { database } = require('../utils/database');

const router = express.Router();

/**
 * 获取合同审核流转日志
 * GET /api/audit/contract-reviews
 * 需要管理员或审核员权限
 */
router.get('/contract-reviews',
  authenticateToken,
  requireRole([USER_ROLES.ADMIN, USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER]),
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页条数必须在1-100之间'),
    query('contractId').optional().isInt({ min: 1 }).withMessage('合同ID必须是正整数'),
    query('action').optional().isIn(['contract_submit', 'contract_approve', 'contract_reject', 'contract_transfer_to_city']).withMessage('操作类型无效'),
    query('userId').optional().isInt({ min: 1 }).withMessage('用户ID必须是正整数'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const {
        page = 1,
        limit = 20,
        contractId,
        action,
        userId,
        startDate,
        endDate
      } = req.query;

      // 构建查询条件
      let whereConditions = ['ol.resource_type = ?'];
      let params = ['contract'];

      // 如果不是管理员，只能查看自己相关的日志
      if (req.user.role !== USER_ROLES.ADMIN) {
        whereConditions.push('(ol.user_id = ? OR c.submitter_id = ? OR c.reviewer_id = ?)');
        params.push(req.user.id, req.user.id, req.user.id);
      }

      if (contractId) {
        whereConditions.push('ol.resource_id = ?');
        params.push(contractId);
      }

      if (action) {
        whereConditions.push('ol.action = ?');
        params.push(action);
      }

      if (userId) {
        whereConditions.push('ol.user_id = ?');
        params.push(userId);
      }

      if (startDate) {
        whereConditions.push('ol.created_at >= ?');
        params.push(startDate);
      }

      if (endDate) {
        whereConditions.push('ol.created_at <= ?');
        params.push(endDate);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      const offset = (page - 1) * limit;

      // 查询总数
      const countSql = `
        SELECT COUNT(*) as total
        FROM operation_logs ol
        LEFT JOIN contracts c ON ol.resource_id = c.id
        ${whereClause}
      `;

      const countResult = await database.get(countSql, params);
      const total = countResult.total;

      // 查询日志列表
      const listSql = `
        SELECT
          ol.id,
          ol.user_id,
          u.username,
          u.role,
          ol.action,
          ol.resource_id as contract_id,
          ol.details,
          ol.status,
          ol.created_at,
          c.serial_number,
          c.filename,
          c.status as contract_status,
          c.review_level,
          submitter.username as submitter_name,
          reviewer.username as reviewer_name
        FROM operation_logs ol
        LEFT JOIN users u ON ol.user_id = u.id
        LEFT JOIN contracts c ON ol.resource_id = c.id
        LEFT JOIN users submitter ON c.submitter_id = submitter.id
        LEFT JOIN users reviewer ON c.reviewer_id = reviewer.id
        ${whereClause}
        ORDER BY ol.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const listParams = [...params, limit, offset];
      const logs = await database.all(listSql, listParams);

      // 格式化日志数据
      const formattedLogs = logs.map(log => ({
        id: log.id,
        user: {
          id: log.user_id,
          username: log.username,
          role: log.role
        },
        action: log.action,
        actionText: getActionText(log.action),
        contract: {
          id: log.contract_id,
          serialNumber: log.serial_number,
          filename: log.filename,
          status: log.contract_status,
          reviewLevel: log.review_level,
          submitterName: log.submitter_name,
          reviewerName: log.reviewer_name
        },
        details: log.details,
        status: log.status,
        createdAt: log.created_at
      }));

      res.json(ResponseUtils.paginated(formattedLogs, {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }));

    } catch (error) {
      console.error('获取审核日志失败:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('获取审核日志失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取特定合同的审核流转历史
 * GET /api/audit/contract/:id/history
 */
router.get('/contract/:id/history',
  authenticateToken,
  requireRole([USER_ROLES.ADMIN, USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER]),
  async (req, res) => {
    try {
      const contractId = parseInt(req.params.id);

      if (!contractId || contractId <= 0) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的合同ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 检查用户是否有权限查看该合同
      const contract = await database.get('SELECT * FROM contracts WHERE id = ?', [contractId]);
      if (!contract) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('合同不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 权限检查：非管理员只能查看自己相关的合同
      if (req.user.role !== USER_ROLES.ADMIN) {
        if (contract.submitter_id !== req.user.id && contract.reviewer_id !== req.user.id) {
          return res.status(HTTP_STATUS.FORBIDDEN).json(
            ResponseUtils.error('您没有权限查看该合同的审核历史', HTTP_STATUS.FORBIDDEN)
          );
        }
      }

      // 查询该合同的所有审核日志
      const sql = `
        SELECT
          ol.id,
          ol.user_id,
          u.username,
          u.role,
          ol.action,
          ol.details,
          ol.status,
          ol.created_at
        FROM operation_logs ol
        LEFT JOIN users u ON ol.user_id = u.id
        WHERE ol.resource_type = 'contract' AND ol.resource_id = ?
        ORDER BY ol.created_at ASC
      `;

      const logs = await database.all(sql, [contractId]);

      // 格式化审核历史
      const history = logs.map(log => ({
        id: log.id,
        user: {
          id: log.user_id,
          username: log.username,
          role: log.role
        },
        action: log.action,
        actionText: getActionText(log.action),
        details: log.details,
        status: log.status,
        createdAt: log.created_at
      }));

      res.json(ResponseUtils.success({
        contract: {
          id: contract.id,
          serialNumber: contract.serial_number,
          filename: contract.filename,
          status: contract.status,
          reviewLevel: contract.review_level
        },
        history
      }, '获取审核历史成功'));

    } catch (error) {
      console.error('获取合同审核历史失败:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('获取审核历史失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取操作类型的中文描述
 */
function getActionText(action) {
  const actionMap = {
    'contract_submit': '提交合同',
    'contract_approve': '审核通过',
    'contract_reject': '审核拒绝',
    'contract_transfer_to_city': '流转市局'
  };
  return actionMap[action] || action;
}

module.exports = router;
