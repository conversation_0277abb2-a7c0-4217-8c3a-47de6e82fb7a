/**
 * 用户管理路由
 * 提供用户的增删改查功能
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcrypt');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const { UserModel } = require('../utils/database');
const { authenticateToken } = require('../middleware/auth');
const { RoleMiddleware, PermissionMiddleware } = require('../middleware/rbac');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES, USER_ROLES, PERMISSIONS } = require('../utils/constants');
// 缓存相关导入已移除，系统直接从数据库获取数据
const { processAvatar, generateAvatarSizes, validateImage } = require('../../utils/imageProcessor');

const router = express.Router();

// 配置头像上传 - 使用内存存储以便进行图片处理
const avatarUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB限制
  },
  fileFilter: function (req, file, cb) {
    // 检查文件类型
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只允许上传 JPEG, JPG, PNG, GIF, WEBP 格式的图片'));
    }
  }
});

/**
 * 获取用户列表
 * GET /api/users
 */
router.get('/',
  authenticateToken,
  RoleMiddleware.requireAdmin,
  async (req, res) => {
    try {
      const { page = 1, pageSize = 10, role, status, keyword } = req.query;

      // 构建过滤条件
      const filters = {};
      if (role) filters.role = role;
      if (status) filters.status = status;
      if (keyword) filters.keyword = keyword;

      const result = await UserModel.getList(filters, parseInt(page), parseInt(pageSize));

      res.json(ResponseUtils.success(result));

    } catch (error) {
      console.error('获取用户列表错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取用户详情
 * GET /api/users/:id
 */
router.get('/:id',
  authenticateToken,
  RoleMiddleware.requireAdmin,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.id);

      if (!userId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的用户ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      const user = await UserModel.findById(userId);

      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('用户不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 移除敏感信息
      delete user.password;

      res.json(ResponseUtils.success(user));

    } catch (error) {
      console.error('获取用户详情错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 创建用户
 * POST /api/users
 */
router.post('/',
  authenticateToken,
  PermissionMiddleware.canCreateUser,
  [
    body('username')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('密码长度至少6个字符'),
    body('role')
      .isIn(Object.values(USER_ROLES))
      .withMessage('无效的用户角色'),
    body('real_name')
      .optional()
      .isLength({ max: 100 })
      .withMessage('真实姓名不能超过100个字符'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('邮箱格式不正确'),
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('手机号格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { username, password, role, real_name, email, phone } = req.body;

      // 检查用户名是否已存在
      const existingUser = await UserModel.findByUsername(username);
      if (existingUser) {
        return res.status(HTTP_STATUS.CONFLICT).json(
          ResponseUtils.error('用户名已存在', HTTP_STATUS.CONFLICT)
        );
      }

      // 加密密码 - 使用较低的saltRounds减少CPU负载
      const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // 创建用户
      const userId = await UserModel.create({
        username,
        password: hashedPassword,
        role,
        real_name: real_name || null,
        email: email || null,
        phone: phone || null,
        status: 'active',
        created_by: req.user.id
      });

      // 获取创建的用户信息
      const newUser = await UserModel.findById(userId);
      delete newUser.password;

      res.status(HTTP_STATUS.CREATED).json(
        ResponseUtils.success(newUser, '用户创建成功')
      );

    } catch (error) {
      console.error('创建用户错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 更新用户
 * PUT /api/users/:id
 */
router.put('/:id',
  authenticateToken,
  PermissionMiddleware.canUpdateUser,
  [
    body('username')
      .optional()
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    body('role')
      .optional()
      .isIn(Object.values(USER_ROLES))
      .withMessage('无效的用户角色'),
    body('real_name')
      .optional()
      .isLength({ max: 100 })
      .withMessage('真实姓名不能超过100个字符'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('邮箱格式不正确'),
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('手机号格式不正确'),
    body('status')
      .optional()
      .isIn(['active', 'inactive', 'banned'])
      .withMessage('无效的用户状态')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const userId = parseInt(req.params.id);
      const updateData = req.body;

      if (!userId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的用户ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 检查用户是否存在
      const existingUser = await UserModel.findById(userId);
      if (!existingUser) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('用户不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 不能修改自己的状态和角色
      if (userId === req.user.id) {
        delete updateData.status;
        delete updateData.role;
      }

      // 如果更新用户名，检查是否重复
      if (updateData.username && updateData.username !== existingUser.username) {
        const duplicateUser = await UserModel.findByUsername(updateData.username);
        if (duplicateUser) {
          return res.status(HTTP_STATUS.CONFLICT).json(
            ResponseUtils.error('用户名已存在', HTTP_STATUS.CONFLICT)
          );
        }
      }

      // 更新用户
      await UserModel.update(userId, updateData);

      // 获取更新后的用户信息
      const updatedUser = await UserModel.findById(userId);
      delete updatedUser.password;

      res.json(ResponseUtils.success(updatedUser, '用户更新成功'));

    } catch (error) {
      console.error('更新用户错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 重置用户密码
 * POST /api/users/:id/reset-password
 */
router.post('/:id/reset-password',
  authenticateToken,
  RoleMiddleware.requireAdmin,
  [
    body('newPassword')
      .isLength({ min: 6 })
      .withMessage('新密码长度至少6个字符')
  ],
  async (req, res) => {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const userId = parseInt(req.params.id);
      const { newPassword } = req.body;

      if (!userId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的用户ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 检查用户是否存在
      const user = await UserModel.findById(userId);
      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('用户不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 加密新密码 - 使用较低的saltRounds减少CPU负载
      const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // 更新密码
      await UserModel.update(userId, { password: hashedPassword });

      // 邮件通知功能已移除

      res.json(ResponseUtils.success(null, '密码重置成功'));

    } catch (error) {
      console.error('重置密码错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 删除用户
 * DELETE /api/users/:id
 */
router.delete('/:id',
  authenticateToken,
  RoleMiddleware.requireAdmin,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.id);

      if (!userId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的用户ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 不能删除自己
      if (userId === req.user.id) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('不能删除自己的账号', HTTP_STATUS.FORBIDDEN)
        );
      }

      // 检查用户是否存在
      const user = await UserModel.findById(userId);
      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('用户不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 删除用户
      await UserModel.delete(userId);

      res.json(ResponseUtils.success(null, '用户删除成功'));

    } catch (error) {
      console.error('删除用户错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 上传用户头像
 * POST /api/users/avatar
 */
router.post('/avatar',
  authenticateToken,
  avatarUpload.single('avatar'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('请选择要上传的头像文件', HTTP_STATUS.BAD_REQUEST)
        );
      }

      const userId = req.user.id;

      // 验证图片文件
      const validation = await validateImage(req.file.buffer);
      if (!validation.isValid) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error(validation.errors.join(', '), HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 生成文件名
      const timestamp = Date.now(); // 保持数字时间戳用于文件名
      const baseFilename = `avatar_${userId}_${timestamp}`;
      const uploadDir = path.join(__dirname, '../../uploads/avatars');

      // 确保上传目录存在
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // 生成多个尺寸的头像
      const result = await generateAvatarSizes(req.file.buffer, baseFilename, uploadDir);

      if (!result.success) {
        return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
          ResponseUtils.error(`图片处理失败: ${result.error}`, HTTP_STATUS.INTERNAL_SERVER_ERROR)
        );
      }

      // 使用中等尺寸作为主头像
      const avatarPath = result.results.medium.url;

      // 获取用户当前头像，用于删除旧文件
      const user = await UserModel.findById(userId);
      const oldAvatarPath = user?.avatar;

      // 更新用户头像路径
      await UserModel.updateAvatar(userId, avatarPath);

      // 删除旧头像文件（如果存在且不是默认头像）
      if (oldAvatarPath && oldAvatarPath.startsWith('/uploads/avatars/')) {
        const oldFilePath = path.join(__dirname, '../../', oldAvatarPath);
        if (fs.existsSync(oldFilePath)) {
          fs.unlinkSync(oldFilePath);
        }
      }

      // 缓存相关代码已移除，系统直接从数据库获取最新数据

      res.json(ResponseUtils.success({
        avatar: avatarPath,
        sizes: result.results,
        message: '头像上传成功',
        metadata: {
          originalSize: req.file.size,
          processedSizes: Object.keys(result.results).map(size => ({
            size,
            ...result.results[size].metadata
          }))
        }
      }));

    } catch (error) {
      console.error('头像上传错误:', error);

      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('头像上传失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取用户头像
 * GET /api/users/avatar/:filename
 */
router.get('/avatar/:filename',
  (req, res) => {
    try {
      const filename = req.params.filename;
      const filePath = path.join(__dirname, '../../uploads/avatars', filename);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('头像文件不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 设置缓存头
      res.set({
        'Cache-Control': 'public, max-age=86400', // 缓存1天
        'ETag': `"${filename}"`
      });

      // 发送文件
      res.sendFile(filePath);

    } catch (error) {
      console.error('获取头像错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('获取头像失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

module.exports = router;
