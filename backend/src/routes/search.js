/**
 * 高级搜索路由
 * 提供增强的搜索和筛选功能
 */

const express = require('express');
const { query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { ContractModel, UserModel } = require('../utils/database');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS, CONTRACT_STATUS, USER_ROLES } = require('../utils/constants');
// 缓存相关导入已移除，系统直接从数据库获取数据

const router = express.Router();

/**
 * 高级合同搜索
 * GET /api/search/contracts
 */
router.get('/contracts',
  authenticateToken,
  [
    query('keyword').optional().isString().withMessage('关键词必须是字符串'),
    query('status').optional().isIn(['pending', 'approved', 'rejected']).withMessage('状态无效'),
    query('submitterId').optional().isInt({ min: 1 }).withMessage('提交人ID必须是正整数'),
    query('reviewerId').optional().isInt({ min: 1 }).withMessage('审核人ID必须是正整数'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确'),
    query('minSize').optional().isInt({ min: 0 }).withMessage('最小文件大小必须是非负整数'),
    query('maxSize').optional().isInt({ min: 0 }).withMessage('最大文件大小必须是非负整数'),
    query('fileType').optional().isString().withMessage('文件类型必须是字符串'),
    query('sortBy').optional().isIn(['created_at', 'updated_at', 'file_size', 'serial_number']).withMessage('排序字段无效'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('排序顺序无效'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页条数必须在1-100之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const {
        keyword,
        status,
        submitterId,
        reviewerId,
        startDate,
        endDate,
        minSize,
        maxSize,
        fileType,
        sortBy = 'created_at',
        sortOrder = 'desc',
        page = 1,
        limit = 20
      } = req.query;

      // 构建搜索条件
      const searchParams = {
        keyword,
        status,
        submitterId: submitterId ? parseInt(submitterId) : undefined,
        reviewerId: reviewerId ? parseInt(reviewerId) : undefined,
        startDate,
        endDate,
        minSize: minSize ? parseInt(minSize) : undefined,
        maxSize: maxSize ? parseInt(maxSize) : undefined,
        fileType,
        sortBy,
        sortOrder,
        page: parseInt(page),
        limit: parseInt(limit),
        currentUserId: req.user.id,
        userRole: req.user.role
      };

      // 权限检查：普通员工只能搜索自己的合同
      if (req.user.role === USER_ROLES.EMPLOYEE) {
        searchParams.submitterId = req.user.id;
      }

      // 审核员只能搜索分配给自己的合同
      if (req.user.role === USER_ROLES.COUNTY_REVIEWER || req.user.role === USER_ROLES.CITY_REVIEWER) {
        searchParams.reviewerId = req.user.id;
      }

      const result = await ContractModel.advancedSearch(searchParams);

      // 缓存事件系统已移除，记录搜索日志

      res.json(ResponseUtils.success(result));

    } catch (error) {
      console.error('高级搜索错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('搜索失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 用户搜索（管理员专用）
 * GET /api/search/users
 */
router.get('/users',
  authenticateToken,
  [
    query('keyword').optional().isString().withMessage('关键词必须是字符串'),
    query('role').optional().isIn(['employee', 'county_reviewer', 'city_reviewer', 'admin']).withMessage('角色无效'),
    query('status').optional().isIn(['active', 'banned']).withMessage('状态无效'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确'),
    query('sortBy').optional().isIn(['created_at', 'updated_at', 'username']).withMessage('排序字段无效'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('排序顺序无效'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页条数必须在1-100之间')
  ],
  async (req, res) => {
    try {
      // 只有管理员可以搜索用户
      if (req.user.role !== USER_ROLES.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
        );
      }

      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const {
        keyword,
        role,
        status,
        startDate,
        endDate,
        sortBy = 'created_at',
        sortOrder = 'desc',
        page = 1,
        limit = 20
      } = req.query;

      const searchParams = {
        keyword,
        role,
        status,
        startDate,
        endDate,
        sortBy,
        sortOrder,
        page: parseInt(page),
        limit: parseInt(limit)
      };

      const result = await UserModel.advancedSearch(searchParams);

      // 缓存事件系统已移除，记录搜索日志

      res.json(ResponseUtils.success(result));

    } catch (error) {
      console.error('用户搜索错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('搜索失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 搜索建议
 * GET /api/search/suggestions
 */
router.get('/suggestions',
  authenticateToken,
  [
    query('type').isIn(['contract', 'user']).withMessage('类型必须是contract或user'),
    query('keyword').isString().isLength({ min: 1, max: 50 }).withMessage('关键词长度必须在1-50个字符之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
        );
      }

      const { type, keyword } = req.query;

      let suggestions = [];

      if (type === 'contract') {
        suggestions = await ContractModel.getSearchSuggestions(keyword, {
          currentUserId: req.user.id,
          userRole: req.user.role
        });
      } else if (type === 'user' && req.user.role === USER_ROLES.ADMIN) {
        suggestions = await UserModel.getSearchSuggestions(keyword);
      }

      res.json(ResponseUtils.success(suggestions));

    } catch (error) {
      console.error('搜索建议错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('获取搜索建议失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 搜索统计
 * GET /api/search/stats
 */
router.get('/stats',
  authenticateToken,
  async (req, res) => {
    try {
      const stats = {
        totalContracts: 0,
        contractsByStatus: {},
        contractsByMonth: [],
        topSubmitters: [],
        topReviewers: []
      };

      // 根据用户角色获取统计数据
      if (req.user.role === USER_ROLES.ADMIN) {
        // 管理员可以查看所有统计
        stats.totalContracts = await ContractModel.getTotalCount();
        stats.contractsByStatus = await ContractModel.getCountByStatus();
        stats.contractsByMonth = await ContractModel.getCountByMonth();
        stats.topSubmitters = await ContractModel.getTopSubmitters();
        stats.topReviewers = await ContractModel.getTopReviewers();
      } else if ([USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(req.user.role)) {
        // 审核员只能查看自己的统计
        stats.totalContracts = await ContractModel.getTotalCountByReviewer(req.user.id);
        stats.contractsByStatus = await ContractModel.getCountByStatusAndReviewer(req.user.id);
        stats.contractsByMonth = await ContractModel.getCountByMonthAndReviewer(req.user.id);
      } else if (req.user.role === USER_ROLES.EMPLOYEE) {
        // 员工只能查看自己的统计
        stats.totalContracts = await ContractModel.getTotalCountBySubmitter(req.user.id);
        stats.contractsByStatus = await ContractModel.getCountByStatusAndSubmitter(req.user.id);
        stats.contractsByMonth = await ContractModel.getCountByMonthAndSubmitter(req.user.id);
      } else if (req.user.role === USER_ROLES.LEGAL_OFFICER) {
        // 法规员不提供搜索统计数据
        stats.message = '法规员专注于合同编号分配工作，不提供搜索统计数据';
        stats.role = '市局法规员';
      }

      res.json(ResponseUtils.success(stats));

    } catch (error) {
      console.error('搜索统计错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('获取搜索统计失败', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

module.exports = router;