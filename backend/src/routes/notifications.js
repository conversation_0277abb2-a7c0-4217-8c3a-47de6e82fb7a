/**
 * 通知管理路由
 * 处理通知相关的API请求
 */

const express = require('express');
const router = express.Router();

const { NotificationModel } = require('../utils/database');
const { authenticateToken } = require('../middleware/auth');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES } = require('../utils/constants');

/**
 * 获取当前用户的通知列表
 * GET /api/notifications
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      pageSize = 20,
      type,
      is_read
    } = req.query;

    // 构建筛选条件
    const filters = {};
    if (type) filters.type = type;
    if (is_read !== undefined) filters.is_read = is_read === 'true';

    // 获取通知列表
    const result = await NotificationModel.getByUserId(
      userId,
      filters,
      parseInt(page),
      parseInt(pageSize)
    );

    res.json(ResponseUtils.success(result, '获取通知列表成功'));
  } catch (error) {
    console.error('获取通知列表失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取未读通知数量
 * GET /api/notifications/unread-count
 */
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const count = await NotificationModel.getUnreadCount(userId);

    res.json(ResponseUtils.success({ count }, '获取未读通知数量成功'));
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取通知统计信息
 * GET /api/notifications/stats
 */
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const stats = await NotificationModel.getStats(userId);

    res.json(ResponseUtils.success(stats, '获取通知统计成功'));
  } catch (error) {
    console.error('获取通知统计失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 标记指定通知为已读
 * POST /api/notifications/:id/read
 */
router.post('/:id/read', authenticateToken, async (req, res) => {
  try {
    const notificationId = req.params.id;
    const userId = req.user.id;

    // 验证通知是否存在且属于当前用户
    const notification = await NotificationModel.findById(notificationId);
    if (!notification) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('通知不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

    if (notification.user_id !== userId) {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('无权限操作此通知', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 标记为已读
    const result = await NotificationModel.markAsRead(notificationId, userId);

    if (result.changes === 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('通知已经是已读状态', HTTP_STATUS.BAD_REQUEST)
      );
    }

    res.json(ResponseUtils.success(null, '通知已标记为已读'));
  } catch (error) {
    console.error('标记通知已读失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 标记所有通知为已读
 * POST /api/notifications/read-all
 */
router.post('/read-all', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const result = await NotificationModel.markAllAsRead(userId);

    res.json(ResponseUtils.success(
      { updatedCount: result.changes },
      `已标记 ${result.changes} 条通知为已读`
    ));
  } catch (error) {
    console.error('标记所有通知已读失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 删除指定通知
 * DELETE /api/notifications/:id
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const notificationId = req.params.id;
    const userId = req.user.id;

    // 验证通知是否存在且属于当前用户
    const notification = await NotificationModel.findById(notificationId);
    if (!notification) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('通知不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

    if (notification.user_id !== userId) {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('无权限删除此通知', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 删除通知
    await NotificationModel.delete(notificationId, userId);

    res.json(ResponseUtils.success(null, '通知删除成功'));
  } catch (error) {
    console.error('删除通知失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 创建通知（仅供内部使用，管理员权限）
 * POST /api/notifications
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    // 检查权限（仅管理员可以创建通知）
    if (req.user.role !== 'admin') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('权限不足', HTTP_STATUS.FORBIDDEN)
      );
    }

    const { user_id, type, title, content, related_id, related_type } = req.body;

    // 验证必填字段
    if (!user_id || !type || !title || !content) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('缺少必填字段', HTTP_STATUS.BAD_REQUEST)
      );
    }

    // 验证通知类型
    const validTypes = ['contract_submitted', 'contract_approved', 'contract_rejected', 'contract_assigned', 'system_notice'];
    if (!validTypes.includes(type)) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('无效的通知类型', HTTP_STATUS.BAD_REQUEST)
      );
    }

    // 创建通知
    const result = await NotificationModel.create({
      user_id,
      type,
      title,
      content,
      related_id,
      related_type
    });

    res.status(HTTP_STATUS.CREATED).json(
      ResponseUtils.success({ id: result.id }, '通知创建成功')
    );
  } catch (error) {
    console.error('创建通知失败:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

module.exports = router;
