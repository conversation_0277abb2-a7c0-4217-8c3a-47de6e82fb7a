/**
 * 角色管理路由
 * 提供角色相关的API端点
 */

const express = require('express');
const router = express.Router();

const { RoleModel, PermissionModel, UserPermissionModel } = require('../models/rbac');
const { authenticateToken } = require('../middleware/auth');
const { requireRole, requirePermission } = require('../middleware/rbac');
const { ResponseUtils, ValidationUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES, USER_ROLES } = require('../utils/constants');
const { logOperation, ACTIONS, RESOURCE_TYPES } = require('../utils/logger');

/**
 * 获取所有角色
 * GET /api/roles
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const roles = await RoleModel.findAll();

    res.json(ResponseUtils.success(roles, '获取角色列表成功'));

  } catch (error) {
    console.error('获取角色列表错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取角色层级结构
 * GET /api/roles/hierarchy
 */
router.get('/hierarchy', authenticateToken, async (req, res) => {
  try {
    const roles = await RoleModel.findAll();

    // 按层级排序并构建层级结构
    const hierarchy = roles.sort((a, b) => a.level - b.level).map(role => ({
      id: role.id,
      name: role.name,
      display_name: role.display_name,
      description: role.description,
      level: role.level,
      is_active: role.is_active
    }));

    res.json(ResponseUtils.success(hierarchy, '获取角色层级结构成功'));

  } catch (error) {
    console.error('获取角色层级结构错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 获取角色统计信息
 * GET /api/roles/stats
 */
router.get('/stats',
  authenticateToken,
  requireRole(['admin']),
  async (req, res) => {
    try {
      const roles = await RoleModel.findAll();
      const stats = {
        total_roles: roles.length,
        roles_by_level: {},
        active_roles: roles.filter(r => r.is_active).length
      };

      // 按层级统计
      roles.forEach(role => {
        const level = role.level || 0;
        stats.roles_by_level[level] = (stats.roles_by_level[level] || 0) + 1;
      });

      res.json(ResponseUtils.success(stats, '获取角色统计信息成功'));

    } catch (error) {
      console.error('获取角色统计信息错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取角色详情
 * GET /api/roles/:id
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const roleId = parseInt(req.params.id);

    if (!roleId || roleId <= 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error('无效的角色ID', HTTP_STATUS.BAD_REQUEST)
      );
    }

    const role = await RoleModel.findById(roleId);

    if (!role) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseUtils.error('角色不存在', HTTP_STATUS.NOT_FOUND)
      );
    }

    // 获取角色权限
    const permissions = await PermissionModel.findByRoleId(roleId);

    res.json(ResponseUtils.success({
      ...role,
      permissions
    }, '获取角色详情成功'));

  } catch (error) {
    console.error('获取角色详情错误:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
});

/**
 * 创建角色 (仅管理员)
 * POST /api/roles
 */
router.post('/',
  authenticateToken,
  requireRole('admin'),
  async (req, res) => {
    try {
      const { name, display_name, description, level } = req.body;

      // 验证必填字段
      const validation = ValidationUtils.validateRequired({
        name,
        display_name
      });

      if (!validation.isValid) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error(validation.message, HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 检查角色名是否已存在
      const existingRole = await RoleModel.findByName(name);
      if (existingRole) {
        return res.status(HTTP_STATUS.CONFLICT).json(
          ResponseUtils.error('角色名已存在', HTTP_STATUS.CONFLICT)
        );
      }

      // 创建角色
      const result = await RoleModel.create({
        name,
        display_name,
        description,
        level: level || 0
      });

      // 记录操作日志
      await logOperation(req.user.id, ACTIONS.CREATE, RESOURCE_TYPES.ROLE, result.lastID, {
        role_name: name,
        display_name
      });

      const newRole = await RoleModel.findById(result.lastID);

      res.status(HTTP_STATUS.CREATED).json(
        ResponseUtils.success(newRole, '角色创建成功')
      );

    } catch (error) {
      console.error('创建角色错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 更新角色 (仅管理员)
 * PUT /api/roles/:id
 */
router.put('/:id',
  authenticateToken,
  requireRole('admin'),
  async (req, res) => {
    try {
      const roleId = parseInt(req.params.id);
      const { display_name, description, level } = req.body;

      if (!roleId || roleId <= 0) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的角色ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 检查角色是否存在
      const existingRole = await RoleModel.findById(roleId);
      if (!existingRole) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('角色不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 更新角色
      await RoleModel.update(roleId, {
        display_name,
        description,
        level
      });

      // 记录操作日志
      await logOperation(req.user.id, ACTIONS.UPDATE, RESOURCE_TYPES.ROLE, roleId, {
        old_data: existingRole,
        new_data: { display_name, description, level }
      });

      const updatedRole = await RoleModel.findById(roleId);

      res.json(ResponseUtils.success(updatedRole, '角色更新成功'));

    } catch (error) {
      console.error('更新角色错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 删除角色 (仅管理员)
 * DELETE /api/roles/:id
 */
router.delete('/:id',
  authenticateToken,
  requireRole('admin'),
  async (req, res) => {
    try {
      const roleId = parseInt(req.params.id);

      if (!roleId || roleId <= 0) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('无效的角色ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 检查角色是否存在
      const existingRole = await RoleModel.findById(roleId);
      if (!existingRole) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error('角色不存在', HTTP_STATUS.NOT_FOUND)
        );
      }

      // 检查是否有用户使用此角色
      const usersWithRole = await UserPermissionModel.getUsersByRoleId(roleId);
      if (usersWithRole.length > 0) {
        return res.status(HTTP_STATUS.CONFLICT).json(
          ResponseUtils.error('无法删除角色，仍有用户使用此角色', HTTP_STATUS.CONFLICT)
        );
      }

      // 软删除角色
      await RoleModel.delete(roleId);

      // 记录操作日志
      await logOperation(req.user.id, ACTIONS.DELETE, RESOURCE_TYPES.ROLE, roleId, {
        role_data: existingRole
      });

      res.json(ResponseUtils.success(null, '角色删除成功'));

    } catch (error) {
      console.error('删除角色错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

module.exports = router;
