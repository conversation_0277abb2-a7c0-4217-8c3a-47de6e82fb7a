#!/bin/bash
# 环境配置示例文件 - 不是可执行脚本
# 复制此文件为 .env 并根据实际情况修改配置

# 应用环境
export NODE_ENV=development

# 服务器配置
export PORT=3000
export HOST=0.0.0.0

# 前端URL配置（用于CORS）
# 如果前端和后端在同一服务器上，通常不需要设置此项
# 如果在不同服务器或需要特定配置，请设置完整的URL
# FRONTEND_URL=http://your-domain.com:5173
# FRONTEND_URL=http://localhost:5173

# SQLite数据库配置
# 数据库文件路径（相对于backend目录）
export DB_PATH=database.sqlite

# JWT密钥
export JWT_SECRET=your-super-secret-jwt-key-here

# 密码加密配置
# BCRYPT_ROUNDS: 加密轮数，影响CPU使用和安全性
# 开发环境建议4，生产环境建议6-8，高性能服务器可用10
# 注意：数值越高越安全但CPU消耗越大
export BCRYPT_ROUNDS=6

# 文件上传配置
export UPLOAD_PATH=uploads
export MAX_FILE_SIZE=5242880
