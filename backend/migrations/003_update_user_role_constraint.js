#!/usr/bin/env node

/**
 * 数据库迁移脚本 - 更新用户表角色约束
 * 
 * 此迁移将：
 * 1. 更新 users 表的 role 字段约束，添加 legal_officer
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🚀 开始执行用户角色约束更新迁移...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 主迁移函数
async function runMigration() {
  try {
    // 检查是否已经迁移过
    const migrationCheck = await checkMigrationStatus();
    if (migrationCheck.completed) {
      console.log('⚠️  用户角色约束更新迁移已完成，跳过执行');
      return;
    }

    // 禁用外键约束进行表重建
    await runQuery('PRAGMA foreign_keys = OFF');
    console.log('✅ 禁用外键约束');

    // 更新用户表角色约束
    await updateUserRoleConstraint();

    // 重新启用外键约束
    await runQuery('PRAGMA foreign_keys = ON');
    console.log('✅ 重新启用外键约束');

    // 记录迁移完成
    await recordMigration();

    console.log('🎉 用户角色约束更新迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 检查迁移状态
async function checkMigrationStatus() {
  try {
    const migration = await getQuery(`
      SELECT * FROM migrations 
      WHERE migration_name = 'update_user_role_constraint'
    `);

    return { completed: !!migration };
  } catch (error) {
    return { completed: false };
  }
}

// 更新用户表角色约束
async function updateUserRoleConstraint() {
  try {
    console.log('📝 开始更新用户表角色约束...');

    // 由于SQLite不支持直接修改CHECK约束，我们需要重建表

    // 0. 删除可能存在的临时表
    try {
      await runQuery('DROP TABLE IF EXISTS users_new');
      console.log('✅ 清理临时表');
    } catch (error) {
      // 忽略错误
    }

    // 1. 创建新的用户表
    await runQuery(`
      CREATE TABLE users_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('employee', 'county_reviewer', 'city_reviewer', 'legal_officer', 'admin')),
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'banned')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        role_id INTEGER,
        FOREIGN KEY (created_by) REFERENCES users(id),
        FOREIGN KEY (role_id) REFERENCES roles(id)
      )
    `);
    console.log('✅ 创建新用户表');

    // 2. 复制数据
    await runQuery(`
      INSERT INTO users_new (id, username, password, role, status, created_at, updated_at, created_by, role_id)
      SELECT id, username, password, role, status, created_at, updated_at, created_by, role_id
      FROM users
    `);
    console.log('✅ 复制用户数据');

    // 3. 删除旧表
    await runQuery('DROP TABLE users');
    console.log('✅ 删除旧用户表');

    // 4. 重命名新表
    await runQuery('ALTER TABLE users_new RENAME TO users');
    console.log('✅ 重命名新用户表');

    // 5. 重建索引
    await runQuery('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)');
    console.log('✅ 重建用户表索引');

  } catch (error) {
    console.error('❌ 更新用户表角色约束失败:', error.message);
    throw error;
  }
}

// 记录迁移完成
async function recordMigration() {
  await runQuery(`
    INSERT OR IGNORE INTO migrations (migration_name, version)
    VALUES ('update_user_role_constraint', '1.0.0')
  `);
  console.log('✅ 记录迁移完成');
}

// 执行迁移
if (require.main === module) {
  runMigration();
}

module.exports = {
  runMigration
};
