#!/usr/bin/env node

/**
 * 数据库迁移脚本 - 更新合同审核历史表约束
 * 
 * 此迁移将：
 * 1. 更新 contract_reviews 表的 reviewer_role 约束，添加 'legal_officer'
 * 2. 更新 contract_reviews 表的 review_level 约束，添加 'legal_officer'
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🚀 开始执行合同审核历史表约束更新迁移...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// 包装数据库操作为Promise
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 检查迁移状态
async function checkMigrationStatus() {
  try {
    const result = await getQuery(`
      SELECT * FROM migrations 
      WHERE migration_name = 'update_contract_reviews_constraint'
    `);
    return { completed: !!result };
  } catch (error) {
    console.log('ℹ️  migrations表不存在或查询失败，继续执行迁移');
    return { completed: false };
  }
}

// 主迁移函数
async function runMigration() {
  try {
    // 检查是否已经迁移过
    const migrationCheck = await checkMigrationStatus();
    if (migrationCheck.completed) {
      console.log('⚠️  合同审核历史表约束更新迁移已完成，跳过执行');
      return;
    }

    // 禁用外键约束进行表重建
    await runQuery('PRAGMA foreign_keys = OFF');
    console.log('✅ 禁用外键约束');

    // 更新合同审核历史表约束
    await updateContractReviewsConstraint();

    // 重新启用外键约束
    await runQuery('PRAGMA foreign_keys = ON');
    console.log('✅ 重新启用外键约束');

    // 记录迁移完成
    await recordMigration();

    console.log('🎉 合同审核历史表约束更新迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 更新合同审核历史表约束
async function updateContractReviewsConstraint() {
  try {
    console.log('📝 开始更新合同审核历史表约束...');
    
    // 1. 创建新的合同审核历史表
    await runQuery(`
      CREATE TABLE contract_reviews_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contract_id INTEGER NOT NULL,
        reviewer_id INTEGER NOT NULL,
        reviewer_role TEXT NOT NULL CHECK (reviewer_role IN ('county_reviewer', 'city_reviewer', 'legal_officer', 'admin')),
        result TEXT NOT NULL CHECK (result IN ('approved', 'rejected')),
        comment TEXT,
        review_level TEXT NOT NULL CHECK (review_level IN ('county_reviewer', 'city_reviewer', 'legal_officer')),
        created_at DATETIME NOT NULL,
        FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewer_id) REFERENCES users(id)
      )
    `);
    console.log('✅ 创建新合同审核历史表');

    // 2. 复制数据
    await runQuery(`
      INSERT INTO contract_reviews_new (
        id, contract_id, reviewer_id, reviewer_role, result, comment, review_level, created_at
      )
      SELECT 
        id, contract_id, reviewer_id, reviewer_role, result, comment, review_level, created_at
      FROM contract_reviews
    `);
    console.log('✅ 复制合同审核历史数据');

    // 3. 删除旧表
    await runQuery('DROP TABLE contract_reviews');
    console.log('✅ 删除旧合同审核历史表');

    // 4. 重命名新表
    await runQuery('ALTER TABLE contract_reviews_new RENAME TO contract_reviews');
    console.log('✅ 重命名新合同审核历史表');

    // 5. 重建索引
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contract_reviews_contract ON contract_reviews(contract_id)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contract_reviews_reviewer ON contract_reviews(reviewer_id)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contract_reviews_created ON contract_reviews(created_at)');
    console.log('✅ 重建合同审核历史表索引');

  } catch (error) {
    console.error('❌ 更新合同审核历史表约束失败:', error.message);
    throw error;
  }
}

// 记录迁移完成
async function recordMigration() {
  await runQuery(`
    INSERT OR IGNORE INTO migrations (migration_name, version)
    VALUES ('update_contract_reviews_constraint', '1.0.0')
  `);
  console.log('✅ 记录迁移完成');
}

// 执行迁移
if (require.main === module) {
  runMigration();
}

module.exports = {
  runMigration
};
