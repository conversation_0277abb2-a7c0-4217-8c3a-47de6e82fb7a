#!/usr/bin/env node

/**
 * 数据库迁移脚本 - 更新合同表状态约束
 * 
 * 此迁移将：
 * 1. 更新 contracts 表的 status 字段约束，添加新状态
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🚀 开始执行合同状态约束更新迁移...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 主迁移函数
async function runMigration() {
  try {
    // 检查是否已经迁移过
    const migrationCheck = await checkMigrationStatus();
    if (migrationCheck.completed) {
      console.log('⚠️  合同状态约束更新迁移已完成，跳过执行');
      return;
    }

    // 禁用外键约束进行表重建
    await runQuery('PRAGMA foreign_keys = OFF');
    console.log('✅ 禁用外键约束');

    // 更新合同表状态约束
    await updateContractStatusConstraint();

    // 重新启用外键约束
    await runQuery('PRAGMA foreign_keys = ON');
    console.log('✅ 重新启用外键约束');

    // 记录迁移完成
    await recordMigration();

    console.log('🎉 合同状态约束更新迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 检查迁移状态
async function checkMigrationStatus() {
  try {
    const migration = await getQuery(`
      SELECT * FROM migrations 
      WHERE migration_name = 'update_contract_status_constraint'
    `);

    return { completed: !!migration };
  } catch (error) {
    return { completed: false };
  }
}

// 更新合同表状态约束
async function updateContractStatusConstraint() {
  try {
    console.log('📝 开始更新合同表状态约束...');
    
    // 删除可能存在的临时表
    try {
      await runQuery('DROP TABLE IF EXISTS contracts_new');
      console.log('✅ 清理临时表');
    } catch (error) {
      // 忽略错误
    }
    
    // 1. 创建新的合同表
    await runQuery(`
      CREATE TABLE contracts_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        serial_number TEXT UNIQUE NOT NULL,
        submitter_id INTEGER NOT NULL,
        reviewer_id INTEGER,
        filename TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'pending_city_review', 'pending_contract_number', 'approved', 'completed', 'rejected')),
        submit_note TEXT,
        review_comment TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        reviewed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        review_level TEXT,
        contract_number TEXT,
        legal_officer_id INTEGER,
        contract_number_assigned_at DATETIME,
        FOREIGN KEY (submitter_id) REFERENCES users(id),
        FOREIGN KEY (reviewer_id) REFERENCES users(id),
        FOREIGN KEY (legal_officer_id) REFERENCES users(id)
      )
    `);
    console.log('✅ 创建新合同表');

    // 2. 复制数据
    await runQuery(`
      INSERT INTO contracts_new (
        id, serial_number, submitter_id, reviewer_id, filename, file_path, file_size,
        status, submit_note, review_comment, submitted_at, reviewed_at, created_at, updated_at,
        review_level, contract_number, legal_officer_id, contract_number_assigned_at
      )
      SELECT 
        id, serial_number, submitter_id, reviewer_id, filename, file_path, file_size,
        status, submit_note, review_comment, submitted_at, reviewed_at, created_at, updated_at,
        review_level, contract_number, legal_officer_id, contract_number_assigned_at
      FROM contracts
    `);
    console.log('✅ 复制合同数据');

    // 3. 删除旧表
    await runQuery('DROP TABLE contracts');
    console.log('✅ 删除旧合同表');

    // 4. 重命名新表
    await runQuery('ALTER TABLE contracts_new RENAME TO contracts');
    console.log('✅ 重命名新合同表');

    // 5. 重建索引
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_serial ON contracts(serial_number)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_submitter ON contracts(submitter_id)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_reviewer ON contracts(reviewer_id)');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(status)');
    await runQuery('CREATE UNIQUE INDEX IF NOT EXISTS idx_contracts_contract_number_unique ON contracts(contract_number) WHERE contract_number IS NOT NULL');
    await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_legal_officer ON contracts(legal_officer_id)');
    console.log('✅ 重建合同表索引');

  } catch (error) {
    console.error('❌ 更新合同表状态约束失败:', error.message);
    throw error;
  }
}

// 记录迁移完成
async function recordMigration() {
  await runQuery(`
    INSERT OR IGNORE INTO migrations (migration_name, version)
    VALUES ('update_contract_status_constraint', '1.0.0')
  `);
  console.log('✅ 记录迁移完成');
}

// 执行迁移
if (require.main === module) {
  runMigration();
}

module.exports = {
  runMigration
};
