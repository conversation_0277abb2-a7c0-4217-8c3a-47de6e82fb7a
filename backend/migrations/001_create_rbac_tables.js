#!/usr/bin/env node

/**
 * 数据库迁移脚本 - 创建RBAC（基于角色的访问控制）表结构
 * 
 * 此迁移将创建：
 * 1. roles 表 - 角色定义
 * 2. permissions 表 - 权限定义  
 * 3. role_permissions 表 - 角色权限关联
 * 4. 更新 users 表添加 role_id 外键
 * 5. 插入默认角色和权限数据
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🚀 开始执行RBAC数据库迁移...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 主迁移函数
async function runMigration() {
  try {
    // 启用外键约束
    await runQuery('PRAGMA foreign_keys = ON');
    console.log('✅ 启用外键约束');

    // 检查是否已经迁移过
    const migrationCheck = await checkMigrationStatus();
    if (migrationCheck.completed) {
      console.log('⚠️  RBAC迁移已完成，跳过执行');
      return;
    }

    // 1. 创建角色表
    await createRolesTable();

    // 2. 创建权限表
    await createPermissionsTable();

    // 3. 创建角色权限关联表
    await createRolePermissionsTable();

    // 4. 创建迁移记录表
    await createMigrationsTable();

    // 5. 插入默认角色数据
    await insertDefaultRoles();

    // 6. 插入默认权限数据
    await insertDefaultPermissions();

    // 7. 设置角色权限关联
    await setupRolePermissions();

    // 8. 更新现有用户的角色关联
    await updateExistingUsers();

    // 9. 记录迁移完成
    await recordMigration();

    console.log('🎉 RBAC数据库迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 检查迁移状态
async function checkMigrationStatus() {
  try {
    const result = await getQuery(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='migrations'
    `);

    if (!result) {
      return { completed: false };
    }

    const migration = await getQuery(`
      SELECT * FROM migrations 
      WHERE migration_name = 'create_rbac_tables'
    `);

    return { completed: !!migration };
  } catch (error) {
    return { completed: false };
  }
}

// 创建角色表
async function createRolesTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS roles (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      display_name TEXT NOT NULL,
      description TEXT,
      level INTEGER DEFAULT 0,
      is_active BOOLEAN DEFAULT TRUE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建角色表 (roles)');

  // 创建索引
  await runQuery('CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_roles_level ON roles(level)');
  console.log('✅ 创建角色表索引');
}

// 创建权限表
async function createPermissionsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS permissions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      action TEXT NOT NULL,
      subject TEXT NOT NULL,
      description TEXT,
      category TEXT DEFAULT 'general',
      is_active BOOLEAN DEFAULT TRUE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建权限表 (permissions)');

  // 创建索引
  await runQuery('CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(name)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_permissions_action_subject ON permissions(action, subject)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_permissions_category ON permissions(category)');
  console.log('✅ 创建权限表索引');
}

// 创建角色权限关联表
async function createRolePermissionsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS role_permissions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      role_id INTEGER NOT NULL,
      permission_id INTEGER NOT NULL,
      granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      granted_by INTEGER,
      FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
      FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
      FOREIGN KEY (granted_by) REFERENCES users(id),
      UNIQUE(role_id, permission_id)
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建角色权限关联表 (role_permissions)');

  // 创建索引
  await runQuery('CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role_id)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_role_permissions_permission ON role_permissions(permission_id)');
  console.log('✅ 创建角色权限关联表索引');
}

// 创建迁移记录表
async function createMigrationsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      migration_name TEXT UNIQUE NOT NULL,
      executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      version TEXT DEFAULT '1.0.0'
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建迁移记录表 (migrations)');
}

// 插入默认角色数据
async function insertDefaultRoles() {
  const roles = [
    {
      name: 'employee',
      display_name: '员工',
      description: '普通员工，可以提交和查看自己的合同',
      level: 1
    },
    {
      name: 'county_reviewer',
      display_name: '县局审核员',
      description: '县级审核员，负责县级合同审核',
      level: 2
    },
    {
      name: 'city_reviewer',
      display_name: '市局审核员',
      description: '市级审核员，负责市级合同审核',
      level: 3
    },
    {
      name: 'admin',
      display_name: '超级管理员',
      description: '系统管理员，拥有所有权限',
      level: 4
    }
  ];

  for (const role of roles) {
    try {
      await runQuery(`
        INSERT OR IGNORE INTO roles (name, display_name, description, level)
        VALUES (?, ?, ?, ?)
      `, [role.name, role.display_name, role.description, role.level]);
      console.log(`✅ 插入角色: ${role.display_name}`);
    } catch (error) {
      console.error(`❌ 插入角色失败 ${role.name}:`, error.message);
    }
  }
}

// 插入默认权限数据
async function insertDefaultPermissions() {
  const permissions = [
    // 合同权限
    { name: 'contract:create', action: 'create', subject: 'contract', description: '创建合同', category: 'contract' },
    { name: 'contract:read', action: 'read', subject: 'contract', description: '查看合同', category: 'contract' },
    { name: 'contract:update', action: 'update', subject: 'contract', description: '更新合同', category: 'contract' },
    { name: 'contract:delete', action: 'delete', subject: 'contract', description: '删除合同', category: 'contract' },
    { name: 'contract:review', action: 'review', subject: 'contract', description: '审核合同', category: 'contract' },
    { name: 'contract:approve', action: 'approve', subject: 'contract', description: '批准合同', category: 'contract' },
    { name: 'contract:reject', action: 'reject', subject: 'contract', description: '拒绝合同', category: 'contract' },

    // 用户权限
    { name: 'user:create', action: 'create', subject: 'user', description: '创建用户', category: 'user' },
    { name: 'user:read', action: 'read', subject: 'user', description: '查看用户', category: 'user' },
    { name: 'user:update', action: 'update', subject: 'user', description: '更新用户', category: 'user' },
    { name: 'user:delete', action: 'delete', subject: 'user', description: '删除用户', category: 'user' },
    { name: 'user:manage', action: 'manage', subject: 'user', description: '管理用户', category: 'user' },
    { name: 'user:ban', action: 'ban', subject: 'user', description: '封禁用户', category: 'user' },

    // 系统权限
    { name: 'system:stats', action: 'read', subject: 'system_stats', description: '查看系统统计', category: 'system' },
    { name: 'system:manage', action: 'manage', subject: 'system', description: '系统管理', category: 'system' },
    { name: 'system:backup', action: 'backup', subject: 'system', description: '系统备份', category: 'system' },

    // 通知权限
    { name: 'notification:read', action: 'read', subject: 'notification', description: '查看通知', category: 'notification' },
    { name: 'notification:create', action: 'create', subject: 'notification', description: '创建通知', category: 'notification' },
    { name: 'notification:manage', action: 'manage', subject: 'notification', description: '管理通知', category: 'notification' }
  ];

  for (const permission of permissions) {
    try {
      await runQuery(`
        INSERT OR IGNORE INTO permissions (name, action, subject, description, category)
        VALUES (?, ?, ?, ?, ?)
      `, [permission.name, permission.action, permission.subject, permission.description, permission.category]);
      console.log(`✅ 插入权限: ${permission.description}`);
    } catch (error) {
      console.error(`❌ 插入权限失败 ${permission.name}:`, error.message);
    }
  }
}

// 设置角色权限关联
async function setupRolePermissions() {
  // 角色权限映射
  const rolePermissionMappings = {
    'employee': [
      'contract:create',
      'contract:read',
      'contract:update',
      'notification:read'
    ],
    'county_reviewer': [
      'contract:read',
      'contract:review',
      'contract:approve',
      'contract:reject',
      'notification:read',
      'notification:create'
    ],
    'city_reviewer': [
      'contract:read',
      'contract:review',
      'contract:approve',
      'contract:reject',
      'notification:read',
      'notification:create'
    ],
    'admin': [
      'contract:create',
      'contract:read',
      'contract:update',
      'contract:delete',
      'contract:review',
      'contract:approve',
      'contract:reject',
      'user:create',
      'user:read',
      'user:update',
      'user:delete',
      'user:manage',
      'user:ban',
      'system:stats',
      'system:manage',
      'system:backup',
      'notification:read',
      'notification:create',
      'notification:manage'
    ]
  };

  for (const [roleName, permissionNames] of Object.entries(rolePermissionMappings)) {
    // 获取角色ID
    const role = await getQuery('SELECT id FROM roles WHERE name = ?', [roleName]);
    if (!role) {
      console.error(`❌ 角色不存在: ${roleName}`);
      continue;
    }

    for (const permissionName of permissionNames) {
      // 获取权限ID
      const permission = await getQuery('SELECT id FROM permissions WHERE name = ?', [permissionName]);
      if (!permission) {
        console.error(`❌ 权限不存在: ${permissionName}`);
        continue;
      }

      try {
        await runQuery(`
          INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
          VALUES (?, ?)
        `, [role.id, permission.id]);
        console.log(`✅ 关联权限: ${roleName} -> ${permissionName}`);
      } catch (error) {
        console.error(`❌ 关联权限失败 ${roleName} -> ${permissionName}:`, error.message);
      }
    }
  }
}

// 更新现有用户的角色关联
async function updateExistingUsers() {
  console.log('📝 开始更新现有用户角色关联...');

  // 首先添加role_id列到users表（如果不存在）
  try {
    await runQuery('ALTER TABLE users ADD COLUMN role_id INTEGER REFERENCES roles(id)');
    console.log('✅ 添加role_id列到users表');
  } catch (error) {
    // 列可能已存在，忽略错误
    console.log('ℹ️  role_id列可能已存在');
  }

  // 获取所有现有用户
  const users = await allQuery('SELECT id, username, role FROM users');

  for (const user of users) {
    // 映射旧角色到新角色
    let newRoleName = user.role;
    if (user.role === 'reviewer') {
      // 默认将reviewer映射为county_reviewer
      newRoleName = 'county_reviewer';
    }

    // 获取新角色ID
    const role = await getQuery('SELECT id FROM roles WHERE name = ?', [newRoleName]);
    if (role) {
      await runQuery('UPDATE users SET role_id = ? WHERE id = ?', [role.id, user.id]);
      console.log(`✅ 更新用户 ${user.username} 角色: ${user.role} -> ${newRoleName}`);
    } else {
      console.error(`❌ 找不到角色: ${newRoleName} for user ${user.username}`);
    }
  }
}

// 记录迁移完成
async function recordMigration() {
  await runQuery(`
    INSERT OR IGNORE INTO migrations (migration_name, version)
    VALUES ('create_rbac_tables', '1.0.0')
  `);
  console.log('✅ 记录迁移完成');
}

// 执行迁移
runMigration();
