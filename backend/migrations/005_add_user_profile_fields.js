#!/usr/bin/env node

/**
 * 数据库迁移脚本 - 添加用户个人资料字段
 * 
 * 此迁移将：
 * 1. 在 users 表中添加 avatar 字段
 * 2. 在 users 表中添加 real_name 字段
 * 3. 在 users 表中添加 email 字段
 * 4. 在 users 表中添加 phone 字段
 * 5. 创建相关索引
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('../src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🚀 开始执行用户个人资料字段迁移...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 主迁移函数
async function runMigration() {
  try {
    // 启用外键约束
    await runQuery('PRAGMA foreign_keys = ON');
    console.log('✅ 启用外键约束');

    // 检查是否已经迁移过
    const migrationCheck = await checkMigrationStatus();
    if (migrationCheck.completed) {
      console.log('⚠️  用户个人资料字段迁移已完成，跳过执行');
      return;
    }

    // 1. 添加用户个人资料字段
    await addUserProfileFields();

    // 2. 创建相关索引
    await createIndexes();

    // 3. 记录迁移完成
    await recordMigration();

    console.log('🎉 用户个人资料字段迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 检查迁移状态
async function checkMigrationStatus() {
  try {
    // 首先检查migrations表是否存在
    const migrationsTableExists = await getQuery(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='migrations'
    `);

    if (!migrationsTableExists) {
      return { completed: false };
    }

    const migration = await getQuery(`
      SELECT * FROM migrations 
      WHERE migration_name = 'add_user_profile_fields'
    `);

    return { completed: !!migration };
  } catch (error) {
    return { completed: false };
  }
}

// 添加用户个人资料字段
async function addUserProfileFields() {
  try {
    console.log('📝 开始添加用户个人资料字段...');

    // 检查当前表结构
    const tableInfo = await allQuery("PRAGMA table_info(users)");
    const existingColumns = tableInfo.map(column => column.name);

    // 需要添加的字段
    const fieldsToAdd = [
      { name: 'avatar', type: 'TEXT', description: '用户头像URL' },
      { name: 'real_name', type: 'TEXT', description: '真实姓名' },
      { name: 'email', type: 'TEXT', description: '邮箱地址' },
      { name: 'phone', type: 'TEXT', description: '手机号码' }
    ];

    // 逐个添加缺失的字段
    for (const field of fieldsToAdd) {
      if (!existingColumns.includes(field.name)) {
        await runQuery(`
          ALTER TABLE users
          ADD COLUMN ${field.name} ${field.type}
        `);
        console.log(`✅ 添加 ${field.name} 字段 (${field.description})`);
      } else {
        console.log(`ℹ️  ${field.name} 字段已存在`);
      }
    }

  } catch (error) {
    console.error('❌ 添加用户个人资料字段失败:', error.message);
    throw error;
  }
}

// 创建相关索引
async function createIndexes() {
  try {
    console.log('📝 开始创建相关索引...');

    // 为邮箱创建索引（可能用于查找）
    await runQuery('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email) WHERE email IS NOT NULL');
    
    // 为手机号创建索引（可能用于查找）
    await runQuery('CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone) WHERE phone IS NOT NULL');

    console.log('✅ 创建用户个人资料相关索引');

  } catch (error) {
    console.error('❌ 创建索引失败:', error.message);
    throw error;
  }
}

// 记录迁移完成
async function recordMigration() {
  try {
    // 确保migrations表存在
    await runQuery(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        migration_name TEXT UNIQUE NOT NULL,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        version TEXT DEFAULT '1.0.0'
      )
    `);

    await runQuery(`
      INSERT OR IGNORE INTO migrations (migration_name, version)
      VALUES ('add_user_profile_fields', '1.0.0')
    `);
    console.log('✅ 记录迁移完成');
  } catch (error) {
    console.error('❌ 记录迁移失败:', error.message);
    throw error;
  }
}

// 执行迁移
if (require.main === module) {
  runMigration();
}

module.exports = {
  runMigration
};
