/**
 * 图片处理工具
 * 使用sharp库进行图片压缩、裁剪和格式转换
 */

const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;

/**
 * 头像处理配置
 */
const AVATAR_CONFIG = {
  // 输出尺寸
  sizes: {
    small: 64,   // 小头像 (用于列表显示)
    medium: 128, // 中等头像 (用于个人资料)
    large: 256   // 大头像 (用于详细页面)
  },
  // 质量设置
  quality: {
    jpeg: 85,
    webp: 80
  },
  // 文件大小限制 (字节)
  maxFileSize: 2 * 1024 * 1024, // 2MB
  // 支持的输入格式
  supportedFormats: ['jpeg', 'jpg', 'png', 'gif', 'webp']
};

/**
 * 处理头像图片
 * @param {Buffer} inputBuffer - 输入图片缓冲区
 * @param {Object} options - 处理选项
 * @returns {Object} 处理结果
 */
async function processAvatar(inputBuffer, options = {}) {
  try {
    const {
      size = 'medium',
      format = 'jpeg',
      quality = AVATAR_CONFIG.quality.jpeg
    } = options;

    // 获取目标尺寸
    const targetSize = AVATAR_CONFIG.sizes[size] || AVATAR_CONFIG.sizes.medium;

    // 创建sharp实例
    let processor = sharp(inputBuffer);

    // 获取图片信息
    const metadata = await processor.metadata();
    
    // 验证输入格式
    if (!AVATAR_CONFIG.supportedFormats.includes(metadata.format)) {
      throw new Error(`不支持的图片格式: ${metadata.format}`);
    }

    // 验证文件大小
    if (inputBuffer.length > AVATAR_CONFIG.maxFileSize) {
      throw new Error(`文件大小超过限制: ${(inputBuffer.length / 1024 / 1024).toFixed(2)}MB`);
    }

    // 处理图片：调整大小、裁剪为正方形、设置质量
    processor = processor
      .resize(targetSize, targetSize, {
        fit: 'cover',
        position: 'center'
      })
      .sharpen(); // 轻微锐化

    // 根据格式设置输出选项
    if (format === 'jpeg' || format === 'jpg') {
      processor = processor.jpeg({ 
        quality,
        progressive: true,
        mozjpeg: true
      });
    } else if (format === 'webp') {
      processor = processor.webp({ 
        quality: AVATAR_CONFIG.quality.webp,
        effort: 6
      });
    } else if (format === 'png') {
      processor = processor.png({ 
        compressionLevel: 9,
        progressive: true
      });
    }

    // 生成处理后的图片
    const outputBuffer = await processor.toBuffer();

    return {
      success: true,
      buffer: outputBuffer,
      metadata: {
        format,
        width: targetSize,
        height: targetSize,
        size: outputBuffer.length,
        originalSize: inputBuffer.length,
        compressionRatio: ((1 - outputBuffer.length / inputBuffer.length) * 100).toFixed(2) + '%'
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 生成多个尺寸的头像
 * @param {Buffer} inputBuffer - 输入图片缓冲区
 * @param {string} baseFilename - 基础文件名
 * @param {string} outputDir - 输出目录
 * @returns {Object} 处理结果
 */
async function generateAvatarSizes(inputBuffer, baseFilename, outputDir) {
  try {
    const results = {};
    const sizes = Object.keys(AVATAR_CONFIG.sizes);

    // 确保输出目录存在
    await fs.mkdir(outputDir, { recursive: true });

    // 为每个尺寸生成头像
    for (const size of sizes) {
      const result = await processAvatar(inputBuffer, { 
        size, 
        format: 'jpeg',
        quality: AVATAR_CONFIG.quality.jpeg 
      });

      if (result.success) {
        const filename = `${baseFilename}_${size}.jpg`;
        const filepath = path.join(outputDir, filename);
        
        await fs.writeFile(filepath, result.buffer);
        
        results[size] = {
          filename,
          filepath,
          url: `/uploads/avatars/${filename}`,
          metadata: result.metadata
        };
      } else {
        throw new Error(`处理${size}尺寸头像失败: ${result.error}`);
      }
    }

    return {
      success: true,
      results
    };

  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 验证图片文件
 * @param {Buffer} buffer - 图片缓冲区
 * @returns {Object} 验证结果
 */
async function validateImage(buffer) {
  try {
    const metadata = await sharp(buffer).metadata();
    
    const validation = {
      isValid: true,
      format: metadata.format,
      width: metadata.width,
      height: metadata.height,
      size: buffer.length,
      errors: []
    };

    // 检查格式
    if (!AVATAR_CONFIG.supportedFormats.includes(metadata.format)) {
      validation.isValid = false;
      validation.errors.push(`不支持的图片格式: ${metadata.format}`);
    }

    // 检查文件大小
    if (buffer.length > AVATAR_CONFIG.maxFileSize) {
      validation.isValid = false;
      validation.errors.push(`文件大小超过限制: ${(buffer.length / 1024 / 1024).toFixed(2)}MB`);
    }

    // 检查最小尺寸
    const minSize = 64;
    if (metadata.width < minSize || metadata.height < minSize) {
      validation.isValid = false;
      validation.errors.push(`图片尺寸过小，最小尺寸为 ${minSize}x${minSize}`);
    }

    return validation;

  } catch (error) {
    return {
      isValid: false,
      errors: [`图片文件损坏或格式错误: ${error.message}`]
    };
  }
}

module.exports = {
  processAvatar,
  generateAvatarSizes,
  validateImage,
  AVATAR_CONFIG
};
