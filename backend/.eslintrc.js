module.exports = {
  root: true,
  env: {
    node: true,
    es2022: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module'
  },
  rules: {
    // 禁止使用console语句
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    // 禁止使用debugger语句
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    // 禁止未使用的变量
    'no-unused-vars': ['error', { 'argsIgnorePattern': '^_' }],
    // 要求使用 === 和 !==
    'eqeqeq': 'error',
    // 禁止不必要的分号
    'no-extra-semi': 'error',
    // 要求或禁止使用分号
    'semi': ['error', 'always'],
    // 强制使用一致的缩进
    'indent': ['error', 2],
    // 强制使用一致的引号
    'quotes': ['error', 'single'],
    // 要求或禁止末尾逗号
    'comma-dangle': ['error', 'never']
  }
};
