/**
 * 首页数据相关 API
 * 获取首页统计数据、活动记录、快捷操作等
 */

import api from "./request";

// 首页 API 接口
export const dashboardAPI = {
  /**
   * 获取首页统计数据
   * @returns {Promise} 统计数据
   */
  getStats() {
    return api.get("/dashboard/stats");
  },

  /**
   * 获取最近活动
   * @param {number} limit - 限制数量
   * @returns {Promise} 活动列表
   */
  getActivities(limit = 10) {
    return api.get("/dashboard/activities", { limit });
  },

  /**
   * 获取快捷操作
   * @returns {Promise} 快捷操作列表
   */
  getQuickActions() {
    // 添加时间戳参数避免浏览器缓存，确保不同角色用户获取正确数据
    const timestamp = Date.now();
    return api.get("/dashboard/quick-actions", { _t: timestamp });
  },

  /**
   * 获取通知信息
   * @returns {Promise} 通知列表
   */
  getNotifications() {
    return api.get("/dashboard/notifications");
  },

  /**
   * 标记通知为已读
   * @param {number} id - 通知ID
   * @returns {Promise} 操作结果
   */
  markNotificationRead(id) {
    return api.put(`/dashboard/notifications/${id}/read`);
  },

  /**
   * 获取用户个人统计数据
   * @returns {Promise} 用户统计数据
   */
  getUserStats() {
    return api.get("/dashboard/user-stats");
  },

  /**
   * 标记所有通知为已读
   * @returns {Promise} 操作结果
   */
  markAllNotificationsRead() {
    return api.put("/dashboard/notifications/read-all");
  },

  /**
   * 获取系统概览（管理员）
   * @returns {Promise} 系统概览数据
   */
  getSystemOverview() {
    return api.get("/dashboard/system-overview");
  },

  /**
   * 获取用户活跃度统计
   * @param {string} period - 统计周期 (day/week/month)
   * @returns {Promise} 活跃度数据
   */
  getUserActivity(period = "week") {
    return api.get("/dashboard/user-activity", { period });
  },

  /**
   * 获取合同趋势统计
   * @param {string} period - 统计周期 (day/week/month)
   * @returns {Promise} 趋势数据
   */
  getContractTrends(period = "month") {
    return api.get("/dashboard/contract-trends", { period });
  },

  /**
   * 清理dashboard缓存
   * @returns {Promise} 操作结果
   */
  clearCache() {
    return api.post("/dashboard/clear-cache");
  },

  /**
   * 清理指定用户的dashboard缓存
   * @param {number} userId - 用户ID
   * @returns {Promise} 操作结果
   */
  clearUserCache(userId) {
    return api.post(`/dashboard/clear-user-cache/${userId}`);
  },
};

export default dashboardAPI;
