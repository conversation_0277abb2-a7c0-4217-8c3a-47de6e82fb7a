/**
 * 审核统计相关 API
 * 获取审核统计数据、趋势分析、分布数据等
 */

import api from "./request";

// 统计 API 接口
export const statisticsAPI = {
  /**
   * 获取统计汇总数据
   * @returns {Promise} 统计汇总数据
   */
  getSummary() {
    return api.get("/statistics/summary");
  },

  /**
   * 获取审核趋势数据
   * @param {string} period - 统计周期 (day/week/month)
   * @returns {Promise} 趋势数据
   */
  getTrends(period = "month") {
    return api.get("/statistics/trends", { period });
  },

  /**
   * 获取审核分布数据
   * @returns {Promise} 分布数据
   */
  getDistribution() {
    return api.get("/statistics/distribution");
  },

  /**
   * 获取详细统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise} 详细统计数据
   */
  getDetailedStats(params = {}) {
    return api.get("/statistics/detailed", params);
  },

  /**
   * 获取审核效率统计
   * @param {string} period - 统计周期
   * @returns {Promise} 效率统计数据
   */
  getEfficiencyStats(period = "month") {
    return api.get("/statistics/efficiency", { period });
  },

  /**
   * 获取用户审核统计
   * @param {number} userId - 用户ID
   * @param {string} period - 统计周期
   * @returns {Promise} 用户统计数据
   */
  getUserStats(userId, period = "month") {
    return api.get(`/statistics/user/${userId}`, { period });
  },

  /**
   * 导出统计报告
   * @param {Object} params - 导出参数
   * @returns {Promise} 导出结果
   */
  exportReport(params = {}) {
    return api.get("/statistics/export", params, {
      responseType: "blob",
    });
  },
};

export default statisticsAPI;
