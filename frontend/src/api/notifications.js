/**
 * 通知相关API
 */

import request from "./request";

/**
 * 获取用户通知列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.type - 通知类型
 * @param {boolean} params.is_read - 是否已读
 * @returns {Promise} API响应
 */
export const getNotifications = (params = {}) => {
  return request.get("/notifications", { params });
};

/**
 * 获取未读通知数量
 * @returns {Promise} API响应
 */
export const getUnreadCount = () => {
  return request.get("/notifications/unread-count");
};

/**
 * 标记通知为已读
 * @param {number} id - 通知ID
 * @returns {Promise} API响应
 */
export const markAsRead = (id) => {
  return request.post(`/notifications/${id}/read`);
};

/**
 * 批量标记通知为已读
 * @param {Array} ids - 通知ID数组
 * @returns {Promise} API响应
 */
export const markBatchAsRead = (ids) => {
  return request.put("/notifications/batch-read", { ids });
};

/**
 * 标记所有通知为已读
 * @returns {Promise} API响应
 */
export const markAllAsRead = () => {
  return request.post("/notifications/read-all");
};

/**
 * 删除通知
 * @param {number} id - 通知ID
 * @returns {Promise} API响应
 */
export const deleteNotification = (id) => {
  return request.delete(`/notifications/${id}`);
};

/**
 * 获取通知统计信息
 * @returns {Promise} API响应
 */
export const getNotificationStats = () => {
  return request.get("/notifications/stats");
};

/**
 * 创建通知（管理员功能）
 * @param {Object} data - 通知数据
 * @param {number} data.user_id - 接收用户ID
 * @param {string} data.type - 通知类型
 * @param {string} data.title - 通知标题
 * @param {string} data.content - 通知内容
 * @param {number} data.related_id - 相关对象ID
 * @param {string} data.related_type - 相关对象类型
 * @returns {Promise} API响应
 */
export const createNotification = (data) => {
  return request.post("/notifications", data);
};

export default {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markBatchAsRead,
  markAllAsRead,
  deleteNotification,
  getNotificationStats,
  createNotification,
};
