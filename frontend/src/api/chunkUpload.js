/**
 * 分片上传API客户端
 * 提供文件分片上传、合并、状态查询等功能
 */

import api from "./request";

const API_BASE = "/api/chunk-upload";

/**
 * 文件分片上传管理器
 */
class ChunkUploadClient {
  constructor() {
    this.uploadSessions = new Map();
    this.defaultChunkSize = 2 * 1024 * 1024; // 2MB
  }

  /**
   * 计算文件MD5（可选）
   * 使用Web Crypto API
   * 注意：此操作会阻塞主线程，建议禁用
   */
  async calculateFileMD5(file) {
    console.warn("MD5计算可能导致界面卡死，建议禁用此功能");

    // 对于大文件，直接返回null避免卡死
    if (file.size > 50 * 1024 * 1024) {
      // 50MB
      console.warn("文件过大，跳过MD5计算");
      return null;
    }

    try {
      const arrayBuffer = await file.arrayBuffer();
      const hashBuffer = await crypto.subtle.digest("SHA-256", arrayBuffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
    } catch (error) {
      console.error("MD5计算失败:", error);
      return null;
    }
  }

  /**
   * 初始化文件分片上传
   */
  async initializeUpload(file, options = {}) {
    const {
      chunkSize = this.defaultChunkSize,
      enableMD5 = false,
      onProgress = null,
    } = options;

    // 计算总分片数
    const totalChunks = Math.ceil(file.size / chunkSize);

    // 计算MD5（可选）
    let fileMd5 = null;
    if (enableMD5) {
      if (onProgress) onProgress({ stage: "calculating_md5", progress: 0 });
      fileMd5 = await this.calculateFileMD5(file);
      if (onProgress) onProgress({ stage: "calculating_md5", progress: 100 });
    }

    const response = await api.post(`${API_BASE}/init`, {
      fileName: file.name,
      fileSize: file.size,
      fileMd5,
      totalChunks,
    });

    const uploadSession = {
      uploadId: response.data.data.uploadId,
      file,
      chunkSize,
      totalChunks,
      uploadedChunks: new Set(),
      onProgress,
      isPaused: false,
      isCompleted: false,
      startTime: Date.now(),
    };

    this.uploadSessions.set(uploadSession.uploadId, uploadSession);

    return uploadSession;
  }

  /**
   * 上传单个分片
   */
  async uploadChunk(uploadId, chunkIndex) {
    const session = this.uploadSessions.get(uploadId);
    if (!session) {
      throw new Error("Upload session not found");
    }

    if (session.uploadedChunks.has(chunkIndex)) {
      return; // 分片已上传
    }

    const start = chunkIndex * session.chunkSize;
    const end = Math.min(start + session.chunkSize, session.file.size);
    const chunk = session.file.slice(start, end);

    const formData = new FormData();
    formData.append("uploadId", uploadId);
    formData.append("chunkIndex", chunkIndex);
    formData.append("chunk", chunk);

    const response = await api.upload(`${API_BASE}/chunk`, formData);

    session.uploadedChunks.add(chunkIndex);

    // 触发进度回调
    if (session.onProgress) {
      const progress =
        (session.uploadedChunks.size / session.totalChunks) * 100;
      session.onProgress({
        stage: "uploading",
        progress,
        uploadedChunks: session.uploadedChunks.size,
        totalChunks: session.totalChunks,
        chunkIndex,
      });
    }

    return response.data;
  }

  /**
   * 上传所有分片
   */
  async uploadAllChunks(uploadId, options = {}) {
    const session = this.uploadSessions.get(uploadId);
    if (!session) {
      throw new Error("Upload session not found");
    }

    const {
      concurrency = 3, // 并发数
      retryCount = 3, // 重试次数
      retryDelay = 1000, // 重试延迟
    } = options;

    // 创建分片上传任务队列
    const chunkTasks = [];
    for (let i = 0; i < session.totalChunks; i++) {
      if (!session.uploadedChunks.has(i)) {
        chunkTasks.push(i);
      }
    }

    // 并发上传分片
    const uploadPromises = [];
    const inProgress = new Set();

    while (chunkTasks.length > 0 || inProgress.size > 0) {
      if (session.isPaused) {
        await new Promise((resolve) => setTimeout(resolve, 100));
        continue;
      }

      // 启动新的上传任务
      while (chunkTasks.length > 0 && inProgress.size < concurrency) {
        const chunkIndex = chunkTasks.shift();
        inProgress.add(chunkIndex);

        const uploadPromise = this.uploadChunkWithRetry(
          uploadId,
          chunkIndex,
          retryCount,
          retryDelay,
        )
          .then(() => {
            inProgress.delete(chunkIndex);
          })
          .catch((error) => {
            inProgress.delete(chunkIndex);
            throw error;
          });

        uploadPromises.push(uploadPromise);
      }

      // 等待至少一个任务完成
      if (inProgress.size > 0) {
        await Promise.race(uploadPromises.filter((p) => p));
      }
    }

    // 等待所有上传完成
    await Promise.all(uploadPromises);
  }

  /**
   * 带重试的分片上传
   */
  async uploadChunkWithRetry(uploadId, chunkIndex, maxRetries, retryDelay) {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        await this.uploadChunk(uploadId, chunkIndex);
        return;
      } catch (error) {
        lastError = error;

        if (attempt < maxRetries) {
          console.warn(
            `Chunk ${chunkIndex} upload failed, retrying (${attempt + 1}/${maxRetries}):`,
            error.message,
          );
          await new Promise((resolve) =>
            setTimeout(resolve, retryDelay * Math.pow(2, attempt)),
          );
        }
      }
    }

    throw lastError;
  }

  /**
   * 合并分片
   */
  async mergeChunks(uploadId) {
    const session = this.uploadSessions.get(uploadId);
    if (!session) {
      throw new Error("Upload session not found");
    }

    if (session.onProgress) {
      session.onProgress({ stage: "merging", progress: 0 });
    }

    const response = await api.post(`${API_BASE}/merge`, {
      uploadId,
    });

    session.isCompleted = true;

    if (session.onProgress) {
      session.onProgress({ stage: "completed", progress: 100 });
    }

    return response.data;
  }

  /**
   * 完整的文件上传流程
   */
  async uploadFile(file, options = {}) {
    try {
      // 1. 初始化上传
      const session = await this.initializeUpload(file, options);

      // 2. 上传所有分片
      await this.uploadAllChunks(session.uploadId, options);

      // 3. 合并分片
      const result = await this.mergeChunks(session.uploadId);

      // 4. 清理会话
      this.uploadSessions.delete(session.uploadId);

      return result;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("Upload file error:", error);
      }
      throw error;
    }
  }

  /**
   * 暂停上传
   */
  pauseUpload(uploadId) {
    const session = this.uploadSessions.get(uploadId);
    if (session) {
      session.isPaused = true;
    }
  }

  /**
   * 恢复上传
   */
  resumeUpload(uploadId) {
    const session = this.uploadSessions.get(uploadId);
    if (session) {
      session.isPaused = false;
    }
  }

  /**
   * 取消上传
   */
  async cancelUpload(uploadId) {
    await api.delete(`${API_BASE}/cancel/${uploadId}`);
    this.uploadSessions.delete(uploadId);
  }

  /**
   * 获取上传状态
   */
  async getUploadStatus(uploadId) {
    const response = await api.get(`${API_BASE}/status/${uploadId}`);
    return response.data;
  }

  /**
   * 获取上传配置
   */
  async getUploadConfig() {
    const response = await api.get(`${API_BASE}/config`);
    return response.data;
  }

  /**
   * 获取活跃会话（管理员）
   */
  async getActiveSessions() {
    const response = await api.get(`${API_BASE}/sessions`);
    return response.data;
  }

  /**
   * 清理过期会话（管理员）
   */
  async cleanupExpiredSessions() {
    const response = await api.post(`${API_BASE}/cleanup`);
    return response.data;
  }
}

// 创建单例实例
const chunkUploadClient = new ChunkUploadClient();

export default chunkUploadClient;
export { ChunkUploadClient };
