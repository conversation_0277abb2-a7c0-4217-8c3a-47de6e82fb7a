/**
 * 操作日志相关API
 */

import request from "./request";

/**
 * 获取操作日志列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.limit=20] - 每页条数
 * @param {number} [params.userId] - 用户ID筛选
 * @param {string} [params.action] - 操作类型筛选
 * @param {string} [params.resourceType] - 资源类型筛选
 * @param {string} [params.status] - 操作状态筛选
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<Object>} 日志列表响应
 */
export const getOperationLogs = (params = {}) => {
  return request({
    method: "GET",
    url: "/api/logs",
    params,
  });
};

/**
 * 获取操作统计数据
 * @param {Object} params - 查询参数
 * @param {string} [params.period='7d'] - 统计周期 (1d/7d/30d)
 * @param {number} [params.userId] - 用户ID筛选
 * @returns {Promise<Object>} 统计数据响应
 */
export const getOperationStats = (params = {}) => {
  return request({
    method: "GET",
    url: "/api/logs/stats",
    params,
  });
};

/**
 * 清理过期日志
 * @param {Object} data - 清理参数
 * @param {number} [data.days=90] - 保留天数
 * @returns {Promise<Object>} 清理结果响应
 */
export const cleanupOldLogs = (data = {}) => {
  return request({
    method: "POST",
    url: "/api/logs/cleanup",
    data,
  });
};

/**
 * 获取操作类型列表
 * @returns {Promise<Object>} 操作类型列表响应
 */
export const getLogActions = () => {
  return request({
    method: "GET",
    url: "/api/logs/actions",
  });
};

/**
 * 获取用户操作日志
 * @param {number} userId - 用户ID
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.limit=20] - 每页条数
 * @param {string} [params.action] - 操作类型筛选
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @returns {Promise<Object>} 用户日志列表响应
 */
export const getUserOperationLogs = (userId, params = {}) => {
  return request({
    method: "GET",
    url: `/api/logs/user/${userId}`,
    params,
  });
};

/**
 * 操作类型映射
 */
export const ACTION_LABELS = {
  // 认证相关
  login: "登录",
  logout: "登出",
  change_password: "修改密码",

  // 合同相关
  submit_contract: "提交合同",
  update_contract: "更新合同",
  delete_contract: "删除合同",
  start_review: "开始审核",
  submit_review: "提交审核",

  // 用户管理
  create_user: "创建用户",
  update_user: "更新用户",
  delete_user: "删除用户",
  reset_password: "重置密码",

  // 文件操作
  upload_file: "上传文件",
  download_file: "下载文件",
  delete_file: "删除文件",

  // 系统管理
  system_config: "系统配置",
  export_data: "导出数据",
  import_data: "导入数据",
  cleanup_logs: "清理日志",
};

/**
 * 资源类型映射
 */
export const RESOURCE_TYPE_LABELS = {
  user: "用户",
  contract: "合同",
  file: "文件",
  system: "系统",
};

/**
 * 操作状态映射
 */
export const STATUS_LABELS = {
  success: "成功",
  failed: "失败",
};

/**
 * 操作状态颜色映射
 */
export const STATUS_COLORS = {
  success: "success",
  failed: "danger",
};

/**
 * 格式化操作详情
 * @param {string} details - 详情JSON字符串
 * @returns {Object} 格式化后的详情对象
 */
export const formatLogDetails = (details) => {
  try {
    return JSON.parse(details || "{}");
  } catch (error) {
    return {};
  }
};

/**
 * 获取操作类型标签
 * @param {string} action - 操作类型
 * @returns {string} 操作类型标签
 */
export const getActionLabel = (action) => {
  return ACTION_LABELS[action] || action;
};

/**
 * 获取资源类型标签
 * @param {string} resourceType - 资源类型
 * @returns {string} 资源类型标签
 */
export const getResourceTypeLabel = (resourceType) => {
  return RESOURCE_TYPE_LABELS[resourceType] || resourceType;
};

/**
 * 获取操作状态标签
 * @param {string} status - 操作状态
 * @returns {string} 操作状态标签
 */
export const getStatusLabel = (status) => {
  return STATUS_LABELS[status] || status;
};

/**
 * 获取操作状态颜色
 * @param {string} status - 操作状态
 * @returns {string} 状态颜色
 */
export const getStatusColor = (status) => {
  return STATUS_COLORS[status] || "info";
};
