/**
 * 文件操作相关 API
 * 处理文件上传、下载、预览等功能
 */

import api from "./request";

// 文件 API 接口
export const filesAPI = {
  /**
   * 上传文件
   * @param {File} file - 文件对象
   * @param {Function} onProgress - 上传进度回调
   * @returns {Promise} 上传结果
   */
  upload(file, onProgress) {
    const formData = new FormData();
    formData.append("file", file);

    return api.upload("/files/upload", formData, {
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total,
          );
          onProgress(progress);
        }
      },
    });
  },

  /**
   * 下载文件
   * @param {string} filename - 文件名
   * @returns {Promise} 文件数据
   */
  download(filename) {
    return api.download(`/files/download/${filename}`);
  },

  /**
   * 预览文件
   * @param {string} filename - 文件名
   * @returns {string} 预览URL
   */
  getPreviewUrl(filename) {
    const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
    return `${baseURL}/files/preview/${filename}`;
  },

  /**
   * 基于合同ID获取预览URL
   * @param {number} contractId - 合同ID
   * @returns {string} 预览URL
   */
  getContractPreviewUrl(contractId) {
    const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
    return `${baseURL}/files/${contractId}/preview`;
  },

  /**
   * 删除文件
   * @param {string} filename - 文件名
   * @returns {Promise} 删除结果
   */
  delete(filename) {
    return api.delete(`/files/${filename}`);
  },

  /**
   * 临时下载文件 (用于刚上传的文件)
   * @param {string} filename - 文件名
   * @returns {Promise} 文件数据
   */
  tempDownload(filename) {
    return api.download(`/files/temp-download/${filename}`);
  },

  /**
   * 临时删除文件 (用于刚上传的文件)
   * @param {string} filename - 文件名
   * @returns {Promise} 删除结果
   */
  tempDelete(filename) {
    return api.delete(`/files/temp-delete/${filename}`);
  },

  /**
   * 获取文件信息
   * @param {string} filename - 文件名
   * @returns {Promise} 文件信息
   */
  getInfo(filename) {
    return api.get(`/files/info/${filename}`);
  },

  /**
   * 获取上传配置
   * @returns {Promise} 上传配置
   */
  getConfig() {
    return api.get("/files/config");
  },

  /**
   * 批量上传文件
   * @param {Array} files - 文件数组
   * @param {Function} onProgress - 进度回调
   * @returns {Promise} 上传结果
   */
  async batchUpload(files, onProgress) {
    const results = [];
    const total = files.length;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      try {
        const result = await this.upload(file, (fileProgress) => {
          const overallProgress = Math.round(
            ((i + fileProgress / 100) / total) * 100,
          );
          if (onProgress) {
            onProgress(overallProgress, i + 1, total);
          }
        });

        results.push({
          file,
          success: true,
          result,
        });
      } catch (error) {
        results.push({
          file,
          success: false,
          error,
        });
      }
    }

    return results;
  },

  /**
   * 验证文件类型
   * @param {File} file - 文件对象
   * @param {Array} allowedTypes - 允许的文件类型
   * @returns {boolean} 是否有效
   */
  validateFileType(file, allowedTypes = [".pdf"]) {
    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.substring(fileName.lastIndexOf("."));
    return allowedTypes.includes(fileExtension);
  },

  /**
   * 验证文件大小
   * @param {File} file - 文件对象
   * @param {number} maxSize - 最大文件大小（字节）
   * @returns {boolean} 是否有效
   */
  validateFileSize(file, maxSize = 10 * 1024 * 1024) {
    return file.size <= maxSize;
  },

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  },

  /**
   * 获取文件图标
   * @param {string} filename - 文件名
   * @returns {string} 图标名称
   */
  getFileIcon(filename) {
    const extension = filename
      .toLowerCase()
      .substring(filename.lastIndexOf("."));

    const iconMap = {
      ".pdf": "DocumentCopy",
      ".doc": "Document",
      ".docx": "Document",
      ".xls": "Document",
      ".xlsx": "Document",
      ".ppt": "Document",
      ".pptx": "Document",
      ".txt": "Document",
      ".jpg": "Picture",
      ".jpeg": "Picture",
      ".png": "Picture",
      ".gif": "Picture",
      ".zip": "Folder",
      ".rar": "Folder",
    };

    return iconMap[extension] || "Document";
  },

  /**
   * 创建文件预览URL（带认证）
   * @param {string} filename - 文件名
   * @returns {string} 预览URL
   */
  createAuthenticatedPreviewUrl(filename) {
    const token = localStorage.getItem("token");
    const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
    return `${baseURL}/files/preview/${filename}?token=${token}`;
  },

  /**
   * 创建基于合同ID的预览URL（带认证）
   * @param {number} contractId - 合同ID
   * @returns {string} 预览URL
   */
  createAuthenticatedContractPreviewUrl(contractId) {
    const token = localStorage.getItem("token");
    const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
    return `${baseURL}/files/${contractId}/preview?token=${token}`;
  },

  /**
   * 检查文件是否存在
   * @param {string} filename - 文件名
   * @returns {Promise<boolean>} 是否存在
   */
  async exists(filename) {
    try {
      await this.getInfo(filename);
      return true;
    } catch (error) {
      return false;
    }
  },
};

export default filesAPI;
