<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 全局加载遮罩 -->
    <div v-if="globalLoading" class="global-loading">
      <div class="loading-content">
        <el-icon class="loading-icon" :size="40">
          <Loading />
        </el-icon>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>

    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition :name="route.meta.transition || 'fade'" mode="out-in" appear>
        <component
          :is="markRaw(Component)"
          v-if="Component"
          :key="route.path"
        />
      </transition>
    </router-view>

    <!-- 全局消息提示容器 -->
    <div id="message-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, markRaw } from "vue";
import { useRouter } from "vue-router";
import { Loading } from "@element-plus/icons-vue";

// 路由实例
const router = useRouter();

// 全局加载状态
const globalLoading = ref(false);
const loadingText = ref("加载中...");

// 监听路由变化，显示加载状态
watch(
  () => router.currentRoute.value,
  (to, from) => {
    if (to.path !== from?.path) {
      globalLoading.value = true;
      loadingText.value = "页面加载中...";

      // 模拟加载时间
      setTimeout(() => {
        globalLoading.value = false;
      }, 300);
    }
  },
);

// 组件挂载后的初始化
onMounted(() => {
  // 检查浏览器兼容性
  checkBrowserCompatibility();

  // 设置全局事件监听
  setupGlobalEventListeners();
});

// 组件卸载时清理
onUnmounted(() => {
  // 清理全局事件监听器
  cleanupGlobalEventListeners();
});

// 检查浏览器兼容性
const checkBrowserCompatibility = () => {
  const isModernBrowser =
    "fetch" in window &&
    "Promise" in window &&
    "Object.assign" in Object &&
    "Array.from" in Array;

  if (!isModernBrowser) {
    // 浏览器版本较低，可能影响系统功能
  }
};

// 事件监听器引用，用于清理
let onlineHandler, offlineHandler, visibilityHandler;

// 设置全局事件监听 - 优化版
const setupGlobalEventListeners = () => {
  // 网络状态监听器
  onlineHandler = () => {
    // 网络连接已恢复
  };

  offlineHandler = () => {
    // 网络连接已断开
  };

  // 页面可见性监听器
  visibilityHandler = () => {
    if (document.hidden) {
      // 页面已隐藏
    } else {
      // 页面已显示
    }
  };

  // 添加事件监听器
  window.addEventListener("online", onlineHandler);
  window.addEventListener("offline", offlineHandler);
  document.addEventListener("visibilitychange", visibilityHandler);
};

// 清理全局事件监听器
const cleanupGlobalEventListeners = () => {
  if (onlineHandler) {
    window.removeEventListener("online", onlineHandler);
  }
  if (offlineHandler) {
    window.removeEventListener("offline", offlineHandler);
  }
  if (visibilityHandler) {
    document.removeEventListener("visibilitychange", visibilityHandler);
  }
};

// 暴露全局方法
window.showGlobalLoading = (text = "加载中...") => {
  globalLoading.value = true;
  loadingText.value = text;
};

window.hideGlobalLoading = () => {
  globalLoading.value = false;
};
</script>

<style scoped>
/* 全局加载遮罩样式 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-icon {
  color: #409eff;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-text {
  color: #666;
  font-size: 14px;
  margin: 0;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
</style>
