/**
 * 用户管理 Composable
 * 提供用户管理相关的状态和方法
 */

import { ref, reactive, computed } from "vue";
import { ElMessage } from "element-plus";
import { usersAPI, adminAPI } from "@/api/users";

/**
 * 用户管理 Hook
 */
export function useUsers() {
  // 状态
  const loading = ref(false);
  const submitting = ref(false);
  const users = ref([]);

  // 分页信息
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });

  // 筛选条件
  const filters = reactive({
    keyword: "",
    role: "",
    status: "",
  });

  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   */
  const getUsers = async (params = {}) => {
    try {
      loading.value = true;

      const queryParams = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...filters,
        ...params,
      };

      const response = await adminAPI.getUsers(queryParams);

      if (response.success) {
        users.value = response.data || [];
        pagination.total = response.pagination?.total || 0;
        pagination.totalPages = response.pagination?.totalPages || 0;
      }
    } catch (error) {
      // 全局错误处理器会处理API错误
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取用户详情
   * @param {number} id - 用户ID
   */
  const getUserDetail = async (id) => {
    try {
      loading.value = true;

      const response = await usersAPI.getDetail(id);

      if (response.success) {
        return response.data;
      }
    } catch (error) {
      // 全局错误处理器会处理API错误
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   */
  const createUser = async (userData) => {
    try {
      submitting.value = true;

      const response = await adminAPI.createUser(userData);

      if (response.success) {
        ElMessage.success("用户创建成功");
        return response.data;
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("创建用户失败:", error);
      }
      ElMessage.error(error.message || "创建用户失败");
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 更新用户
   * @param {number} id - 用户ID
   * @param {Object} userData - 用户数据
   */
  const updateUser = async (id, userData) => {
    try {
      submitting.value = true;

      const response = await adminAPI.updateUser(id, userData);

      if (response.success) {
        ElMessage.success("用户更新成功");
        return response.data;
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("更新用户失败:", error);
      }
      ElMessage.error(error.message || "更新用户失败");
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 删除用户
   * @param {number} id - 用户ID
   */
  const deleteUser = async (id) => {
    try {
      submitting.value = true;

      const response = await adminAPI.deleteUser(id);

      if (response.success) {
        ElMessage.success("用户删除成功");
        return response.data;
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("删除用户失败:", error);
      }
      ElMessage.error(error.message || "删除用户失败");
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 重置用户密码
   * @param {number} id - 用户ID
   * @param {string} newPassword - 新密码
   */
  const resetUserPassword = async (id, newPassword) => {
    try {
      submitting.value = true;

      const response = await adminAPI.resetUserPassword(id, {
        password: newPassword,
      });

      if (response.success) {
        ElMessage.success("密码重置成功");
        return response.data;
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("重置密码失败:", error);
      }
      ElMessage.error(error.message || "重置密码失败");
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 封禁用户
   * @param {number} id - 用户ID
   */
  const banUser = async (id) => {
    try {
      submitting.value = true;

      const response = await usersAPI.update(id, { status: "banned" });

      if (response.success) {
        ElMessage.success("用户封禁成功");
        return response.data;
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("封禁用户失败:", error);
      }
      ElMessage.error(error.message || "封禁用户失败");
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 解封用户
   * @param {number} id - 用户ID
   */
  const unbanUser = async (id) => {
    try {
      submitting.value = true;

      const response = await usersAPI.update(id, { status: "active" });

      if (response.success) {
        ElMessage.success("用户解封成功");
        return response.data;
      }
    } catch (error) {
      // 全局错误处理器会处理API错误
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 设置筛选条件
   * @param {Object} newFilters - 新的筛选条件
   */
  const setFilters = (newFilters) => {
    Object.assign(filters, newFilters);
    pagination.page = 1; // 重置到第一页
  };

  /**
   * 重置筛选条件
   */
  const resetFilters = () => {
    filters.keyword = "";
    filters.role = "";
    filters.status = "";
    pagination.page = 1;
  };

  /**
   * 设置分页
   * @param {number} page - 页码
   * @param {number} pageSize - 页面大小
   */
  const setPagination = (page, pageSize) => {
    pagination.page = page;
    pagination.pageSize = pageSize;
  };

  /**
   * 刷新数据
   */
  const refresh = async () => {
    await getUsers();
  };

  // 计算属性
  const isEmpty = computed(() => users.value.length === 0);
  const hasData = computed(() => users.value.length > 0);

  return {
    // 状态
    loading,
    submitting,
    users,
    pagination,
    filters,
    isEmpty,
    hasData,

    // 方法
    getUsers,
    getUserDetail,
    createUser,
    updateUser,
    deleteUser,
    resetUserPassword,
    banUser,
    unbanUser,
    setFilters,
    resetFilters,
    setPagination,
    refresh,
  };
}

export default useUsers;
