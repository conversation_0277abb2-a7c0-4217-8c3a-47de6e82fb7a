/**
 * PDF缓存服务
 * 提供PDF文件的缓存、预加载和离线访问功能
 */

import pdfCacheManager from "@/utils/pdfCacheManager.js";
import api from "@/api/request.js";

class PDFCacheService {
  constructor() {
    this.preloadQueue = new Set(); // 预加载队列
    this.loadingUrls = new Set(); // 正在加载的URL
  }

  /**
   * 获取PDF文件（优先从缓存）
   * @param {string} url - PDF文件URL
   * @param {Object} options - 选项
   */
  async getPDF(url, options = {}) {
    const { forceRefresh = false, showLoading = true, metadata = {} } = options;

    try {
      // 先获取最新的元数据（用于缓存验证）
      let latestMetadata = metadata;
      if (!forceRefresh) {
        try {
          // 发送HEAD请求获取最新的ETag等信息
          const headResponse = await this.fetchPDFData(url, false, 'HEAD');
          latestMetadata = {
            ...metadata,
            etag: headResponse.headers.get("etag"),
            contractId: headResponse.headers.get("x-contract-id"),
            filePath: headResponse.headers.get("x-file-path"),
          };
        } catch (headError) {
          console.warn("获取PDF元数据失败，使用原有元数据:", headError);
        }
      }

      // 如果不强制刷新，先尝试从缓存获取
      if (!forceRefresh) {
        const cached = await pdfCacheManager.getCachedPDF(url, latestMetadata);
        if (cached) {
          return {
            data: cached.data,
            source: "cache",
            metadata: cached.metadata,
            cachedAt: cached.cachedAt,
          };
        }
      }

      // 从网络获取
      return await this.fetchAndCachePDF(url, { showLoading, metadata: latestMetadata });
    } catch (error) {
      console.error("获取PDF失败:", error);

      // 网络失败时尝试从缓存获取（离线模式）
      if (!forceRefresh) {
        const cached = await pdfCacheManager.getCachedPDF(url, metadata);
        if (cached) {
          return {
            data: cached.data,
            source: "cache-offline",
            metadata: cached.metadata,
            cachedAt: cached.cachedAt,
          };
        }
      }

      throw error;
    }
  }

  /**
   * 从网络获取并缓存PDF
   * @param {string} url - PDF文件URL
   * @param {Object} options - 选项
   */
  async fetchAndCachePDF(url, options = {}) {
    const { showLoading = true, metadata = {} } = options;

    // 防止重复加载
    if (this.loadingUrls.has(url)) {
      throw new Error("PDF正在加载中，请稍候");
    }

    this.loadingUrls.add(url);

    try {
      // 使用fetch获取PDF数据
      const response = await this.fetchPDFData(url, showLoading);
      const arrayBuffer = await response.arrayBuffer();

      // 提取响应头中的元数据
      const responseMetadata = {
        contentType: response.headers.get("content-type") || "application/pdf",
        contentLength: response.headers.get("content-length"),
        lastModified: response.headers.get("last-modified"),
        etag: response.headers.get("etag"),
        contractId: response.headers.get("x-contract-id"),
        filePath: response.headers.get("x-file-path"),
        ...metadata,
      };

      // 缓存PDF文件
      await pdfCacheManager.cachePDF(url, arrayBuffer, responseMetadata);

      return {
        data: arrayBuffer,
        source: "network",
        metadata: responseMetadata,
      };
    } finally {
      this.loadingUrls.delete(url);
    }
  }

  /**
   * 使用fetch获取PDF数据
   * @param {string} url - PDF文件URL
   * @param {boolean} showLoading - 是否显示加载状态
   * @param {string} method - HTTP方法（GET或HEAD）
   */
  async fetchPDFData(url, showLoading = true, method = 'GET') {
    // 添加认证token
    const token = localStorage.getItem("token");
    const headers = {};

    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // 如果URL中没有token参数，添加token参数（兼容iframe方式）
    let fetchUrl = url;
    if (token && !url.includes("token=")) {
      const separator = url.includes("?") ? "&" : "?";
      fetchUrl = `${url}${separator}token=${token}`;
    }

    const response = await fetch(fetchUrl, {
      method: method,
      headers,
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`PDF加载失败: ${response.status} ${response.statusText}`);
    }

    return response;
  }

  /**
   * 预加载PDF文件
   * @param {string} url - PDF文件URL
   * @param {Object} metadata - 文件元数据
   */
  async preloadPDF(url, metadata = {}) {
    // 避免重复预加载
    if (this.preloadQueue.has(url) || this.loadingUrls.has(url)) {
      return;
    }

    try {
      // 先获取最新的元数据
      const headResponse = await this.fetchPDFData(url, false, 'HEAD');
      const latestMetadata = {
        ...metadata,
        etag: headResponse.headers.get("etag"),
        contractId: headResponse.headers.get("x-contract-id"),
        filePath: headResponse.headers.get("x-file-path"),
      };

      // 检查是否已缓存（使用最新元数据）
      const isCached = await pdfCacheManager.isCached(url, latestMetadata);
      if (isCached) {
        return;
      }

      this.preloadQueue.add(url);

      await this.fetchAndCachePDF(url, {
        showLoading: false,
        metadata: latestMetadata,
      });
    } catch (error) {
      console.warn(`⚠️ PDF预加载失败: ${url}`, error);
    } finally {
      this.preloadQueue.delete(url);
    }
  }

  /**
   * 批量预加载PDF文件
   * @param {Array} urls - PDF文件URL数组
   * @param {Object} options - 选项
   */
  async batchPreload(urls, options = {}) {
    const {
      concurrency = 3, // 并发数
      delay = 1000, // 延迟时间（毫秒）
    } = options;

    // 分批处理，避免同时发起太多请求
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);

      // 并发预加载当前批次
      const promises = batch.map((urlInfo) => {
        const url = typeof urlInfo === "string" ? urlInfo : urlInfo.url;
        const metadata = typeof urlInfo === "object" ? urlInfo.metadata : {};
        return this.preloadPDF(url, metadata);
      });

      await Promise.allSettled(promises);

      // 批次间延迟
      if (i + concurrency < urls.length) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * 检查PDF是否已缓存
   * @param {string} url - PDF文件URL
   */
  async isCached(url) {
    return await pdfCacheManager.isCached(url);
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    return await pdfCacheManager.getCacheStats();
  }

  /**
   * 清空所有缓存
   */
  async clearAllCache() {
    return await pdfCacheManager.clearAll();
  }

  /**
   * 删除特定PDF缓存
   * @param {string} url - PDF文件URL
   */
  async deleteCachedPDF(url) {
    return await pdfCacheManager.deleteCached(url);
  }

  /**
   * 创建PDF的Blob URL（用于iframe显示）
   * @param {ArrayBuffer} data - PDF数据
   */
  createBlobUrl(data) {
    const blob = new Blob([data], { type: "application/pdf" });
    return URL.createObjectURL(blob);
  }

  /**
   * 释放Blob URL
   * @param {string} blobUrl - Blob URL
   */
  revokeBlobUrl(blobUrl) {
    if (blobUrl && blobUrl.startsWith("blob:")) {
      URL.revokeObjectURL(blobUrl);
    }
  }

  /**
   * 获取缓存健康状态
   */
  async getCacheHealth() {
    try {
      const stats = await this.getCacheStats();

      return {
        status: "healthy",
        totalFiles: stats.fileCount,
        totalSize: stats.totalSize,
        usagePercent: stats.usagePercent,
        maxSize: stats.maxSize,
        maxFiles: stats.maxFiles,
        formattedSize: pdfCacheManager.formatSize(stats.totalSize),
        formattedMaxSize: pdfCacheManager.formatSize(stats.maxSize),
      };
    } catch (error) {
      return {
        status: "error",
        error: error.message,
        totalFiles: 0,
        totalSize: 0,
        usagePercent: 0,
      };
    }
  }

  /**
   * 预热缓存（预加载常用PDF）
   * @param {Array} contractIds - 合同ID数组
   */
  async warmupCache(contractIds) {
    if (!contractIds || contractIds.length === 0) return;

    const urls = contractIds.map((id) => {
      const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
      return {
        url: `${baseURL}/files/${id}/preview`,
        metadata: { contractId: id },
      };
    });

    await this.batchPreload(urls, {
      concurrency: 2, // 降低并发数，避免影响用户体验
      delay: 2000, // 增加延迟
    });
  }
}

// 创建单例实例
const pdfCacheService = new PDFCacheService();

export default pdfCacheService;
