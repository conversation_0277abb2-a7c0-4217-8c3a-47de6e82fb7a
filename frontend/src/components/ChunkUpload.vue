<!--
  分片上传组件
  支持大文件分片上传、断点续传、进度显示
-->

<template>
  <div class="chunk-upload">
    <!-- 上传区域 -->
    <div v-if="!isUploading && !isCompleted" class="upload-area">
      <el-upload
        ref="uploadRef"
        :drag="true"
        :multiple="false"
        :show-file-list="false"
        :before-upload="handleBeforeUpload"
        :auto-upload="false"
        accept=".pdf,.doc,.docx"
        class="upload-dragger"
      >
        <el-icon class="upload-icon"><upload-filled /></el-icon>
        <div class="upload-text">
          <p>点击或拖拽文件到此区域上传</p>
          <p class="upload-hint">支持 PDF、Word 文档</p>
        </div>
      </el-upload>

      <!-- 选择的文件信息 -->
      <div v-if="selectedFile" class="file-info">
        <el-card>
          <div class="file-details">
            <el-icon class="file-icon"><document /></el-icon>
            <div class="file-meta">
              <div class="file-name">{{ selectedFile.name }}</div>
              <div class="file-size">
                {{ formatFileSize(selectedFile.size) }}
              </div>
            </div>
            <div class="file-actions">
              <el-button
                type="primary"
                :disabled="isUploading"
                @click="startUpload"
              >
                开始上传
              </el-button>
              <el-button @click="clearFile">取消</el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 上传进度 -->
    <div v-if="isUploading" class="upload-progress">
      <el-card>
        <div class="progress-header">
          <div class="progress-title">
            <el-icon class="progress-icon"><upload-filled /></el-icon>
            <span>{{
              uploadStatus.stage === "merging" ? "合并文件中" : "上传中"
            }}</span>
          </div>
          <div class="progress-actions">
            <el-button
              v-if="!isPaused && uploadStatus.stage === 'uploading'"
              size="small"
              @click="pauseUpload"
            >
              暂停
            </el-button>
            <el-button
              v-if="isPaused"
              size="small"
              type="primary"
              @click="resumeUpload"
            >
              继续
            </el-button>
            <el-button size="small" type="danger" @click="cancelUpload">
              取消
            </el-button>
          </div>
        </div>

        <div class="progress-content">
          <div class="file-info-row">
            <span class="file-name">{{ selectedFile.name }}</span>
            <span class="file-size">{{
              formatFileSize(selectedFile.size)
            }}</span>
          </div>

          <div class="progress-bar-container">
            <el-progress
              :percentage="uploadStatus.progress"
              :stroke-width="8"
              :status="getProgressStatus()"
            />
          </div>

          <div class="progress-details">
            <div class="progress-stats">
              <span v-if="uploadStatus.stage === 'uploading'">
                分片进度: {{ uploadStatus.uploadedChunks || 0 }} /
                {{ uploadStatus.totalChunks || 0 }}
              </span>
              <span v-if="uploadStatus.stage === 'calculating_md5'">
                计算文件校验码...
              </span>
              <span v-if="uploadStatus.stage === 'merging'">
                正在合并文件...
              </span>
            </div>

            <div v-if="uploadSpeed" class="upload-speed">
              {{ formatSpeed(uploadSpeed) }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 上传完成 -->
    <div v-if="isCompleted" class="upload-complete">
      <el-card>
        <div class="complete-content">
          <el-icon class="success-icon"><check-circle /></el-icon>
          <div class="complete-text">
            <h3>上传成功</h3>
            <p>文件已成功上传到服务器</p>
          </div>
        </div>

        <div class="complete-actions">
          <el-button type="primary" @click="handleUploadComplete">
            继续
          </el-button>
          <el-button @click="resetUpload"> 重新上传 </el-button>
        </div>
      </el-card>
    </div>

    <!-- 配置选项 -->
    <div v-if="showOptions" class="upload-options">
      <el-card>
        <template #header>
          <span>上传配置</span>
        </template>

        <el-form label-width="120px">
          <el-form-item label="启用MD5校验">
            <el-switch v-model="enableMD5" />
            <div class="form-item-tip">
              <el-text type="warning" size="small">
                ⚠️ MD5计算可能导致界面卡死，建议保持关闭
              </el-text>
            </div>
          </el-form-item>

          <el-form-item label="并发上传数">
            <el-input-number
              v-model="concurrency"
              :min="1"
              :max="10"
              size="small"
            />
          </el-form-item>

          <el-form-item label="重试次数">
            <el-input-number
              v-model="retryCount"
              :min="0"
              :max="5"
              size="small"
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, markRaw } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { UploadFilled, Document, Check } from "@element-plus/icons-vue";
import chunkUploadClient from "@/api/chunkUpload";

export default {
  name: "ChunkUpload",
  components: {
    UploadFilled,
    Document,
    Check,
  },
  props: {
    showOptions: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["upload-complete", "upload-error"],
  setup(props, { emit }) {
    // 响应式数据
    const uploadRef = ref(null);
    const selectedFile = ref(null);
    const isUploading = ref(false);
    const isCompleted = ref(false);
    const isPaused = ref(false);
    const currentUploadId = ref(null);
    const maxFileSizeMB = ref(100);

    // 上传状态
    const uploadStatus = reactive({
      stage: "", // 'calculating_md5', 'uploading', 'merging', 'completed'
      progress: 0,
      uploadedChunks: 0,
      totalChunks: 0,
      chunkIndex: 0,
    });

    // 上传配置
    const enableMD5 = ref(false); // 默认禁用MD5计算，避免主线程阻塞
    const concurrency = ref(2); // 降低并发数，减少服务器压力
    const retryCount = ref(3);

    // 上传速度计算 - 优化版，使用非响应式对象
    const uploadSpeed = ref(0);
    const speedCalculator = markRaw({
      startTime: 0,
      lastProgress: 0,
      lastTime: 0,
      speeds: [],
    });

    // 计算属性
    const getProgressStatus = () => {
      if (uploadStatus.stage === "merging") return "warning";
      if (uploadStatus.stage === "completed") return "success";
      if (isPaused.value) return "exception";
      return "active";
    };

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    // 格式化上传速度
    const formatSpeed = (speed) => {
      return formatFileSize(speed) + "/s";
    };

    // 文件选择前的处理
    const handleBeforeUpload = (file) => {
      // 文件类型检查
      const allowedTypes = [".pdf", ".doc", ".docx"];
      const fileExt = "." + file.name.split(".").pop().toLowerCase();
      if (!allowedTypes.includes(fileExt)) {
        ElMessage.error("只支持 PDF、Word 文档格式");
        return false;
      }

      selectedFile.value = file;
      return false; // 阻止自动上传
    };

    // 清除选择的文件
    const clearFile = () => {
      selectedFile.value = null;
      uploadRef.value.clearFiles();
    };

    // 开始上传
    const startUpload = async () => {
      if (!selectedFile.value) {
        ElMessage.error("请先选择文件");
        return;
      }

      try {
        isUploading.value = true;
        isPaused.value = false;

        // 重置上传状态
        Object.assign(uploadStatus, {
          stage: "",
          progress: 0,
          uploadedChunks: 0,
          totalChunks: 0,
          chunkIndex: 0,
        });

        // 初始化速度计算器
        speedCalculator.startTime = Date.now();
        speedCalculator.lastProgress = 0;
        speedCalculator.lastTime = speedCalculator.startTime;
        speedCalculator.speeds = [];

        // 开始上传
        const result = await chunkUploadClient.uploadFile(selectedFile.value, {
          enableMD5: enableMD5.value,
          concurrency: concurrency.value,
          retryCount: retryCount.value,
          onProgress: handleUploadProgress,
        });

        isCompleted.value = true;
        emit("upload-complete", result);
        ElMessage.success("文件上传成功");
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("Upload error:", error);
        }
        ElMessage.error(error.message || "上传失败");
        emit("upload-error", error);
      } finally {
        isUploading.value = false;
      }
    };

    // 处理上传进度
    const handleUploadProgress = (progress) => {
      Object.assign(uploadStatus, progress);

      // 计算上传速度
      if (progress.stage === "uploading") {
        const now = Date.now();
        const timeDiff = now - speedCalculator.lastTime;

        if (timeDiff > 1000) {
          // 每秒更新一次速度
          const progressDiff = progress.progress - speedCalculator.lastProgress;
          const bytesUploaded = (progressDiff / 100) * selectedFile.value.size;
          const currentSpeed = bytesUploaded / (timeDiff / 1000);

          speedCalculator.speeds.push(currentSpeed);
          if (speedCalculator.speeds.length > 5) {
            speedCalculator.speeds.shift();
          }

          // 计算平均速度
          const avgSpeed =
            speedCalculator.speeds.reduce((a, b) => a + b, 0) /
            speedCalculator.speeds.length;
          uploadSpeed.value = avgSpeed;

          speedCalculator.lastProgress = progress.progress;
          speedCalculator.lastTime = now;
        }
      }
    };

    // 暂停上传
    const pauseUpload = () => {
      if (currentUploadId.value) {
        chunkUploadClient.pauseUpload(currentUploadId.value);
        isPaused.value = true;
      }
    };

    // 恢复上传
    const resumeUpload = () => {
      if (currentUploadId.value) {
        chunkUploadClient.resumeUpload(currentUploadId.value);
        isPaused.value = false;
      }
    };

    // 取消上传
    const cancelUpload = async () => {
      try {
        await ElMessageBox.confirm("确定要取消上传吗？", "确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        if (currentUploadId.value) {
          await chunkUploadClient.cancelUpload(currentUploadId.value);
        }

        resetUpload();
        ElMessage.info("上传已取消");
      } catch (error) {
        // 用户取消确认
      }
    };

    // 重置上传状态
    const resetUpload = () => {
      selectedFile.value = null;
      isUploading.value = false;
      isCompleted.value = false;
      isPaused.value = false;
      currentUploadId.value = null;
      uploadSpeed.value = 0;

      Object.assign(uploadStatus, {
        stage: "",
        progress: 0,
        uploadedChunks: 0,
        totalChunks: 0,
        chunkIndex: 0,
      });

      if (uploadRef.value) {
        uploadRef.value.clearFiles();
      }
    };

    // 处理上传完成
    const handleUploadComplete = () => {
      emit("upload-complete", {
        fileName: selectedFile.value.name,
        fileSize: selectedFile.value.size,
      });
      resetUpload();
    };

    // 获取上传配置
    const loadUploadConfig = async () => {
      try {
        const response = await chunkUploadClient.getUploadConfig();
        const config = response.data;
        maxFileSizeMB.value = Math.floor(config.maxFileSize / (1024 * 1024));
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("Load upload config error:", error);
        }
      }
    };

    // 生命周期钩子
    onMounted(() => {
      loadUploadConfig();
    });

    onUnmounted(() => {
      // 清理资源
      if (currentUploadId.value) {
        chunkUploadClient
          .cancelUpload(currentUploadId.value)
          .catch(console.error);
      }
    });

    return {
      // 引用
      uploadRef,

      // 响应式数据
      selectedFile,
      isUploading,
      isCompleted,
      isPaused,
      uploadStatus,
      uploadSpeed,
      maxFileSizeMB,

      // 配置
      enableMD5,
      concurrency,
      retryCount,

      // 方法
      handleBeforeUpload,
      clearFile,
      startUpload,
      pauseUpload,
      resumeUpload,
      cancelUpload,
      resetUpload,
      handleUploadComplete,

      // 工具方法
      formatFileSize,
      formatSpeed,
      getProgressStatus,
    };
  },
};
</script>

<style scoped>
.chunk-upload {
  max-width: 600px;
  margin: 0 auto;
}

.upload-area {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger :deep(.el-upload) {
  width: 100%;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.upload-dragger :deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.upload-icon {
  font-size: 48px;
  color: #8c939d;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

.file-info {
  margin-top: 16px;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
}

.file-meta {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.upload-progress {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.progress-icon {
  color: #409eff;
}

.progress-actions {
  display: flex;
  gap: 8px;
}

.progress-content {
  margin-top: 16px;
}

.file-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.file-info-row .file-name {
  font-weight: 500;
  color: #303133;
}

.file-info-row .file-size {
  color: #909399;
  font-size: 14px;
}

.progress-bar-container {
  margin-bottom: 12px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.upload-complete {
  margin-bottom: 20px;
}

.complete-content {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.success-icon {
  font-size: 32px;
  color: #67c23a;
}

.complete-text h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.complete-text p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.complete-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.upload-options {
  margin-top: 20px;
}

.form-item-tip {
  margin-top: 4px;
}

.upload-options :deep(.el-card__body) {
  padding: 16px;
}
</style>
