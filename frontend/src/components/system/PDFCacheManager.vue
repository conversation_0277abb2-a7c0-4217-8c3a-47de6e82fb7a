<template>
  <div class="pdf-cache-manager">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>PDF缓存管理</span>
          <div class="header-actions">
            <el-button size="small" @click="refreshStats">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" type="danger" @click="clearAllCache">
              <el-icon><Delete /></el-icon>
              清空缓存
            </el-button>
          </div>
        </div>
      </template>

      <!-- 缓存统计 -->
      <div class="cache-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="缓存文件数" :value="stats.fileCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="缓存大小" :value="stats.formattedSize" />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="使用率"
              :value="stats.usagePercent"
              suffix="%"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最大容量" :value="stats.formattedMaxSize" />
          </el-col>
        </el-row>

        <!-- 使用率进度条 -->
        <div class="usage-progress">
          <el-progress
            :percentage="stats.usagePercent"
            :color="getProgressColor(stats.usagePercent)"
            :show-text="false"
          />
          <span class="progress-text">
            {{ stats.formattedSize }} / {{ stats.formattedMaxSize }}
          </span>
        </div>
      </div>

      <!-- 缓存文件列表 -->
      <div class="cache-files">
        <h4>缓存文件列表</h4>
        <el-table :data="stats.files" style="width: 100%" max-height="400">
          <el-table-column prop="filename" label="文件名" min-width="200">
            <template #default="{ row }">
              <el-tooltip :content="row.url" placement="top">
                <span>{{ row.filename || "未知文件" }}</span>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column prop="size" label="大小" width="100">
            <template #default="{ row }">
              {{ formatSize(row.size) }}
            </template>
          </el-table-column>

          <el-table-column prop="cachedAt" label="缓存时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.cachedAt) }}
            </template>
          </el-table-column>

          <el-table-column prop="lastAccessed" label="最后访问" width="160">
            <template #default="{ row }">
              {{ formatDate(row.lastAccessed) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button
                size="small"
                type="danger"
                @click="deleteCacheFile(row.url)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh, Delete } from "@element-plus/icons-vue";
import pdfCacheService from "@/services/pdfCacheService.js";

// 响应式数据
const stats = ref({
  fileCount: 0,
  totalSize: 0,
  formattedSize: "0 B",
  formattedMaxSize: "100 MB",
  usagePercent: 0,
  files: [],
});

// 刷新统计信息
const refreshStats = async () => {
  try {
    const cacheStats = await pdfCacheService.getCacheStats();
    stats.value = {
      ...cacheStats,
      formattedSize: formatSize(cacheStats.totalSize),
      formattedMaxSize: formatSize(cacheStats.maxSize),
    };
  } catch (error) {
    console.error("获取缓存统计失败:", error);
    ElMessage.error("获取缓存统计失败");
  }
};

// 清空所有缓存
const clearAllCache = async () => {
  try {
    await ElMessageBox.confirm(
      "确定要清空所有PDF缓存吗？此操作不可恢复。",
      "确认清空",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    const success = await pdfCacheService.clearAllCache();
    if (success) {
      ElMessage.success("缓存清空成功");
      await refreshStats();
    } else {
      ElMessage.error("缓存清空失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("清空缓存失败:", error);
      ElMessage.error("清空缓存失败");
    }
  }
};

// 删除单个缓存文件
const deleteCacheFile = async (url) => {
  try {
    await ElMessageBox.confirm("确定要删除这个缓存文件吗？", "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const success = await pdfCacheService.deleteCachedPDF(url);
    if (success) {
      ElMessage.success("缓存文件删除成功");
      await refreshStats();
    } else {
      ElMessage.error("缓存文件删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除缓存文件失败:", error);
      ElMessage.error("删除缓存文件失败");
    }
  }
};

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 50) return "#67c23a";
  if (percentage < 80) return "#e6a23c";
  return "#f56c6c";
};

// 格式化文件大小
const formatSize = (bytes) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return "-";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 组件挂载时加载数据
onMounted(() => {
  refreshStats();
});
</script>

<style scoped>
.pdf-cache-manager {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.cache-stats {
  margin-bottom: 24px;
}

.usage-progress {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.cache-files h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-weight: 500;
}
</style>
