<template>
  <div class="cache-manager">
    <el-card header="缓存管理工具">
      <div class="space-y-4">
        <!-- 缓存状态 -->
        <div class="cache-status">
          <h4 class="text-lg font-medium mb-2">缓存状态</h4>
          <div class="grid grid-cols-2 gap-4">
            <el-statistic title="PDF缓存数量" :value="cacheStats.fileCount" />
            <el-statistic title="缓存大小" :value="formatSize(cacheStats.totalSize)" />
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="cache-actions">
          <h4 class="text-lg font-medium mb-2">缓存操作</h4>
          <div class="flex flex-wrap gap-2">
            <el-button 
              type="primary" 
              @click="refreshStats"
              :loading="loading.stats"
            >
              刷新状态
            </el-button>
            
            <el-button 
              type="warning" 
              @click="clearAllCache"
              :loading="loading.clearAll"
            >
              清空所有缓存
            </el-button>
            
            <el-button 
              type="info" 
              @click="cleanExpiredCache"
              :loading="loading.cleanExpired"
            >
              清理过期缓存
            </el-button>
          </div>
        </div>

        <!-- 合同缓存清理 -->
        <div class="contract-cache">
          <h4 class="text-lg font-medium mb-2">合同缓存清理</h4>
          <div class="flex gap-2">
            <el-input
              v-model="contractId"
              placeholder="输入合同ID"
              style="width: 200px"
              type="number"
            />
            <el-button 
              type="danger" 
              @click="clearContractCache"
              :loading="loading.clearContract"
              :disabled="!contractId"
            >
              清理指定合同缓存
            </el-button>
          </div>
        </div>

        <!-- 缓存详情 -->
        <div class="cache-details" v-if="cacheStats.files.length > 0">
          <h4 class="text-lg font-medium mb-2">缓存详情</h4>
          <el-table :data="cacheStats.files" max-height="300">
            <el-table-column prop="filename" label="文件名" width="200" />
            <el-table-column prop="size" label="大小" width="100">
              <template #default="{ row }">
                {{ formatSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="cachedAt" label="缓存时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.cachedAt) }}
              </template>
            </el-table-column>
            <el-table-column prop="lastAccessed" label="最后访问" width="180">
              <template #default="{ row }">
                {{ formatDate(row.lastAccessed) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="deleteCacheItem(row.url)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import pdfCacheManager from '@/utils/pdfCacheManager';
import cacheSync from '@/utils/cacheSync';

// 响应式数据
const cacheStats = ref({
  fileCount: 0,
  totalSize: 0,
  files: []
});

const contractId = ref('');

const loading = ref({
  stats: false,
  clearAll: false,
  cleanExpired: false,
  clearContract: false
});

// 格式化文件大小
const formatSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化日期
const formatDate = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

// 刷新缓存状态
const refreshStats = async () => {
  loading.value.stats = true;
  try {
    const stats = await pdfCacheManager.getCacheStats();
    cacheStats.value = stats;
  } catch (error) {
    console.error('获取缓存状态失败:', error);
    ElMessage.error('获取缓存状态失败');
  } finally {
    loading.value.stats = false;
  }
};

// 清空所有缓存
const clearAllCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有PDF缓存吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    loading.value.clearAll = true;
    await cacheSync.forceClearCache();
    ElMessage.success('缓存清空成功');
    await refreshStats();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空缓存失败:', error);
      ElMessage.error('清空缓存失败');
    }
  } finally {
    loading.value.clearAll = false;
  }
};

// 清理过期缓存
const cleanExpiredCache = async () => {
  loading.value.cleanExpired = true;
  try {
    const count = await pdfCacheManager.cleanupExpiredCache();
    ElMessage.success(`清理了 ${count} 个过期缓存`);
    await refreshStats();
  } catch (error) {
    console.error('清理过期缓存失败:', error);
    ElMessage.error('清理过期缓存失败');
  } finally {
    loading.value.cleanExpired = false;
  }
};

// 清理指定合同缓存
const clearContractCache = async () => {
  if (!contractId.value) return;

  loading.value.clearContract = true;
  try {
    await cacheSync.clearContractCache(contractId.value);
    ElMessage.success(`合同${contractId.value}缓存清理成功`);
    contractId.value = '';
    await refreshStats();
  } catch (error) {
    console.error('清理合同缓存失败:', error);
    ElMessage.error('清理合同缓存失败');
  } finally {
    loading.value.clearContract = false;
  }
};

// 删除单个缓存项
const deleteCacheItem = async (url) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除此缓存项吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    await pdfCacheManager.deleteCached(url);
    ElMessage.success('缓存项删除成功');
    await refreshStats();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除缓存项失败:', error);
      ElMessage.error('删除缓存项失败');
    }
  }
};

// 组件挂载时获取缓存状态
onMounted(() => {
  refreshStats();
});
</script>

<style scoped>
.cache-manager {
  max-width: 1000px;
  margin: 0 auto;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-2 {
  gap: 0.5rem;
}

.flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.text-lg {
  font-size: 1.125rem;
}

.font-medium {
  font-weight: 500;
}

.mb-2 {
  margin-bottom: 0.5rem;
}
</style>
