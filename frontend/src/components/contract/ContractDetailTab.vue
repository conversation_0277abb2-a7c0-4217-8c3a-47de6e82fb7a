<template>
  <div class="contract-detail-tab">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon" :size="48">
        <Loading />
      </el-icon>
      <p>正在加载合同详情...</p>
    </div>

    <!-- 合同详情内容 -->
    <div v-else-if="contract" class="detail-content">
      <!-- 合同基本信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon><Document /></el-icon>
              <span>合同信息</span>
            </div>
            <div class="header-actions">
              <el-tag :type="getStatusColor(contract.status)" size="large">
                {{ formatStatus(contract.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="contract-info">
          <div class="info-item">
            <label class="info-label">流水号：</label>
            <span class="info-value">{{ contract.serial_number }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">文件名：</label>
            <span class="info-value">{{ contract.filename }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">文件大小：</label>
            <span class="info-value">{{
              formatFileSize(contract.file_size)
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">提交人：</label>
            <span class="info-value">{{ contract.submitter_name }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">审核人：</label>
            <span class="info-value">{{
              contract.reviewer_name || "未分配"
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">提交时间：</label>
            <span class="info-value">{{
              formatDateTime(contract.created_at)
            }}</span>
          </div>

          <div class="info-item full-width">
            <label class="info-label">提交说明：</label>
            <span class="info-value">{{ contract.submit_note || "无" }}</span>
          </div>


        </div>

        <!-- 审核历史区域 -->
        <div
          v-if="contract.reviewHistory && contract.reviewHistory.length > 0"
          class="review-history-section"
        >
          <h4 class="review-title">
            <el-icon><ChatDotRound /></el-icon>
            审核历史
          </h4>
          <div class="review-history">
            <div
              v-for="(review, index) in contract.reviewHistory"
              :key="review.id"
              class="review-item"
              :class="`review-item--${review.result}`"
            >
              <div class="review-header">
                <div class="reviewer-info">
                  <span class="reviewer-name">{{ review.reviewer_name }}</span>
                  <el-tag
                    :type="review.result === 'approved' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ review.result === "approved" ? "通过" : "拒绝" }}
                  </el-tag>
                  <span class="review-level">
                    {{
                      review.review_level === "county_reviewer"
                        ? "县局审核"
                        : review.review_level === "city_reviewer"
                        ? "市局审核"
                        : review.review_level === "legal_officer"
                        ? "合同编号"
                        : "审核"
                    }}
                  </span>
                </div>
                <div class="review-time">
                  {{ formatDateTime(review.created_at) }}
                </div>
              </div>
              <div v-if="review.comment" class="review-comment">
                {{ review.comment }}
              </div>
            </div>
          </div>
        </div>

        <!-- 当前审核意见（兼容旧数据） -->
        <div v-else-if="contract.review_comment" class="review-section">
          <h4 class="review-title">
            <el-icon><ChatDotRound /></el-icon>
            审核意见
          </h4>
          <div
            class="review-comment"
            :class="`review-comment--${contract.status}`"
          >
            {{ contract.review_comment }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button
            v-if="canModify(contract)"
            type="warning"
            @click="editContract"
          >
            <el-icon><Edit /></el-icon>
            修改合同
          </el-button>
        </div>
      </el-card>

      <!-- 审核操作面板 -->
      <el-card
        v-if="canReviewContract(contract, currentUser)"
        ref="reviewPanelRef"
        class="review-card"
      >
        <div class="panel-header">
          <h3 class="panel-title">
            <el-icon><EditPen /></el-icon>
            审核操作
          </h3>
        </div>

        <div class="review-form">
          <el-form
            ref="reviewFormRef"
            :model="reviewForm"
            :rules="reviewRules"
            label-width="80px"
          >
            <el-form-item label="审核结果" prop="result">
              <el-radio-group v-model="reviewForm.result" size="default">
                <el-radio value="approved" border>
                  <el-icon color="#67c23a"><CircleCheckFilled /></el-icon>
                  <span style="margin-left: 8px">通过</span>
                </el-radio>
                <el-radio value="rejected" border>
                  <el-icon color="#f56c6c"><CircleCloseFilled /></el-icon>
                  <span style="margin-left: 8px">拒绝</span>
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="审核意见" prop="comment">
              <el-input
                v-model="reviewForm.comment"
                type="textarea"
                :rows="4"
                placeholder="请输入审核意见（拒绝时至少10个字符）"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="submittingReview"
                :disabled="
                  !reviewForm.result ||
                  (reviewForm.result === 'rejected' && !reviewForm.comment)
                "
                @click="submitReviewAction"
              >
                <el-icon><Check /></el-icon>
                提交审核结果
              </el-button>
              <el-button @click="resetReviewForm">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 合同编号分配操作面板 -->
      <el-card
        v-if="canAssignNumber"
        ref="assignPanelRef"
        class="assign-card"
      >
        <div class="panel-header">
          <h3 class="panel-title">
            <el-icon><EditPen /></el-icon>
            分配合同编号
          </h3>
        </div>

        <div class="assign-form">
          <el-form
            ref="assignFormRef"
            :model="assignForm"
            :rules="assignRules"
            label-width="100px"
          >
            <el-form-item label="合同编号" prop="contract_number">
              <el-input
                v-model="assignForm.contract_number"
                placeholder="请输入合同编号"
                maxlength="50"
                show-word-limit
                clearable
              />
              <div class="form-tips">
                <el-icon><InfoFilled /></el-icon>
                <span>请输入唯一的合同编号，建议格式：HT-YYYY-NNNN</span>
              </div>
            </el-form-item>

            <el-form-item label="备注说明" prop="comment">
              <el-input
                v-model="assignForm.comment"
                type="textarea"
                :rows="3"
                placeholder="可选：添加备注说明"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="submittingAssign"
                :disabled="!assignForm.contract_number"
                @click="submitAssignment"
              >
                <el-icon><Check /></el-icon>
                分配合同编号
              </el-button>
              <el-button @click="resetAssignForm">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- PDF 预览区域 - 独立显示在页面底部 -->
    <div v-if="contract && previewUrl" class="pdf-preview-section">
      <div class="preview-header">
        <div class="preview-title">
          <el-icon><View /></el-icon>
          <span>文件预览</span>
        </div>
      </div>

      <div class="preview-content">
        <SimplePDFViewer
          :src="previewUrl"
          :filename="contract.filename"
          @load="handlePreviewLoad"
          @error="handlePreviewError"
        />
      </div>
    </div>

    <!-- PDF 预览错误状态 -->
    <div v-else-if="contract && !previewUrl" class="pdf-preview-section">
      <div class="preview-header">
        <div class="preview-title">
          <el-icon><View /></el-icon>
          <span>文件预览</span>
        </div>
      </div>

      <div class="preview-placeholder">
        <el-icon :size="64" class="placeholder-icon">
          <DocumentCopy />
        </el-icon>
        <p class="placeholder-text">无法预览此文件</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-icon :size="64" class="error-icon">
        <Warning />
      </el-icon>
      <p class="error-text">合同不存在或已被删除</p>
    </div>

    <!-- 编辑对话框 -->
    <ContractEditDialog
      v-model="showEditDialog"
      :contract="contract"
      @updated="handleContractUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Loading,
  Document,
  Edit,
  Check,
  View,
  DocumentCopy,
  Warning,
  ChatDotRound,
  EditPen,
  CircleCheckFilled,
  CircleCloseFilled,
  RefreshLeft,
  InfoFilled,
} from "@element-plus/icons-vue";

import SimplePDFViewer from "@/components/common/SimplePDFViewer.vue";
import ContractEditDialog from "@/components/contract/ContractEditDialog.vue";
import { useContracts } from "@/composables/useContracts";
import { useAuth } from "@/composables/useAuth";
import { filesAPI } from "@/api/files";
import { contractUtils, contractsAPI } from "@/api/contracts";

// 定义 props
const props = defineProps({
  contractId: {
    type: [Number, String],
    required: true,
  },
  contract: {
    type: Object,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["edit", "review", "updated"]);

// 认证信息
const { user: authUser } = useAuth();

// 临时解决方案：直接从localStorage获取用户信息
const currentUser = computed(() => {
  try {
    const savedUser = localStorage.getItem("user");
    return savedUser ? JSON.parse(savedUser) : null;
  } catch (error) {
    console.error("解析用户信息失败:", error);
    return null;
  }
});

// 合同管理
const {
  getContractDetail,
  canModify,
  canReview,
  formatStatus,
  getStatusColor,
  formatFileSize,
  formatDateTime,
  submitReview,
  getContracts,
  getStats,
} = useContracts();

// 响应式数据
const loading = ref(false);
const contract = ref(props.contract);
const showEditDialog = ref(false);

// 审核相关状态
const submittingReview = ref(false);
const reviewFormRef = ref();
const reviewPanelRef = ref();

// 审核表单
const reviewForm = ref({
  result: "",
  comment: "",
});

// 合同编号分配相关状态
const submittingAssign = ref(false);
const assignFormRef = ref();
const assignPanelRef = ref();

// 合同编号分配表单
const assignForm = reactive({
  contract_number: "",
  comment: "",
});

// 表单验证规则
const assignRules = {
  contract_number: [
    { required: true, message: "请输入合同编号", trigger: "blur" },
    { min: 1, max: 50, message: "合同编号长度应在1-50字符之间", trigger: "blur" },
    {
      pattern: /^[A-Za-z0-9\-_]+$/,
      message: "合同编号只能包含字母、数字、横线和下划线",
      trigger: "blur",
    },
  ],
  comment: [
    { max: 500, message: "备注不能超过500字符", trigger: "blur" },
  ],
};

// 动态审核表单验证规则
const reviewRules = computed(() => {
  const rules = {
    result: [{ required: true, message: "请选择审核结果", trigger: "change" }],
    comment: [],
  };

  // 根据审核结果动态设置comment验证规则
  if (reviewForm.value.result === "rejected") {
    // 审核驳回时，说明必填且至少10个字符
    rules.comment = [
      { required: true, message: "请填写审核意见", trigger: "blur" },
      { min: 10, message: "审核意见至少10个字符", trigger: "blur" },
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else if (reviewForm.value.result === "approved") {
    // 审核通过时，说明可选
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else {
    // 未选择审核结果时，暂不验证comment
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  }

  return rules;
});

// 开发环境标志
const isDev = computed(() => import.meta.env.DEV);

// 权限检查函数
const canReviewContract = (contract, user) => {
  return contractUtils.canReview(contract, user);
};

// 法规员权限检查
const canAssignNumber = computed(() => {
  if (!contract.value || !currentUser.value) {
    return false;
  }
  // 检查用户角色是否为法规员
  if (currentUser.value.role !== "legal_officer") {
    return false;
  }
  // 法规员可以为待分配编号的合同分配编号
  return contract.value.status === "pending_contract_number";
});

// 预览相关 - 使用不带token的URL，让SimplePDFViewer自动添加token
const previewUrl = computed(() => {
  if (contract.value && contract.value.id) {
    return filesAPI.getContractPreviewUrl(contract.value.id);
  }
  return null;
});

// 加载合同详情
const loadContractDetail = async () => {
  // 总是从API获取完整的合同详情，确保包含reviewHistory
  // 即使传递了contract prop，也要重新获取以确保数据完整性
  loading.value = true;
  try {
    const result = await getContractDetail(props.contractId);
    contract.value = result;
  } finally {
    loading.value = false;
  }
};

// 监听 props 变化
watch(
  () => props.contractId,
  (newId) => {
    if (newId) {
      loadContractDetail();
    }
  },
  { immediate: true },
);

watch(
  () => props.contract,
  (newContract) => {
    if (newContract && props.contractId) {
      // 当contract prop变化时，重新获取完整的合同详情
      loadContractDetail();
    }
  },
  { immediate: true },
);

// 监听合同编号分配权限变化，初始化表单
watch(
  () => canAssignNumber.value,
  (canAssign) => {
    if (canAssign) {
      resetAssignForm();
    }
  },
);

// 编辑合同
const editContract = () => {
  showEditDialog.value = true;
};

// 处理合同更新
const handleContractUpdated = (updatedContract) => {
  contract.value = updatedContract;
  emit("updated", updatedContract);
  ElMessage.success("合同修改成功");
};

// 提交审核结果
const submitReviewAction = async () => {
  try {
    // 验证表单
    await reviewFormRef.value.validate();

    const resultText = reviewForm.value.result === "approved" ? "通过" : "拒绝";
    await ElMessageBox.confirm(
      `确定要${resultText}这个合同吗？提交后无法修改审核结果。`,
      "确认提交审核结果",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    submittingReview.value = true;
    const reviewData = {
      result: reviewForm.value.result,
      comment: reviewForm.value.comment,
    };

    const result = await submitReview(contract.value.id, reviewData);

    if (result) {
      contract.value = result;
      emit("updated", result);
      resetReviewForm();
      ElMessage.success(`审核${resultText}成功`);
    }
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("提交审核失败:", error);
      }
    }
  } finally {
    submittingReview.value = false;
  }
};

// 重置审核表单
const resetReviewForm = () => {
  reviewForm.value = {
    result: "",
    comment: "",
  };
  reviewFormRef.value?.clearValidate();
};

// 生成建议的合同编号
const generateSuggestedNumber = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const random = String(Math.floor(Math.random() * 9999) + 1).padStart(4, "0");
  assignForm.contract_number = `HT-${year}${month}${day}-${random}`;
};

// 重置分配表单
const resetAssignForm = () => {
  assignForm.contract_number = "";
  assignForm.comment = "";
  if (assignFormRef.value) {
    assignFormRef.value.clearValidate();
  }
  generateSuggestedNumber();
};

// 提交合同编号分配
const submitAssignment = async () => {
  try {
    // 验证表单
    await assignFormRef.value.validate();

    await ElMessageBox.confirm(
      `确定要为合同 ${contract.value.serial_number} 分配编号 ${assignForm.contract_number} 吗？`,
      "确认分配合同编号",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    submittingAssign.value = true;

    const response = await contractsAPI.assignContractNumber(
      contract.value.id,
      {
        contract_number: assignForm.contract_number,
        comment: assignForm.comment,
      }
    );

    if (response.success) {
      ElMessage.success("合同编号分配成功");
      contract.value = response.data;
      emit("updated", response.data);
      resetAssignForm();

      // 强制刷新缓存 - 类似审核员审核后的处理
      try {
        await Promise.all([getContracts(), getStats()]);
      } catch (refreshError) {
        if (import.meta.env.DEV) {
          console.error("刷新数据失败:", refreshError);
        }
        // 静默处理刷新失败，不影响用户体验
      }
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("分配合同编号失败:", error);
      ElMessage.error(error.message || "分配合同编号失败");
    }
  } finally {
    submittingAssign.value = false;
  }
};

// PDF预览事件处理
const handlePreviewLoad = () => {};

const handlePreviewError = (error) => {
  console.error("PDF预览加载失败:", error);
};

// 组件挂载时加载数据
onMounted(() => {
  if (props.contractId && !contract.value) {
    loadContractDetail();
  }
});
</script>

<style scoped>
.contract-detail-tab {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.loading-icon {
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.contract-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.info-item {
  background: #fafbfc;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.info-item:hover {
  border-color: #1976d2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
}

.info-item.full-width {
  grid-column: 1 / -1;
  background: #f8f9fa;
  border-left: 4px solid #1976d2;
}

.info-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #5f6368;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
  line-height: 1;
}

.info-value {
  display: block;
  font-size: 15px;
  font-weight: 500;
  color: #202124;
  line-height: 1.4;
  word-break: break-word;
}

.info-item.full-width .info-value {
  font-size: 14px;
  line-height: 1.5;
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 审核面板样式 */
.review-card {
  margin-top: 20px;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.review-form {
  max-width: 600px;
}

.review-form .el-radio {
  margin-right: 20px;
  margin-bottom: 12px;
}

.review-form .el-radio__label {
  display: flex;
  align-items: center;
}

.review-form .el-form-item {
  margin-bottom: 20px;
}

.review-form .el-button {
  margin-right: 12px;
}

/* 合同编号分配卡片样式 */
.assign-card {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.panel-header {
  margin-bottom: 20px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.assign-form {
  max-width: 600px;
}

.form-tips {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.form-tips .el-icon {
  font-size: 14px;
}

.assign-form .el-button {
  margin-right: 12px;
}

/* PDF 预览区域样式 */
.pdf-preview-section {
  margin-top: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  padding: 0;
  background: #fff;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
  background: #fafafa;
}

.placeholder-icon {
  margin-bottom: 16px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #f56c6c;
}

.error-icon {
  margin-bottom: 16px;
}

.error-text {
  margin-bottom: 16px;
  font-size: 16px;
}

/* 审核历史区域 */
.review-history-section {
  margin-top: 20px;
}

.review-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.review-history {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background: #f5f5f5;
  border-radius: 6px;
  overflow: hidden;
}

.review-item {
  background: white;
  padding: 12px 16px;
  position: relative;
  transition: all 0.2s ease;
}

.review-item:hover {
  background: #fafafa;
}

.review-item--approved::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #67c23a;
}

.review-item--rejected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #f56c6c;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.reviewer-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.review-level {
  font-size: 11px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 400;
}

.review-time {
  font-size: 12px;
  color: #999;
  font-weight: 400;
}

/* 审核意见区域（兼容旧数据） */
.review-section {
  margin-top: 20px;
}

.review-comment {
  padding: 12px 16px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  line-height: 1.5;
  color: #555;
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 14px;
  position: relative;
}

.review-comment--approved::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #67c23a;
  border-radius: 3px 0 0 3px;
}

.review-comment--rejected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #f56c6c;
  border-radius: 3px 0 0 3px;
}
</style>
