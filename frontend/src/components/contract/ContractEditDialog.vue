<template>
  <el-dialog
    v-model="dialogVisible"
    title="修改合同"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div v-if="contract" class="edit-content">
      <!-- 合同基本信息 -->
      <div class="contract-info">
        <h4 class="section-title">
          <el-icon class="title-icon"><Document /></el-icon>
          合同信息
        </h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">流水号：</span>
            <span class="info-value">{{ contract.serial_number }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">当前状态：</span>
            <el-tag :type="getStatusColor(contract.status)" size="small">
              {{ formatStatus(contract.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="info-label">提交时间：</span>
            <span class="info-value">{{
              formatDateTime(contract.created_at)
            }}</span>
          </div>
        </div>
      </div>

      <!-- 编辑表单 -->
      <div class="edit-form">
        <h4 class="section-title">
          <el-icon class="title-icon"><EditPen /></el-icon>
          修改内容
        </h4>

        <el-form
          ref="editFormRef"
          :model="editForm"
          :rules="editRules"
          label-width="120px"
          size="default"
        >
          <!-- 文件修改 -->
          <el-form-item label="合同文件" prop="files">
            <div class="current-file">
              <div class="file-info">
                <el-icon class="file-icon"><DocumentCopy /></el-icon>
                <div class="file-details">
                  <span class="file-name">{{ contract.filename }}</span>
                  <span class="file-size">{{
                    formatFileSize(contract.file_size)
                  }}</span>
                </div>
              </div>
              <el-button size="small" @click="showFileUpload = !showFileUpload">
                {{ showFileUpload ? "取消更换" : "更换文件" }}
              </el-button>
            </div>

            <div v-if="showFileUpload" class="file-upload-section">
              <FileUpload
                v-model="editForm.files"
                :limit="1"
                accept=".pdf"
                :max-size="10 * 1024 * 1024"
                @success="handleFileUploadSuccess"
                @error="handleFileUploadError"
              />
              <div class="upload-tips">
                <el-icon><InfoFilled /></el-icon>
                <span>上传新文件将替换当前文件</span>
              </div>
            </div>
          </el-form-item>

          <!-- 审核人员修改 -->
          <el-form-item label="审核人员" prop="reviewer_id">
            <el-select
              v-model="editForm.reviewer_id"
              placeholder="请选择审核人员"
              style="width: 100%"
              :loading="loadingReviewers"
            >
              <el-option
                v-for="reviewer in reviewers"
                :key="reviewer.id"
                :label="`${reviewer.username} (${reviewer.role === 'admin' ? '管理员' : '审核员'})`"
                :value="reviewer.id"
              />
            </el-select>
            <div class="form-tips">
              <el-icon><InfoFilled /></el-icon>
              <span>更换审核人员将重新开始审核流程</span>
            </div>
          </el-form-item>

          <!-- 修改说明 -->
          <el-form-item label="修改说明" prop="submit_note">
            <el-input
              v-model="editForm.submit_note"
              type="textarea"
              :rows="4"
              placeholder="请说明本次修改的内容（可选）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 修改提示 -->
      <div class="edit-notice">
        <el-alert title="修改提示" type="warning" :closable="false" show-icon>
          <template #default>
            <ul class="notice-list">
              <li>修改合同后将重新进入审核流程</li>
              <li>只有待审核状态的合同可以修改</li>
              <li>修改文件或审核人员将重置审核进度</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitting" @click="handleClose">
          取消
        </el-button>
        <el-button type="primary" :loading="submitting" @click="handleUpdate">
          {{ submitting ? "保存中..." : "保存修改" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  Document,
  EditPen,
  DocumentCopy,
  InfoFilled,
} from "@element-plus/icons-vue";

import FileUpload from "@/components/common/FileUpload.vue";
import { useContracts } from "@/composables/useContracts";
import { contractsAPI } from "@/api/contracts";

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  contract: {
    type: Object,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["update:modelValue", "updated"]);

// 合同管理
const {
  updateContract,
  submitting,
  formatStatus,
  getStatusColor,
  formatDateTime,
  formatFileSize,
} = useContracts();

// 对话框显示状态
const dialogVisible = ref(props.modelValue);

// 表单引用
const editFormRef = ref();

// 是否显示文件上传
const showFileUpload = ref(false);

// 审核人员列表
const reviewers = ref([]);
const loadingReviewers = ref(false);

// 编辑表单数据
const editForm = reactive({
  files: [],
  reviewer_id: "",
  submit_note: "",
});

// 表单验证规则
const editRules = {
  reviewer_id: [
    { required: true, message: "请选择审核人员", trigger: "change" },
  ],
  submit_note: [
    { max: 500, message: "修改说明不能超过500个字符", trigger: "blur" },
  ],
};

// 监听 props 变化
watch(
  () => props.modelValue,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);

watch(
  () => props.contract,
  (newContract) => {
    if (newContract) {
      initForm();
    }
  },
);

// 监听对话框显示状态
watch(
  () => dialogVisible.value,
  (newValue) => {
    emit("update:modelValue", newValue);

    if (newValue && props.contract) {
      initForm();
      getReviewers();
    } else {
      resetForm();
    }
  },
);

// 初始化表单
const initForm = () => {
  if (props.contract) {
    editForm.reviewer_id = props.contract.reviewer_id;
    editForm.submit_note = props.contract.submit_note || "";
    editForm.files = [];
    showFileUpload.value = false;
  }
};

// 重置表单
const resetForm = () => {
  if (editFormRef.value) {
    editFormRef.value.resetFields();
  }

  editForm.files = [];
  editForm.reviewer_id = "";
  editForm.submit_note = "";
  showFileUpload.value = false;
};

// 获取审核人员列表
const getReviewers = async () => {
  try {
    loadingReviewers.value = true;
    const response = await contractsAPI.getReviewers();
    if (response.success) {
      reviewers.value = response.data;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("获取审核人员失败:", error);
    }
    ElMessage.error("获取审核人员失败");
  } finally {
    loadingReviewers.value = false;
  }
};

// 处理文件上传成功
const handleFileUploadSuccess = (fileInfo) => {};

// 处理文件上传失败
const handleFileUploadError = (error) => {
  if (import.meta.env.DEV) {
    console.error("文件上传失败:", error);
  }
};

// 处理更新
const handleUpdate = async () => {
  try {
    // 验证表单
    await editFormRef.value.validate();

    // 检查是否有修改
    const hasFileChange = editForm.files.length > 0;
    const hasReviewerChange =
      editForm.reviewer_id !== props.contract.reviewer_id;
    const hasNoteChange =
      editForm.submit_note !== (props.contract.submit_note || "");

    if (!hasFileChange && !hasReviewerChange && !hasNoteChange) {
      ElMessage.warning("没有检测到任何修改");
      return;
    }

    // 构建更新数据
    const updateData = {
      reviewer_id: editForm.reviewer_id,
      submit_note: editForm.submit_note,
    };

    // 如果上传了新文件，使用新文件信息
    if (hasFileChange) {
      const file = editForm.files[0];
      updateData.filename = file.name;
      updateData.file_path = file.filename;
      updateData.file_size = file.size;
    }

    // 更新合同
    const result = await updateContract(props.contract.id, updateData);

    if (result) {
      emit("updated", result);
      dialogVisible.value = false;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("更新合同失败:", error);
    }
  }
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};

// 组件挂载时获取审核人员
onMounted(() => {
  if (dialogVisible.value && props.contract) {
    getReviewers();
  }
});
</script>

<style scoped>
.edit-content {
  max-height: 70vh;
  overflow-y: auto;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px;
}

.title-icon {
  color: #409eff;
}

.contract-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.info-value {
  color: #303133;
}

.edit-form {
  margin-bottom: 24px;
}

.current-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 12px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 20px;
  color: #409eff;
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-upload-section {
  margin-top: 12px;
  padding: 16px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

.upload-tips,
.form-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.upload-tips .el-icon,
.form-tips .el-icon {
  color: #409eff;
}

.edit-notice {
  margin-bottom: 16px;
}

.notice-list {
  margin: 0;
  padding-left: 20px;
  color: #e6a23c;
}

.notice-list li {
  margin-bottom: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Element Plus 样式覆盖 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .current-file {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
