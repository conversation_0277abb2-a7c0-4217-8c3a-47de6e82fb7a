<template>
  <div
    class="loading-state"
    :class="[`loading-state--${type}`, { 'loading-state--overlay': overlay }]"
  >
    <!-- 骨架屏加载 -->
    <div v-if="type === 'skeleton'" class="skeleton-container">
      <div v-for="n in skeletonRows" :key="n" class="skeleton-row">
        <div v-if="showAvatar" class="skeleton-avatar"></div>
        <div class="skeleton-content">
          <div class="skeleton-line skeleton-line--title"></div>
          <div class="skeleton-line skeleton-line--subtitle"></div>
          <div class="skeleton-line skeleton-line--text"></div>
        </div>
      </div>
    </div>

    <!-- 旋转加载 -->
    <div v-else-if="type === 'spinner'" class="spinner-container">
      <div class="spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <p v-if="text" class="loading-text">{{ text }}</p>
    </div>

    <!-- 点状加载 -->
    <div v-else-if="type === 'dots'" class="dots-container">
      <div class="dots">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
      <p v-if="text" class="loading-text">{{ text }}</p>
    </div>

    <!-- 进度条加载 -->
    <div v-else-if="type === 'progress'" class="progress-container">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
      </div>
      <p v-if="text" class="loading-text">{{ text }} {{ progress }}%</p>
    </div>

    <!-- 脉冲加载 -->
    <div v-else-if="type === 'pulse'" class="pulse-container">
      <div class="pulse-circle"></div>
      <p v-if="text" class="loading-text">{{ text }}</p>
    </div>

    <!-- 默认加载 -->
    <div v-else class="default-container">
      <el-icon class="loading-icon" :size="iconSize">
        <Loading />
      </el-icon>
      <p v-if="text" class="loading-text">{{ text }}</p>
    </div>
  </div>
</template>

<script setup>
import { Loading } from "@element-plus/icons-vue";

// 定义 props
</script>

<style scoped>
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  min-height: 200px;
}

.loading-state--overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 1000;
  backdrop-filter: blur(2px);
}

/* 默认加载 */
.default-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: #606266;
  margin: 0;
  text-align: center;
}

/* 骨架屏 */
.skeleton-container {
  width: 100%;
  max-width: 600px;
}

.skeleton-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.skeleton-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-content {
  flex: 1;
}

.skeleton-line {
  height: 12px;
  border-radius: 6px;
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 8px;
}

.skeleton-line--title {
  width: 60%;
  height: 16px;
}

.skeleton-line--subtitle {
  width: 80%;
  height: 14px;
}

.skeleton-line--text {
  width: 40%;
  margin-bottom: 0;
}

/* 旋转加载 */
.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.spinner {
  position: relative;
  width: 48px;
  height: 48px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  animation-delay: 0.1s;
  border-top-color: #67c23a;
}

.spinner-ring:nth-child(3) {
  animation-delay: 0.2s;
  border-top-color: #e6a23c;
}

/* 点状加载 */
.dots-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.dots {
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #409eff;
  animation: dot-bounce 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

/* 进度条加载 */
.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
  max-width: 300px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 4px;
  transition: width 0.3s ease;
  animation: progress-shine 2s infinite;
}

/* 脉冲加载 */
.pulse-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.pulse-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #409eff;
  animation: pulse 2s infinite;
}

/* 动画定义 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dot-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes progress-shine {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-state {
    padding: 20px 16px;
    min-height: 150px;
  }

  .skeleton-row {
    padding: 12px;
  }

  .skeleton-avatar {
    width: 40px;
    height: 40px;
  }

  .spinner,
  .pulse-circle {
    width: 40px;
    height: 40px;
  }

  .loading-text {
    font-size: 12px;
  }
}
</style>
