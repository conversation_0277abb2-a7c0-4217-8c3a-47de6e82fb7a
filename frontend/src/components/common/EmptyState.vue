<template>
  <div
    class="empty-state"
    :class="[`empty-state--${type}`, { 'empty-state--compact': compact }]"
  >
    <!-- 图标 -->
    <div class="empty-icon">
      <el-icon v-if="icon" :size="iconSize" :class="iconClass">
        <component :is="icon" />
      </el-icon>
      <div v-else class="default-icon">
        <svg viewBox="0 0 64 64" :width="iconSize" :height="iconSize">
          <circle
            cx="32"
            cy="32"
            r="30"
            fill="none"
            stroke="#e4e7ed"
            stroke-width="2"
            stroke-dasharray="5,5"
          />
          <circle cx="32" cy="32" r="8" fill="#c0c4cc" />
        </svg>
      </div>
    </div>

    <!-- 标题 -->
    <h3 v-if="title" class="empty-title">{{ title }}</h3>

    <!-- 描述 -->
    <p v-if="description" class="empty-description">{{ description }}</p>

    <!-- 操作按钮 -->
    <div v-if="showAction" class="empty-actions">
      <el-button
        v-if="actionText"
        :type="actionType"
        :size="actionSize"
        @click="handleAction"
      >
        <el-icon v-if="actionIcon">
          <component :is="actionIcon" />
        </el-icon>
        {{ actionText }}
      </el-button>

      <!-- 自定义操作插槽 -->
      <slot name="actions"></slot>
    </div>

    <!-- 额外内容插槽 -->
    <div v-if="$slots.extra" class="empty-extra">
      <slot name="extra"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

// 定义 props
const props = defineProps({
  type: {
    type: String,
    default: "default",
    validator: (value) =>
      [
        "default",
        "no-data",
        "no-search",
        "error",
        "network",
        "permission",
      ].includes(value),
  },
  icon: {
    type: [String, Object],
    default: null,
  },
  iconSize: {
    type: Number,
    default: 64,
  },
  title: {
    type: String,
    default: "",
  },
  description: {
    type: String,
    default: "",
  },
  actionText: {
    type: String,
    default: "",
  },
  actionType: {
    type: String,
    default: "primary",
  },
  actionSize: {
    type: String,
    default: "default",
  },
  actionIcon: {
    type: [String, Object],
    default: null,
  },
  showAction: {
    type: Boolean,
    default: true,
  },
  compact: {
    type: Boolean,
    default: false,
  },
});

// 定义 emits
const emit = defineEmits(["action"]);

// 计算属性
const iconClass = computed(() => {
  const typeClasses = {
    "no-data": "empty-icon--no-data",
    "no-search": "empty-icon--no-search",
    error: "empty-icon--error",
    network: "empty-icon--network",
    permission: "empty-icon--permission",
  };
  return typeClasses[props.type] || "empty-icon--default";
});

// 获取配置

// 处理操作
const handleAction = () => {
  emit("action", props.type);
};
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;
}

.empty-state--compact {
  padding: 40px 20px;
  min-height: 200px;
}

.empty-icon {
  margin-bottom: 24px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.empty-state:hover .empty-icon {
  opacity: 1;
}

.empty-icon--default {
  color: #c0c4cc;
}

.empty-icon--no-data {
  color: #909399;
}

.empty-icon--no-search {
  color: #409eff;
}

.empty-icon--error {
  color: #f56c6c;
}

.empty-icon--network {
  color: #e6a23c;
}

.empty-icon--permission {
  color: #f56c6c;
}

.default-icon {
  display: inline-block;
  animation: rotate-slow 8s linear infinite;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px;
  line-height: 1.4;
}

.empty-description {
  font-size: 14px;
  color: #606266;
  margin: 0 0 24px;
  line-height: 1.6;
  max-width: 400px;
}

.empty-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.empty-extra {
  margin-top: 24px;
}

/* 紧凑模式 */
.empty-state--compact .empty-icon {
  margin-bottom: 16px;
}

.empty-state--compact .empty-title {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-state--compact .empty-description {
  font-size: 13px;
  margin-bottom: 16px;
}

/* 动画 */
@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.empty-state {
  animation: fade-in 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .empty-state {
    padding: 40px 16px;
    min-height: 250px;
  }

  .empty-state--compact {
    padding: 30px 16px;
    min-height: 180px;
  }

  .empty-title {
    font-size: 16px;
  }

  .empty-description {
    font-size: 13px;
    max-width: 300px;
  }

  .empty-actions {
    flex-direction: column;
    align-items: center;
  }

  .empty-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .empty-state {
    padding: 30px 12px;
  }

  .empty-icon {
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 15px;
    margin-bottom: 8px;
  }

  .empty-description {
    font-size: 12px;
    margin-bottom: 16px;
  }
}
</style>
