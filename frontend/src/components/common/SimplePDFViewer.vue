<template>
  <div class="simple-pdf-viewer">
    <div class="pdf-header">
      <h3>PDF预览 - {{ filename }}</h3>
      <div class="pdf-actions">
        <div v-if="cacheStatus" class="cache-status">
          <el-tooltip :content="cacheStatusText" placement="top">
            <el-icon :class="cacheStatusClass">
              <Download v-if="cacheStatus === 'network'" />
              <Folder v-else />
            </el-icon>
          </el-tooltip>
        </div>
        <el-button size="small" title="全屏预览" @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
          全屏
        </el-button>
      </div>
    </div>

    <div class="pdf-content">
      <!-- 使用iframe作为备选方案 -->
      <div v-if="useIframe" class="iframe-container">
        <iframe
          ref="iframeRef"
          :src="pdfUrl"
          width="100%"
          height="100%"
          style="min-height: 600px"
          frameborder="0"
          @load="handleIframeLoad"
          @error="handleIframeError"
        ></iframe>
      </div>

      <!-- vue-pdf-embed组件 -->
      <div v-else class="vue-pdf-container">
        <div v-if="loading" class="loading-state">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <p>正在加载...</p>
        </div>

        <div v-else-if="error" class="error-state">
          <el-icon class="error-icon"><DocumentDelete /></el-icon>
          <p>{{ error }}</p>
          <el-button type="primary" @click="switchToIframe">
            使用备选预览方式
          </el-button>
        </div>

        <VuePdfEmbed
          v-else
          ref="pdfEmbedRef"
          :source="src"
          :page="currentPage"
          :text-layer="false"
          :annotation-layer="false"
          class="pdf-embed"
          @loading-failed="handleLoadingFailed"
          @loaded="handleLoaded"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import VuePdfEmbed from "vue-pdf-embed";
import {
  Loading,
  DocumentDelete,
  FullScreen,
  Download,
  Folder,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import pdfCacheService from "@/services/pdfCacheService.js";

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  filename: {
    type: String,
    default: "document.pdf",
  },
});

const emit = defineEmits(["load", "error"]);

// 响应式数据
const loading = ref(true);
const error = ref("");
const useIframe = ref(true); // 默认使用iframe，避免vue-pdf-embed的"Extracting text from PDF"提示
const iframeRef = ref(null);
const pdfEmbedRef = ref(null);

// PDF控制相关
const currentPage = ref(1);
const totalPages = ref(0);

// 缓存相关状态
const cacheStatus = ref(null); // 'cache', 'network', 'cache-offline'
const cachedBlobUrl = ref(null); // 缓存的Blob URL

// 计算属性
const pdfUrl = computed(() => {
  // 如果有缓存的Blob URL，优先使用
  if (cachedBlobUrl.value) {
    return cachedBlobUrl.value;
  }

  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
  const isServerDeployment = apiBaseUrl && apiBaseUrl.startsWith("http");

  // 检查是否为本地API端点
  const isLocalApiEndpoint =
    props.src.includes("/api/files/") ||
    props.src.startsWith("/api/") ||
    (apiBaseUrl && props.src.startsWith(apiBaseUrl));

  // 如果是外部URL（不是本地API），使用Google Docs Viewer
  if (props.src.startsWith("http") && !isLocalApiEndpoint) {
    return `https://docs.google.com/viewer?url=${encodeURIComponent(props.src)}&embedded=true`;
  }

  // 处理本地API端点
  if (isLocalApiEndpoint) {
    let url = props.src;

    // 如果是完整的HTTP URL且不是服务器部署环境，转换为相对路径使用Vite代理
    if (url.startsWith("http") && !isServerDeployment) {
      const urlObj = new URL(url);
      url = urlObj.pathname + urlObj.search;
    }

    // 添加token（如果URL中还没有token）
    if (!url.includes("token=")) {
      const token = localStorage.getItem("token");
      if (token) {
        const separator = url.includes("?") ? "&" : "?";
        url = `${url}${separator}token=${token}`;
      }
    }

    // 添加PDF查看器参数：适应页面宽度
    const separator = url.includes("?") ? "&" : "?";
    url = `${url}${separator}view=FitH&zoom=page-width&toolbar=1&navpanes=0&scrollbar=1`;

    return url;
  }

  return props.src;
});

// 缓存状态相关计算属性
const cacheStatusText = computed(() => {
  switch (cacheStatus.value) {
    case "cache":
      return "从本地缓存加载";
    case "network":
      return "从网络加载";
    case "cache-offline":
      return "离线模式（从缓存加载）";
    default:
      return "";
  }
});

const cacheStatusClass = computed(() => {
  switch (cacheStatus.value) {
    case "cache":
      return "cache-icon cache-local";
    case "network":
      return "cache-icon cache-network";
    case "cache-offline":
      return "cache-icon cache-offline";
    default:
      return "";
  }
});

// 事件处理
const handleLoaded = (pdf) => {
  loading.value = false;
  error.value = "";
  totalPages.value = pdf.numPages || 1;
  ElMessage.success(`PDF加载成功，共${totalPages.value}页`);
  emit("load", pdf);
};

const handleLoadingFailed = (err) => {
  loading.value = false;
  error.value = err.message || "PDF加载失败";
  console.error("PDF加载失败:", err);
  emit("error", err);
};

const handleIframeLoad = () => {
  loading.value = false;
  ElMessage.success("PDF预览加载成功");
};

const handleIframeError = () => {
  error.value = "PDF预览加载失败";
  ElMessage.error("PDF预览加载失败");
};

const switchToIframe = () => {
  useIframe.value = true;
  loading.value = true;
  error.value = "";
};

// 打印功能已移除 - 用户可以通过浏览器的打印功能或全屏模式进行打印

// 全屏功能
const toggleFullscreen = () => {
  if (props.src) {
    // 使用带token的URL
    const authenticatedUrl = pdfUrl.value;

    // 获取屏幕尺寸
    const screenWidth = screen.availWidth || screen.width;
    const screenHeight = screen.availHeight || screen.height;

    // 创建最大化的全屏预览窗口，隐藏地址栏和工具栏
    const windowFeatures = [
      `width=${screenWidth}`,
      `height=${screenHeight}`,
      "left=0",
      "top=0",
      "fullscreen=yes",
      "location=no", // 隐藏地址栏
      "menubar=no", // 隐藏菜单栏
      "toolbar=no", // 隐藏工具栏
      "status=no", // 隐藏状态栏
      "titlebar=no", // 隐藏标题栏
      "scrollbars=yes", // 保留滚动条
      "resizable=yes", // 允许调整大小
    ].join(",");

    const fullscreenWindow = window.open(
      authenticatedUrl,
      "_blank",
      windowFeatures,
    );

    if (fullscreenWindow) {
      // 确保窗口获得焦点
      fullscreenWindow.focus();

      // 尝试最大化窗口（某些浏览器支持）
      try {
        fullscreenWindow.moveTo(0, 0);
        fullscreenWindow.resizeTo(screenWidth, screenHeight);
      } catch (error) {}

      ElMessage.success("已在新窗口中打开全屏预览");
    } else {
      ElMessage.warning("无法打开全屏预览，请检查浏览器弹窗设置");
    }
  } else {
    ElMessage.warning("PDF文件未加载，无法进行全屏预览");
  }
};

// 加载PDF文件（支持缓存）
const loadPDFWithCache = async () => {
  if (!props.src) return;

  loading.value = true;
  error.value = "";
  cacheStatus.value = null;

  try {
    // 检查是否为API端点
    const isApiEndpoint =
      props.src.includes("/api/files/") || props.src.startsWith("/api/");

    if (isApiEndpoint) {
      // 使用缓存服务加载PDF
      const result = await pdfCacheService.getPDF(props.src, {
        metadata: {
          filename: props.filename,
        },
      });

      cacheStatus.value = result.source;

      // 创建Blob URL用于iframe显示
      cachedBlobUrl.value = pdfCacheService.createBlobUrl(result.data);
    }

    loading.value = false;
  } catch (error) {
    console.error("PDF加载失败:", error);
    loading.value = false;
    error.value = error.message || "PDF加载失败";

    // 加载失败时清理状态
    cacheStatus.value = null;
    if (cachedBlobUrl.value) {
      pdfCacheService.revokeBlobUrl(cachedBlobUrl.value);
      cachedBlobUrl.value = null;
    }
  }
};

// 组件挂载时的处理
onMounted(async () => {
  // 对于API端点，使用缓存加载
  if (props.src.includes("/api/files/") || props.src.startsWith("/api/")) {
    useIframe.value = true;
    await loadPDFWithCache();
  } else {
    // 对于外部URL，使用传统方式
    useIframe.value = true;
    loading.value = true;
  }
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (cachedBlobUrl.value) {
    pdfCacheService.revokeBlobUrl(cachedBlobUrl.value);
    cachedBlobUrl.value = null;
  }
});
</script>

<style scoped>
.simple-pdf-viewer {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.pdf-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.cache-status {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.cache-icon {
  font-size: 16px;
  transition: color 0.3s ease;
}

.cache-local {
  color: #67c23a; /* 绿色 - 本地缓存 */
}

.cache-network {
  color: #409eff; /* 蓝色 - 网络加载 */
}

.cache-offline {
  color: #e6a23c; /* 橙色 - 离线模式 */
}

.pdf-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.pdf-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pdf-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.pdf-content {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.iframe-container {
  width: 100%;
  height: 100%;
  flex: 1;
}

.vue-pdf-container {
  flex: 1;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
}

.loading-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: rotate 2s linear infinite;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #f56c6c;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.pdf-embed {
  width: 100%;
  height: 800px;
}

/* 打印样式 */
@media print {
  .simple-pdf-viewer {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .pdf-header {
    display: none !important;
  }

  .pdf-content {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .iframe-container,
  .vue-pdf-container {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .pdf-embed {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 隐藏所有其他页面元素 */
  body > *:not(.simple-pdf-viewer) {
    display: none !important;
  }

  /* 确保PDF内容可见 */
  iframe {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
  }
}
</style>
