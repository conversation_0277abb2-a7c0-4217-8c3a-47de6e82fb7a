<template>
  <div class="error-boundary">
    <!-- 正常内容 -->
    <div v-if="!hasError" class="error-boundary__content">
      <slot></slot>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-boundary__error">
      <div class="error-container">
        <!-- 错误图标 -->
        <div class="error-icon">
          <el-icon :size="64" class="error-icon__svg">
            <WarningFilled />
          </el-icon>
        </div>

        <!-- 错误信息 -->
        <div class="error-content">
          <h3 class="error-title">{{ errorTitle }}</h3>
          <p class="error-message">{{ errorMessage }}</p>

          <!-- 错误详情（开发模式） -->
          <div v-if="showDetails && errorDetails" class="error-details">
            <el-collapse>
              <el-collapse-item title="错误详情" name="details">
                <pre class="error-stack">{{ errorDetails }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <el-button type="primary" @click="handleRetry">
            <el-icon><Refresh /></el-icon>
            重试
          </el-button>

          <el-button @click="handleReload">
            <el-icon><RefreshRight /></el-icon>
            刷新页面
          </el-button>

          <el-button v-if="showReport" type="warning" @click="handleReport">
            <el-icon><Warning /></el-icon>
            报告问题
          </el-button>
        </div>

        <!-- 建议操作 -->
        <div class="error-suggestions">
          <h4 class="suggestions-title">您可以尝试：</h4>
          <ul class="suggestions-list">
            <li>检查网络连接是否正常</li>
            <li>刷新页面重新加载</li>
            <li>清除浏览器缓存</li>
            <li>如果问题持续存在，请联系技术支持</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onErrorCaptured, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  WarningFilled,
  Refresh,
  RefreshRight,
  Warning,
} from "@element-plus/icons-vue";

// 定义 props
const props = defineProps({
  fallbackTitle: {
    type: String,
    default: "页面出现错误",
  },
  fallbackMessage: {
    type: String,
    default: "抱歉，页面加载时出现了错误，请尝试刷新页面",
  },
  showDetails: {
    type: Boolean,
    default: process.env.NODE_ENV === "development",
  },
  showReport: {
    type: Boolean,
    default: true,
  },
  autoRetry: {
    type: Boolean,
    default: false,
  },
  retryDelay: {
    type: Number,
    default: 3000,
  },
  maxRetries: {
    type: Number,
    default: 3,
  },
});

// 定义 emits
const emit = defineEmits(["error", "retry", "reload", "report"]);

// 状态
const hasError = ref(false);
const error = ref(null);
const retryCount = ref(0);
const retryTimer = ref(null);

// 计算属性
const errorTitle = computed(() => {
  if (error.value?.title) return error.value.title;
  return props.fallbackTitle;
});

const errorMessage = computed(() => {
  if (error.value?.message) return error.value.message;
  return props.fallbackMessage;
});

const errorDetails = computed(() => {
  if (!error.value) return null;

  const details = [];

  if (error.value.stack) {
    details.push(`Stack Trace:\n${error.value.stack}`);
  }

  if (error.value.componentStack) {
    details.push(`Component Stack:\n${error.value.componentStack}`);
  }

  if (error.value.info) {
    details.push(
      `Additional Info:\n${JSON.stringify(error.value.info, null, 2)}`,
    );
  }

  return details.join("\n\n");
});

// 错误捕获
onErrorCaptured((err, instance, info) => {
  // 只在开发环境输出错误信息
  if (import.meta.env.DEV) {
    console.error("ErrorBoundary caught an error:", err);
  }

  hasError.value = true;
  error.value = {
    message: err.message,
    stack: err.stack,
    componentStack: info,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
  };

  // 发送错误事件
  emit("error", error.value);

  // 自动重试
  if (props.autoRetry && retryCount.value < props.maxRetries) {
    startAutoRetry();
  }

  // 阻止错误继续传播
  return false;
});

// 全局错误处理
onMounted(() => {
  // 监听未捕获的Promise错误
  window.addEventListener("unhandledrejection", handleUnhandledRejection);

  // 监听JavaScript错误
  window.addEventListener("error", handleGlobalError);
});

// 处理未捕获的Promise错误
const handleUnhandledRejection = (event) => {
  // 只在开发环境输出错误信息
  if (import.meta.env.DEV) {
    console.error("Unhandled promise rejection:", event.reason);
  }

  hasError.value = true;
  error.value = {
    message: event.reason?.message || "未处理的Promise错误",
    stack: event.reason?.stack,
    type: "unhandledrejection",
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
  };

  emit("error", error.value);

  // 阻止默认的错误处理
  event.preventDefault();
};

// 处理全局JavaScript错误
const handleGlobalError = (event) => {
  // 只在开发环境输出错误信息
  if (import.meta.env.DEV) {
    console.error("Global error:", event.error);
  }

  hasError.value = true;
  error.value = {
    message: event.error?.message || event.message || "未知错误",
    stack: event.error?.stack,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    type: "javascript",
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
  };

  emit("error", error.value);
};

// 开始自动重试
const startAutoRetry = () => {
  if (retryTimer.value) {
    clearTimeout(retryTimer.value);
  }

  retryTimer.value = setTimeout(() => {
    handleRetry();
  }, props.retryDelay);
};

// 处理重试
const handleRetry = () => {
  retryCount.value++;
  hasError.value = false;
  error.value = null;

  if (retryTimer.value) {
    clearTimeout(retryTimer.value);
    retryTimer.value = null;
  }

  emit("retry", retryCount.value);

  ElMessage.info(`正在重试... (${retryCount.value}/${props.maxRetries})`);
};

// 处理页面刷新
const handleReload = () => {
  emit("reload");
  window.location.reload();
};

// 处理错误报告
const handleReport = async () => {
  try {
    const reportData = {
      error: error.value,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("userId") || "anonymous",
    };

    // 显示报告对话框
    await ElMessageBox.confirm(
      "是否要将错误信息发送给技术支持团队？这将有助于我们改进产品。",
      "报告错误",
      {
        confirmButtonText: "发送报告",
        cancelButtonText: "取消",
        type: "info",
      },
    );

    // 发送错误报告
    emit("report", reportData);

    // 这里可以调用API发送错误报告
    // await errorReportAPI.send(reportData)

    ElMessage.success("错误报告已发送，感谢您的反馈！");
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("发送错误报告失败:", error);
      }
      ElMessage.error("发送错误报告失败");
    }
  }
};

// 重置错误状态
const reset = () => {
  hasError.value = false;
  error.value = null;
  retryCount.value = 0;

  if (retryTimer.value) {
    clearTimeout(retryTimer.value);
    retryTimer.value = null;
  }
};

// 暴露方法
defineExpose({
  reset,
  hasError: computed(() => hasError.value),
  error: computed(() => error.value),
});
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-boundary__content {
  width: 100%;
  height: 100%;
}

.error-boundary__error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
  background: #fafafa;
}

.error-container {
  max-width: 600px;
  text-align: center;
}

.error-icon {
  margin-bottom: 24px;
}

.error-icon__svg {
  color: #f56c6c;
  animation: pulse 2s infinite;
}

.error-content {
  margin-bottom: 32px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px;
}

.error-message {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin: 0 0 24px;
}

.error-details {
  text-align: left;
  margin-top: 24px;
}

.error-stack {
  background: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 32px;
}

.error-suggestions {
  text-align: left;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
}

.suggestions-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px;
}

.suggestions-list {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  line-height: 1.8;
}

.suggestions-list li {
  margin-bottom: 4px;
}

/* 动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-boundary__error {
    padding: 20px 16px;
    min-height: 300px;
  }

  .error-title {
    font-size: 20px;
  }

  .error-message {
    font-size: 14px;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }

  .error-suggestions {
    padding: 16px;
  }
}
</style>
