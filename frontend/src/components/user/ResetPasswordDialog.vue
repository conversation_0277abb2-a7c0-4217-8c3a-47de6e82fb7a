<template>
  <el-dialog
    v-model="dialogVisible"
    title="重置密码"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div v-if="user" class="reset-content">
      <!-- 用户信息 -->
      <div class="user-info">
        <h4 class="info-title">
          <el-icon><User /></el-icon>
          用户信息
        </h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">用户名：</span>
            <span class="info-value">{{ user.username }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">真实姓名：</span>
            <span class="info-value">{{ user.real_name || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">角色：</span>
            <el-tag :type="getRoleColor(user.role)" size="small">
              {{ formatRole(user.role) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 密码重置表单 -->
      <div class="reset-form">
        <h4 class="form-title">
          <el-icon><Lock /></el-icon>
          新密码设置
        </h4>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          size="default"
        >
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="formData.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 重置提示 -->
      <div class="reset-notice">
        <el-alert
          title="重置密码提示"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul class="notice-list">
              <li>密码重置后，用户需要使用新密码登录</li>
              <li>建议通知用户及时修改密码</li>
              <li>新密码长度至少6个字符</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitting" @click="handleClose">
          取消
        </el-button>
        <el-button type="primary" :loading="submitting" @click="handleReset">
          {{ submitting ? "重置中..." : "确认重置" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { ElMessageBox } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";

import { useUsers } from "@/composables/useUsers";

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  user: {
    type: Object,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["update:modelValue", "reset"]);

// 用户管理
const { resetUserPassword, submitting } = useUsers();

// 对话框显示状态
const dialogVisible = ref(props.modelValue);

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive({
  newPassword: "",
  confirmPassword: "",
});

// 表单验证规则
const formRules = {
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度至少6个字符", trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, message: "请确认新密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

// 监听 props 变化
watch(
  () => props.modelValue,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);

// 监听对话框显示状态
watch(
  () => dialogVisible.value,
  (newValue) => {
    emit("update:modelValue", newValue);

    if (!newValue) {
      resetForm();
    }
  },
);

// 重置表单
const resetForm = () => {
  formData.newPassword = "";
  formData.confirmPassword = "";

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 格式化角色
const formatRole = (role) => {
  const roleMap = {
    employee: "员工",
    county_reviewer: "县级审核员",
    city_reviewer: "市级审核员",
    legal_officer: "市局法规员",
    admin: "管理员",
  };
  return roleMap[role] || role;
};

// 获取角色颜色
const getRoleColor = (role) => {
  const colorMap = {
    employee: "info",
    county_reviewer: "warning",
    city_reviewer: "warning",
    legal_officer: "success",
    admin: "danger",
  };
  return colorMap[role] || "info";
};

// 处理重置
const handleReset = async () => {
  try {
    // 验证表单
    await formRef.value.validate();

    // 确认重置
    await ElMessageBox.confirm(
      `确定要重置用户 ${props.user.username} 的密码吗？`,
      "确认重置密码",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    // 重置密码
    await resetUserPassword(props.user.id, formData.newPassword);

    emit("reset");
    dialogVisible.value = false;
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("重置密码失败:", error);
      }
    }
  }
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.reset-content {
  max-height: 60vh;
  overflow-y: auto;
}

.info-title,
.form-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px;
}

.user-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-grid {
  display: grid;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-value {
  color: #303133;
}

.reset-form {
  margin-bottom: 24px;
}

.reset-notice {
  margin-bottom: 16px;
}

.notice-list {
  margin: 0;
  padding-left: 20px;
  color: #e6a23c;
}

.notice-list li {
  margin-bottom: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Element Plus 样式覆盖 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
