/**
 * Vue 应用主入口文件
 * 配置 Vue 应用、Element Plus、路由等
 */

import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";

// Element Plus
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/dark/css-vars.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";

// Tailwind CSS
import "./styles/tailwind.css";

// 自定义样式
import "./styles/main.css";

// 自定义指令
import { registerDirectives } from "./utils/directives";

// 创建 Vue 应用实例
const app = createApp(App);

// 创建 Pinia 实例
const pinia = createPinia();

// 使用 Element Plus
app.use(ElementPlus, {
  // Element Plus 全局配置
  size: "default",
  zIndex: 3000,
});

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 使用 Pinia
app.use(pinia);

// 使用路由
app.use(router);

// 注册自定义指令
registerDirectives(app);

// 全局错误处理器
app.config.errorHandler = (err, instance, info) => {
  // 构建错误信息对象
  const errorInfo = {
    message: err.message,
    stack: err.stack,
    componentName:
      instance?.$options.name || instance?.$options.__name || "Unknown",
    errorInfo: info,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
  };

  // 开发环境：输出详细错误信息
  if (import.meta.env.DEV) {
    console.group("🚨 Vue 应用错误");
    console.error("错误对象:", err);
    console.error("组件实例:", instance);
    console.error("错误信息:", info);
    console.error("完整错误信息:", errorInfo);
    console.groupEnd();
  }

  // 生产环境：发送错误到监控服务
  if (import.meta.env.PROD) {
    // 这里可以集成错误监控服务，如 Sentry
    try {
      // 示例：发送到错误监控服务
      // Sentry.captureException(err, {
      //   tags: {
      //     component: errorInfo.componentName,
      //     errorInfo: info
      //   },
      //   extra: errorInfo
      // });
      // 或者发送到自定义错误收集API
      // fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorInfo)
      // }).catch(() => {}); // 静默处理发送失败
    } catch (reportError) {
      // 静默处理错误报告失败
    }
  }

  // 触发自定义错误事件，供其他组件监听
  window.dispatchEvent(
    new CustomEvent("vue-error", {
      detail: errorInfo,
    }),
  );
};

// 全局未捕获Promise错误处理
window.addEventListener("unhandledrejection", (event) => {
  // 忽略请求取消错误，这是正常的业务逻辑
  if (
    event.reason?.name === "CanceledError" ||
    event.reason?.code === "ERR_CANCELED" ||
    event.reason?.message?.includes("取消")
  ) {
    event.preventDefault();
    return;
  }

  // 忽略用户登出过程中的请求阻止错误
  if (event.reason?.message === "用户正在登出") {
    event.preventDefault();
    return;
  }

  const errorInfo = {
    type: "unhandledrejection",
    reason: event.reason,
    message: event.reason?.message || "未处理的Promise错误",
    stack: event.reason?.stack,
    timestamp: new Date().toISOString(),
    url: window.location.href,
  };

  // 开发环境输出错误
  if (import.meta.env.DEV) {
    console.group("🚨 未捕获的Promise错误");
    console.error("错误原因:", event.reason);
    console.error("完整信息:", errorInfo);
    console.groupEnd();
  }

  // 生产环境发送到监控服务
  if (import.meta.env.PROD) {
    // 发送到错误监控服务
    // 同样可以集成 Sentry 或自定义API
  }

  // 阻止默认的错误处理（避免在控制台显示）
  event.preventDefault();
});

// 全局属性配置
app.config.globalProperties.$ELEMENT = {
  size: "default",
  zIndex: 3000,
};

// 开发环境配置
if (import.meta.env.DEV) {
  app.config.performance = true;
}

// 初始化认证状态后再挂载应用
import { useUserStore } from "./stores/user";
import cacheSync from "./utils/cacheSync";

const initApp = async () => {
  try {
    // 1. 初始化用户状态
    const userStore = useUserStore();
    await userStore.initAuth();

    // 2. 如果用户已登录，初始化缓存同步
    if (userStore.isAuthenticated) {
      await cacheSync.init();
    }

    // 3. 挂载应用
    app.mount("#app");
  } catch (error) {
    // 应用初始化失败，在开发环境输出错误
    if (import.meta.env.DEV) {
      console.error("❌ 应用初始化失败:", error);
    }
    // 即使初始化失败，也要挂载应用
    app.mount("#app");
  }
};

// 启动应用
initApp();
