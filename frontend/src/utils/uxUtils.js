/**
 * 用户体验优化工具
 * 提供各种UX优化功能
 */

import { ElMessage, ElNotification, ElLoading } from "element-plus";
import {
  formatTime,
  formatRelativeTime as formatRelativeTimeUtil,
} from "@/utils/dateUtils";

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout;

  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };

    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) func.apply(this, args);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle;

  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * 平滑滚动到指定元素
 * @param {string|Element} target - 目标元素或选择器
 * @param {Object} options - 滚动选项
 */
export function smoothScrollTo(target, options = {}) {
  const element =
    typeof target === "string" ? document.querySelector(target) : target;

  if (!element) {
    console.warn("滚动目标元素未找到:", target);
    return;
  }

  const defaultOptions = {
    behavior: "smooth",
    block: "start",
    inline: "nearest",
    ...options,
  };

  element.scrollIntoView(defaultOptions);
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {string} successMessage - 成功提示消息
 * @returns {Promise<boolean>} 是否复制成功
 */
export async function copyToClipboard(text, successMessage = "已复制到剪贴板") {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 使用现代 Clipboard API
      await navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand("copy");
      document.body.removeChild(textArea);

      if (!successful) {
        throw new Error("复制失败");
      }
    }

    ElMessage.success(successMessage);
    return true;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("复制失败:", error);
    }
    ElMessage.error("复制失败，请手动复制");
    return false;
  }
}

/**
 * 文件下载
 * @param {Blob|string} data - 文件数据或URL
 * @param {string} filename - 文件名
 * @param {string} mimeType - MIME类型
 */
export function downloadFile(
  data,
  filename,
  mimeType = "application/octet-stream",
) {
  let url;

  if (typeof data === "string") {
    url = data;
  } else {
    const blob = new Blob([data], { type: mimeType });
    url = URL.createObjectURL(blob);
  }

  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  link.style.display = "none";

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // 清理对象URL
  if (typeof data !== "string") {
    setTimeout(() => URL.revokeObjectURL(url), 100);
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}

/**
 * 格式化时间（使用统一的时区转换）
 * @param {string|Date} date - 日期（UTC时间）
 * @param {string} format - 格式
 * @returns {string} 格式化后的北京时间
 */
export function formatDateTime(date, format = "YYYY-MM-DD HH:mm:ss") {
  if (!date) return "";

  // 使用统一的时区转换工具，将UTC时间转换为北京时间显示
  return formatTime(date, format);
}

/**
 * 相对时间格式化（使用统一的时区转换）
 * @param {string|Date} date - 日期（UTC时间）
 * @returns {string} 相对时间
 */
export function formatRelativeTime(date) {
  if (!date) return "";

  // 使用统一的时区转换工具，将UTC时间转换为北京时间的相对时间显示
  return formatRelativeTimeUtil(date);
}

/**
 * 加载管理器
 */
export class LoadingManager {
  constructor() {
    this.loadingInstances = new Map();
    this.globalLoading = null;
  }

  /**
   * 显示加载状态
   * @param {string} key - 加载键
   * @param {Object} options - 加载选项
   */
  show(key = "default", options = {}) {
    const defaultOptions = {
      text: "加载中...",
      background: "rgba(0, 0, 0, 0.7)",
      ...options,
    };

    if (key === "global") {
      this.globalLoading = ElLoading.service(defaultOptions);
    } else {
      const loading = ElLoading.service(defaultOptions);
      this.loadingInstances.set(key, loading);
    }
  }

  /**
   * 隐藏加载状态
   * @param {string} key - 加载键
   */
  hide(key = "default") {
    if (key === "global") {
      if (this.globalLoading) {
        this.globalLoading.close();
        this.globalLoading = null;
      }
    } else {
      const loading = this.loadingInstances.get(key);
      if (loading) {
        loading.close();
        this.loadingInstances.delete(key);
      }
    }
  }

  /**
   * 隐藏所有加载状态
   */
  hideAll() {
    // 隐藏全局加载
    this.hide("global");

    // 隐藏所有实例
    for (const [key] of this.loadingInstances) {
      this.hide(key);
    }
  }
}

/**
 * 通知管理器
 */
export class NotificationManager {
  constructor() {
    this.notifications = new Map();
  }

  /**
   * 显示成功通知
   * @param {string} message - 消息
   * @param {Object} options - 选项
   */
  success(message, options = {}) {
    return this.show("success", message, options);
  }

  /**
   * 显示错误通知
   * @param {string} message - 消息
   * @param {Object} options - 选项
   */
  error(message, options = {}) {
    return this.show("error", message, { duration: 0, ...options });
  }

  /**
   * 显示警告通知
   * @param {string} message - 消息
   * @param {Object} options - 选项
   */
  warning(message, options = {}) {
    return this.show("warning", message, options);
  }

  /**
   * 显示信息通知
   * @param {string} message - 消息
   * @param {Object} options - 选项
   */
  info(message, options = {}) {
    return this.show("info", message, options);
  }

  /**
   * 显示通知
   * @param {string} type - 类型
   * @param {string} message - 消息
   * @param {Object} options - 选项
   */
  show(type, message, options = {}) {
    const notification = ElNotification({
      type,
      message,
      position: "top-right",
      duration: 4500,
      showClose: true,
      ...options,
    });

    const id = performance.now() + Math.random();
    this.notifications.set(id, notification);

    // 自动清理
    setTimeout(() => {
      this.notifications.delete(id);
    }, options.duration || 4500);

    return notification;
  }

  /**
   * 关闭所有通知
   */
  closeAll() {
    ElNotification.closeAll();
    this.notifications.clear();
  }
}

/**
 * 性能监控
 */
export class PerformanceMonitor {
  constructor() {
    this.marks = new Map();
    this.measures = new Map();
  }

  /**
   * 标记开始时间
   * @param {string} name - 标记名称
   */
  mark(name) {
    const timestamp = performance.now();
    this.marks.set(name, timestamp);

    if (performance.mark) {
      performance.mark(name);
    }
  }

  /**
   * 测量时间差
   * @param {string} name - 测量名称
   * @param {string} startMark - 开始标记
   * @param {string} endMark - 结束标记
   * @returns {number} 时间差（毫秒）
   */
  measure(name, startMark, endMark = null) {
    const startTime = this.marks.get(startMark);
    const endTime = endMark ? this.marks.get(endMark) : performance.now();

    if (startTime === undefined) {
      // 开始标记未找到
      return 0;
    }

    const duration = endTime - startTime;
    this.measures.set(name, duration);

    if (performance.measure) {
      try {
        performance.measure(name, startMark, endMark);
      } catch (error) {
        // 性能测量失败
      }
    }

    return duration;
  }

  /**
   * 获取测量结果
   * @param {string} name - 测量名称
   * @returns {number} 时间（毫秒）
   */
  getMeasure(name) {
    return this.measures.get(name) || 0;
  }

  /**
   * 清理标记和测量
   */
  clear() {
    this.marks.clear();
    this.measures.clear();

    if (performance.clearMarks) {
      performance.clearMarks();
    }

    if (performance.clearMeasures) {
      performance.clearMeasures();
    }
  }

  /**
   * 获取页面性能信息
   * @returns {Object} 性能信息
   */
  getPagePerformance() {
    const navigation = performance.getEntriesByType("navigation")[0];

    if (!navigation) {
      return null;
    }

    return {
      dns: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcp: navigation.connectEnd - navigation.connectStart,
      request: navigation.responseEnd - navigation.requestStart,
      response: navigation.responseEnd - navigation.responseStart,
      dom:
        navigation.domContentLoadedEventEnd -
        navigation.domContentLoadedEventStart,
      load: navigation.loadEventEnd - navigation.loadEventStart,
      total: navigation.loadEventEnd - navigation.navigationStart,
    };
  }
}

// 创建全局实例
export const loadingManager = new LoadingManager();
export const notificationManager = new NotificationManager();
export const performanceMonitor = new PerformanceMonitor();

// 默认导出
export default {
  debounce,
  throttle,
  smoothScrollTo,
  copyToClipboard,
  downloadFile,
  formatFileSize,
  formatDateTime,
  formatRelativeTime,
  loadingManager,
  notificationManager,
  performanceMonitor,
  LoadingManager,
  NotificationManager,
  PerformanceMonitor,
};
