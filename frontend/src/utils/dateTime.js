/**
 * 日期时间工具函数
 * 为了保持向后兼容性，重新导出 dateUtils 中的函数
 */

import { formatTime } from './dateUtils.js';

/**
 * 格式化日期时间（别名函数，保持向后兼容）
 * @param {string|Date} date - 日期（通常是UTC时间）
 * @param {string} format - 格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的北京时间
 */
export const formatDateTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return formatTime(date, format);
};

// 默认导出
export default {
  formatDateTime
};
