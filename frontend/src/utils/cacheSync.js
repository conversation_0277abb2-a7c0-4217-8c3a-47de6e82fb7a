/**
 * 缓存同步工具
 * 用于检测数据库重置并清理相应的缓存
 */

import pdfCacheManager from './pdfCacheManager.js';

class CacheSync {
  constructor() {
    this.lastKnownContractCount = null;
    this.storageKey = 'app_cache_sync';
  }

  /**
   * 初始化缓存同步
   */
  async init() {
    try {
      // 从localStorage读取上次的状态
      const savedState = localStorage.getItem(this.storageKey);
      if (savedState) {
        const state = JSON.parse(savedState);
        this.lastKnownContractCount = state.contractCount;
      }

      // 检查是否需要清理缓存
      await this.checkAndCleanCache();
    } catch (error) {
      console.error('缓存同步初始化失败:', error);
    }
  }

  /**
   * 检查并清理缓存
   */
  async checkAndCleanCache() {
    try {
      // 获取当前合同数量（通过API）
      const baseURL = import.meta.env.VITE_API_BASE_URL || '/api';
      const response = await fetch(`${baseURL}/contracts/stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        console.warn('无法获取合同统计信息，跳过缓存检查');
        return;
      }

      const result = await response.json();
      const currentContractCount = result.data?.total || 0;

      // 如果这是第一次运行，记录当前状态
      if (this.lastKnownContractCount === null) {
        this.lastKnownContractCount = currentContractCount;
        this.saveState();
        return;
      }

      // 检查是否发生了数据库重置
      if (currentContractCount < this.lastKnownContractCount) {
        console.log('🔍 检测到数据库可能被重置，清理PDF缓存...');

        // 清空所有PDF缓存
        await pdfCacheManager.clearAllCache();

        // 清理浏览器缓存（通过重新加载）
        this.clearBrowserCache();

        console.log('✅ 缓存清理完成');
      }

      // 更新状态
      this.lastKnownContractCount = currentContractCount;
      this.saveState();

    } catch (error) {
      console.error('缓存检查失败:', error);
    }
  }

  /**
   * 清理浏览器缓存
   */
  clearBrowserCache() {
    try {
      // 清理Service Worker缓存（如果有）
      if ('serviceWorker' in navigator && 'caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            if (cacheName.includes('pdf') || cacheName.includes('files')) {
              caches.delete(cacheName);
            }
          });
        });
      }

      // 清理相关的localStorage项
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('pdf_cache') || key.includes('contract_preview'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

    } catch (error) {
      console.error('清理浏览器缓存失败:', error);
    }
  }

  /**
   * 保存状态到localStorage
   */
  saveState() {
    try {
      const state = {
        contractCount: this.lastKnownContractCount,
        lastCheck: Date.now()
      };
      localStorage.setItem(this.storageKey, JSON.stringify(state));
    } catch (error) {
      console.error('保存缓存同步状态失败:', error);
    }
  }

  /**
   * 手动触发缓存清理
   */
  async forceClearCache() {
    try {
      console.log('🧹 手动清理所有缓存...');

      // 清空PDF缓存
      await pdfCacheManager.clearAllCache();

      // 清理浏览器缓存
      this.clearBrowserCache();

      // 重置状态
      this.lastKnownContractCount = null;
      localStorage.removeItem(this.storageKey);

      console.log('✅ 手动缓存清理完成');

      // 刷新页面以确保清理生效
      window.location.reload();

    } catch (error) {
      console.error('手动清理缓存失败:', error);
    }
  }

  /**
   * 清理特定合同的缓存
   * @param {number} contractId - 合同ID
   */
  async clearContractCache(contractId) {
    try {
      console.log(`🧹 清理合同${contractId}的缓存...`);

      // 清理PDF缓存
      await pdfCacheManager.clearContractCache(contractId);

      console.log(`✅ 合同${contractId}缓存清理完成`);

    } catch (error) {
      console.error(`清理合同${contractId}缓存失败:`, error);
    }
  }
}

// 创建单例实例
const cacheSync = new CacheSync();

export default cacheSync;
