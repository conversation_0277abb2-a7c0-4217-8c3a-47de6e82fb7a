/**
 * Vue 自定义指令
 * 包含权限控制指令
 */

import { ROLE_PERMISSIONS } from "@/composables/usePermission";

// 从 localStorage 获取用户信息的辅助函数
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem("user");
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.warn("获取用户信息失败:", error);
    return null;
  }
};

// 检查权限的辅助函数
const checkPermission = (permissions) => {
  const user = getCurrentUser();
  if (!user || !user.role) {
    return false;
  }

  const userPermissions = ROLE_PERMISSIONS[user.role] || [];

  if (typeof permissions === "string") {
    return userPermissions.includes(permissions);
  }

  if (Array.isArray(permissions)) {
    return permissions.every((permission) =>
      userPermissions.includes(permission),
    );
  }

  return false;
};

// 检查角色的辅助函数
const checkRole = (roles) => {
  const user = getCurrentUser();
  if (!user || !user.role) {
    return false;
  }

  if (typeof roles === "string") {
    return user.role === roles;
  }

  if (Array.isArray(roles)) {
    return roles.includes(user.role);
  }

  return false;
};

/**
 * 权限指令
 * 用法：v-permission="'user:create'" 或 v-permission="['user:create', 'user:update']"
 */
export const permission = {
  mounted(el, binding) {
    const permissions = binding.value;

    if (!checkPermission(permissions)) {
      // 移除元素
      el.parentNode && el.parentNode.removeChild(el);
    }
  },
  updated(el, binding) {
    const permissions = binding.value;

    if (!checkPermission(permissions)) {
      // 隐藏元素
      el.style.display = "none";
    } else {
      // 显示元素
      el.style.display = "";
    }
  },
};

/**
 * 角色指令
 * 用法：v-role="'admin'" 或 v-role="['admin', 'reviewer']"
 */
export const role = {
  mounted(el, binding) {
    const roles = binding.value;

    if (!checkRole(roles)) {
      // 移除元素
      el.parentNode && el.parentNode.removeChild(el);
    }
  },
  updated(el, binding) {
    const roles = binding.value;

    if (!checkRole(roles)) {
      // 隐藏元素
      el.style.display = "none";
    } else {
      // 显示元素
      el.style.display = "";
    }
  },
};

/**
 * 权限禁用指令
 * 用法：v-permission-disabled="'user:delete'"
 * 没有权限时禁用元素而不是隐藏
 */
export const permissionDisabled = {
  mounted(el, binding) {
    const permissions = binding.value;

    if (!checkPermission(permissions)) {
      el.disabled = true;
      el.classList.add("is-disabled");
      el.title = "权限不足";
    }
  },
  updated(el, binding) {
    const permissions = binding.value;

    if (!checkPermission(permissions)) {
      el.disabled = true;
      el.classList.add("is-disabled");
      el.title = "权限不足";
    } else {
      el.disabled = false;
      el.classList.remove("is-disabled");
      el.title = "";
    }
  },
};

/**
 * 注册所有指令
 */
export function registerDirectives(app) {
  app.directive("permission", permission);
  app.directive("role", role);
  app.directive("permission-disabled", permissionDisabled);
}

export default {
  permission,
  role,
  permissionDisabled,
  registerDirectives,
};
