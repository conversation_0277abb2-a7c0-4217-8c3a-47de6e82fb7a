/**
 * 统一日期时间工具函数
 * 使用dayjs处理北京时间（UTC+8）
 */

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import relativeTime from "dayjs/plugin/relativeTime";
import customParseFormat from "dayjs/plugin/customParseFormat";

// 加载插件
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);
dayjs.extend(customParseFormat);

// 设置中文相对时间
dayjs.locale("zh-cn", {
  relativeTime: {
    future: "%s后",
    past: "%s前",
    s: "几秒",
    m: "1分钟",
    mm: "%d分钟",
    h: "1小时",
    hh: "%d小时",
    d: "1天",
    dd: "%d天",
    M: "1个月",
    MM: "%d个月",
    y: "1年",
    yy: "%d年",
  },
});

// 北京时区常量
const BEIJING_TIMEZONE = "Asia/Shanghai";
const DEFAULT_FORMAT = "YYYY-MM-DD HH:mm:ss";

/**
 * 格式化时间为北京时间显示
 * @param {string|Date} date - 日期（通常是UTC时间）
 * @param {string} format - 格式
 * @returns {string} 格式化后的北京时间
 */
export const formatTime = (date, format = DEFAULT_FORMAT) => {
  if (!date) return "";

  try {
    // 将UTC时间转换为北京时间显示
    const dayjsObj = dayjs(date).utc();
    if (!dayjsObj.isValid()) return "";

    return dayjsObj.tz(BEIJING_TIMEZONE).format(format);
  } catch (error) {
    console.error("时间格式化失败:", error);
    return "";
  }
};

/**
 * 格式化相对时间（使用北京时间）
 * @param {string|Date} date - 日期（UTC时间）
 * @returns {string} 相对时间描述
 */
export const formatRelativeTime = (date) => {
  if (!date) return "";

  try {
    const inputTime = dayjs(date).utc().tz(BEIJING_TIMEZONE);
    const now = dayjs().tz(BEIJING_TIMEZONE);

    if (!inputTime.isValid()) return "";

    return inputTime.from(now);
  } catch (error) {
    console.error("相对时间计算失败:", error);
    return "";
  }
};

/**
 * 格式化日期范围
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {string} 日期范围字符串
 */
export const formatDateRange = (startDate, endDate) => {
  const start = formatTime(startDate, "YYYY-MM-DD");
  const end = formatTime(endDate, "YYYY-MM-DD");

  if (!start && !end) return "";
  if (!start) return `至 ${end}`;
  if (!end) return `${start} 至今`;

  return `${start} 至 ${end}`;
};

/**
 * 获取今天的日期字符串（北京时间）
 * @returns {string} YYYY-MM-DD格式的今天日期
 */
export const getToday = () => {
  return dayjs().tz(BEIJING_TIMEZONE).format("YYYY-MM-DD");
};

/**
 * 获取昨天的日期字符串（北京时间）
 * @returns {string} YYYY-MM-DD格式的昨天日期
 */
export const getYesterday = () => {
  return dayjs().tz(BEIJING_TIMEZONE).subtract(1, "day").format("YYYY-MM-DD");
};

/**
 * 获取本周开始日期（北京时间）
 * @returns {string} YYYY-MM-DD格式的本周开始日期
 */
export const getWeekStart = () => {
  return dayjs().tz(BEIJING_TIMEZONE).startOf("week").format("YYYY-MM-DD");
};

/**
 * 获取本月开始日期（北京时间）
 * @returns {string} YYYY-MM-DD格式的本月开始日期
 */
export const getMonthStart = () => {
  return dayjs().tz(BEIJING_TIMEZONE).startOf("month").format("YYYY-MM-DD");
};

/**
 * 判断是否为今天（北京时间）
 * @param {string|Date} date - 日期（UTC时间）
 * @returns {boolean} 是否为今天
 */
export const isToday = (date) => {
  if (!date) return false;

  try {
    const inputDate = dayjs(date).utc().tz(BEIJING_TIMEZONE);
    const today = dayjs().tz(BEIJING_TIMEZONE);

    return inputDate.format("YYYY-MM-DD") === today.format("YYYY-MM-DD");
  } catch (error) {
    console.error("判断是否为今天失败:", error);
    return false;
  }
};

/**
 * 判断是否为昨天（北京时间）
 * @param {string|Date} date - 日期（UTC时间）
 * @returns {boolean} 是否为昨天
 */
export const isYesterday = (date) => {
  if (!date) return false;

  try {
    const inputDate = dayjs(date).utc().tz(BEIJING_TIMEZONE);
    const yesterday = dayjs().tz(BEIJING_TIMEZONE).subtract(1, "day");

    return inputDate.format("YYYY-MM-DD") === yesterday.format("YYYY-MM-DD");
  } catch (error) {
    console.error("判断是否为昨天失败:", error);
    return false;
  }
};

/**
 * 计算两个日期之间的天数差（北京时间）
 * @param {string|Date} date1 - 日期1（UTC时间）
 * @param {string|Date} date2 - 日期2（UTC时间）
 * @returns {number} 天数差（绝对值）
 */
export const daysBetween = (date1, date2) => {
  if (!date1 || !date2) return 0;

  try {
    const d1 = dayjs(date1).utc().tz(BEIJING_TIMEZONE);
    const d2 = dayjs(date2).utc().tz(BEIJING_TIMEZONE);

    if (!d1.isValid() || !d2.isValid()) return 0;

    return Math.abs(d1.diff(d2, "day"));
  } catch (error) {
    console.error("计算日期差失败:", error);
    return 0;
  }
};

/**
 * 获取当前北京时间
 * @param {string} format - 时间格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化的北京时间字符串
 */
export const now = (format = DEFAULT_FORMAT) => {
  return dayjs().tz(BEIJING_TIMEZONE).format(format);
};

/**
 * 验证时间字符串是否有效
 * @param {string} dateString - 时间字符串
 * @returns {boolean} 是否有效
 */
export const isValid = (dateString) => {
  if (!dateString) return false;
  return dayjs(dateString).isValid();
};

/**
 * 将北京时间转换为UTC时间（用于发送到后端）
 * @param {string|Date} date - 北京时间
 * @returns {string} ISO格式的UTC时间字符串
 */
export const toUTC = (date) => {
  if (!date) return null;

  try {
    // 将输入时间视为北京时间，转换为UTC
    return dayjs.tz(date, BEIJING_TIMEZONE).utc().toISOString();
  } catch (error) {
    console.error("时间转换为UTC失败:", error);
    return null;
  }
};

export default {
  formatTime,
  formatRelativeTime,
  formatDateRange,
  getToday,
  getYesterday,
  getWeekStart,
  getMonthStart,
  isToday,
  isYesterday,
  daysBetween,
  now,
  isValid,
  toUTC,
};
