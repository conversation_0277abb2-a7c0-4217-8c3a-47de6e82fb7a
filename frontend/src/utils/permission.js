/**
 * 权限工具函数
 * 提供权限检查和菜单过滤等工具方法
 */

import { ROLE_PERMISSIONS, PERMISSIONS } from "@/composables/usePermission";

/**
 * 检查用户是否具有指定权限
 * @param {string} userRole - 用户角色
 * @param {string|Array} permissions - 权限或权限数组
 * @returns {boolean} 是否具有权限
 */
export function checkPermission(userRole, permissions) {
  if (!userRole) {
    return false;
  }

  const userPermissions = ROLE_PERMISSIONS[userRole] || [];

  if (typeof permissions === "string") {
    return userPermissions.includes(permissions);
  }

  if (Array.isArray(permissions)) {
    return permissions.every((permission) =>
      userPermissions.includes(permission),
    );
  }

  return false;
}

/**
 * 检查用户是否具有任一权限
 * @param {string} userRole - 用户角色
 * @param {Array} permissions - 权限数组
 * @returns {boolean} 是否具有任一权限
 */
export function checkAnyPermission(userRole, permissions) {
  if (!userRole || !Array.isArray(permissions)) {
    return false;
  }

  const userPermissions = ROLE_PERMISSIONS[userRole] || [];
  return permissions.some((permission) => userPermissions.includes(permission));
}

/**
 * 过滤菜单项
 * @param {Array} menus - 菜单数组
 * @param {string} userRole - 用户角色
 * @returns {Array} 过滤后的菜单
 */
export function filterMenus(menus, userRole) {
  if (!userRole || !Array.isArray(menus)) {
    return [];
  }

  return menus
    .filter((menu) => {
      // 如果菜单项没有权限要求，则显示
      if (!menu.permission) {
        return true;
      }

      // 检查权限
      return checkPermission(userRole, menu.permission);
    })
    .map((menu) => {
      // 递归过滤子菜单
      if (menu.children && Array.isArray(menu.children)) {
        return {
          ...menu,
          children: filterMenus(menu.children, userRole),
        };
      }
      return menu;
    });
}

/**
 * 获取用户可访问的路由
 * @param {Array} routes - 路由数组
 * @param {string} userRole - 用户角色
 * @returns {Array} 可访问的路由
 */
export function getAccessibleRoutes(routes, userRole) {
  if (!userRole || !Array.isArray(routes)) {
    return [];
  }

  return routes.filter((route) => {
    // 如果路由没有角色要求，则可访问
    if (!route.meta || !route.meta.roles) {
      return true;
    }

    // 检查角色
    const requiredRoles = Array.isArray(route.meta.roles)
      ? route.meta.roles
      : [route.meta.roles];
    return requiredRoles.includes(userRole);
  });
}

/**
 * 检查合同操作权限
 * @param {Object} contract - 合同对象
 * @param {Object} user - 用户对象
 * @param {string} action - 操作类型 ('view', 'modify', 'review')
 * @returns {boolean} 是否有权限
 */
export function checkContractPermission(contract, user, action) {
  if (!contract || !user) {
    return false;
  }

  switch (action) {
    case "view":
      return canViewContract(contract, user);
    case "modify":
      return canModifyContract(contract, user);
    case "review":
      return canReviewContract(contract, user);
    default:
      return false;
  }
}

/**
 * 检查是否可以查看合同
 * @param {Object} contract - 合同对象
 * @param {Object} user - 用户对象
 * @returns {boolean} 是否可以查看
 */
export function canViewContract(contract, user) {
  // 管理员可以查看所有合同
  if (user.role === "admin") {
    return true;
  }

  // 提交人可以查看自己的合同
  if (contract.submitter_id === user.id) {
    return true;
  }

  // 审核员可以查看分配给自己的合同
  if (user.role === "reviewer" && contract.reviewer_id === user.id) {
    return true;
  }

  return false;
}

/**
 * 检查是否可以修改合同
 * @param {Object} contract - 合同对象
 * @param {Object} user - 用户对象
 * @returns {boolean} 是否可以修改
 */
export function canModifyContract(contract, user) {
  // 管理员不能修改合同内容
  if (user.role === "admin") {
    return false;
  }

  // 只有提交人可以修改
  if (contract.submitter_id !== user.id) {
    return false;
  }

  // 员工可以修改 pending 或 rejected 状态的合同
  return ["pending", "rejected"].includes(contract.status);
}

/**
 * 检查是否可以审核合同
 * @param {Object} contract - 合同对象
 * @param {Object} user - 用户对象
 * @returns {boolean} 是否可以审核
 */
export function canReviewContract(contract, user) {
  // 只有待审核状态的合同可以审核（支持县局和市局审核）
  const reviewableStatuses = ["pending", "pending_city_review"];
  if (!reviewableStatuses.includes(contract.status)) {
    return false;
  }

  // 不能审核自己提交的合同
  if (contract.submitter_id === user.id) {
    return false;
  }

  // 管理员可以审核任何合同
  if (user.role === "admin") {
    return true;
  }

  // 审核员只能审核分配给自己的合同
  if (
    (user.role === "reviewer" ||
      user.role === "county_reviewer" ||
      user.role === "city_reviewer") &&
    contract.reviewer_id === user.id
  ) {
    // 额外检查：市局审核员只能审核待市局审核的合同
    if (
      user.role === "city_reviewer" &&
      contract.status !== "pending_city_review"
    ) {
      return false;
    }
    // 县局审核员只能审核待审核的合同
    if (
      (user.role === "county_reviewer" || user.role === "reviewer") &&
      contract.status !== "pending"
    ) {
      return false;
    }
    return true;
  }

  return false;
}

/**
 * 获取操作按钮配置
 * @param {Object} contract - 合同对象
 * @param {Object} user - 用户对象
 * @returns {Object} 按钮配置
 */
export function getContractActions(contract, user) {
  const actions = {
    view: false,
    modify: false,
    review: false,
    delete: false,
  };

  if (!contract || !user) {
    return actions;
  }

  actions.view = canViewContract(contract, user);
  actions.modify = canModifyContract(contract, user);
  actions.review = canReviewContract(contract, user);

  // 只有管理员可以删除合同
  actions.delete = user.role === "admin";

  return actions;
}

/**
 * 格式化权限名称
 * @param {string} permission - 权限标识
 * @returns {string} 权限名称
 */
export function formatPermissionName(permission) {
  const permissionNames = {
    [PERMISSIONS.CONTRACT_CREATE]: "创建合同",
    [PERMISSIONS.CONTRACT_READ]: "查看合同",
    [PERMISSIONS.CONTRACT_UPDATE]: "修改合同",
    [PERMISSIONS.CONTRACT_DELETE]: "删除合同",
    [PERMISSIONS.CONTRACT_REVIEW]: "审核合同",
    [PERMISSIONS.USER_CREATE]: "创建用户",
    [PERMISSIONS.USER_READ]: "查看用户",
    [PERMISSIONS.USER_UPDATE]: "修改用户",
    [PERMISSIONS.USER_DELETE]: "删除用户",
    [PERMISSIONS.USER_MANAGE]: "用户管理",
    [PERMISSIONS.SYSTEM_STATS]: "系统统计",
    [PERMISSIONS.SYSTEM_MANAGE]: "系统管理",
  };

  return permissionNames[permission] || permission;
}

/**
 * 格式化角色名称
 * @param {string} role - 角色标识
 * @returns {string} 角色名称
 */
export function formatRoleName(role) {
  const roleNames = {
    admin: "管理员",
    reviewer: "审核员",
    employee: "员工",
  };

  return roleNames[role] || role;
}

/**
 * 获取角色权限列表
 * @param {string} role - 角色标识
 * @returns {Array} 权限列表
 */
export function getRolePermissions(role) {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * 检查权限依赖
 * @param {string} permission - 权限标识
 * @param {Array} userPermissions - 用户权限列表
 * @returns {boolean} 是否满足依赖
 */
export function checkPermissionDependencies(permission, userPermissions) {
  // 权限依赖关系配置
  const dependencies = {
    [PERMISSIONS.CONTRACT_UPDATE]: [PERMISSIONS.CONTRACT_READ],
    [PERMISSIONS.CONTRACT_DELETE]: [PERMISSIONS.CONTRACT_READ],
    [PERMISSIONS.USER_UPDATE]: [PERMISSIONS.USER_READ],
    [PERMISSIONS.USER_DELETE]: [PERMISSIONS.USER_READ],
  };

  const deps = dependencies[permission];
  if (!deps) {
    return true;
  }

  return deps.every((dep) => userPermissions.includes(dep));
}

export default {
  checkPermission,
  checkAnyPermission,
  filterMenus,
  getAccessibleRoutes,
  checkContractPermission,
  canViewContract,
  canModifyContract,
  canReviewContract,
  getContractActions,
  formatPermissionName,
  formatRoleName,
  getRolePermissions,
  checkPermissionDependencies,
};
