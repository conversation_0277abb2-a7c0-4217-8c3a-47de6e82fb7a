/**
 * PDF文件缓存管理器
 * 使用IndexedDB存储PDF文件，支持离线访问和缓存管理
 */

class PDFCacheManager {
  constructor() {
    this.dbName = "PDFCacheDB";
    this.dbVersion = 1;
    this.storeName = "pdfFiles";
    this.maxCacheSize = 100 * 1024 * 1024; // 100MB缓存限制
    this.maxFiles = 50; // 最多缓存50个文件
    this.db = null;
  }

  /**
   * 初始化IndexedDB
   */
  async init() {
    if (this.db) return this.db;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error("PDF缓存数据库打开失败:", request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // 创建对象存储
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, {
            keyPath: "url",
          });
          store.createIndex("lastAccessed", "lastAccessed", { unique: false });
          store.createIndex("size", "size", { unique: false });
        }
      };
    });
  }

  /**
   * 生成唯一的缓存键
   * @param {string} url - PDF文件URL
   * @param {Object} metadata - 文件元数据
   * @returns {string} 唯一的缓存键
   */
  generateCacheKey(url, metadata = {}) {
    // 提取合同ID
    const contractIdMatch = url.match(/\/files\/(\d+)\/preview/);
    const contractId = contractIdMatch ? contractIdMatch[1] : null;

    // 使用合同ID、文件路径和ETag生成唯一键
    const keyParts = [
      url,
      contractId ? `contract-${contractId}` : '',
      metadata.etag || '',
      metadata.filePath || '',
    ].filter(Boolean);

    return keyParts.join('|');
  }

  /**
   * 缓存PDF文件
   * @param {string} url - PDF文件URL
   * @param {ArrayBuffer} data - PDF文件数据
   * @param {Object} metadata - 文件元数据
   */
  async cachePDF(url, data, metadata = {}) {
    try {
      await this.init();

      // 生成唯一的缓存键
      const cacheKey = this.generateCacheKey(url, metadata);

      const cacheEntry = {
        url: cacheKey, // 使用唯一的缓存键
        originalUrl: url, // 保存原始URL
        data: data,
        size: data.byteLength,
        cachedAt: Date.now(),
        lastAccessed: Date.now(),
        metadata: {
          filename: metadata.filename || "document.pdf",
          contractId: metadata.contractId,
          contentType: metadata.contentType || "application/pdf",
          etag: metadata.etag,
          filePath: metadata.filePath,
          ...metadata,
        },
      };

      // 检查缓存空间
      await this.ensureCacheSpace(cacheEntry.size);

      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);

      await new Promise((resolve, reject) => {
        const request = store.put(cacheEntry);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error("PDF缓存失败:", error);
      return false;
    }
  }

  /**
   * 获取缓存的PDF文件
   * @param {string} url - PDF文件URL
   * @param {Object} metadata - 文件元数据（用于生成缓存键）
   */
  async getCachedPDF(url, metadata = {}) {
    try {
      await this.init();

      // 生成唯一的缓存键
      const cacheKey = this.generateCacheKey(url, metadata);

      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);

      const cacheEntry = await new Promise((resolve, reject) => {
        const request = store.get(cacheKey);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      if (cacheEntry) {
        // 验证缓存是否仍然有效（检查ETag）
        if (metadata.etag && cacheEntry.metadata.etag &&
          metadata.etag !== cacheEntry.metadata.etag) {
          // ETag不匹配，缓存已过期
          await this.removeCachedPDF(cacheKey);
          return null;
        }

        // 更新最后访问时间
        cacheEntry.lastAccessed = Date.now();
        store.put(cacheEntry);

        return {
          data: cacheEntry.data,
          metadata: cacheEntry.metadata,
          cachedAt: cacheEntry.cachedAt,
        };
      }

      return null;
    } catch (error) {
      console.error("获取缓存PDF失败:", error);
      return null;
    }
  }

  /**
   * 删除缓存的PDF文件
   * @param {string} cacheKey - 缓存键
   */
  async removeCachedPDF(cacheKey) {
    try {
      await this.init();

      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);

      return new Promise((resolve, reject) => {
        const request = store.delete(cacheKey);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error("删除缓存PDF失败:", error);
    }
  }

  /**
   * 检查PDF是否已缓存
   * @param {string} url - PDF文件URL
   * @param {Object} metadata - 文件元数据（用于生成缓存键）
   */
  async isCached(url, metadata = {}) {
    try {
      await this.init();

      // 生成唯一的缓存键
      const cacheKey = this.generateCacheKey(url, metadata);

      const transaction = this.db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);

      return new Promise((resolve, reject) => {
        const request = store.get(cacheKey);
        request.onsuccess = () => {
          const result = request.result;
          if (result) {
            // 检查ETag是否匹配
            if (metadata.etag && result.metadata.etag &&
              metadata.etag !== result.metadata.etag) {
              resolve(false); // ETag不匹配，视为未缓存
            } else {
              resolve(true);
            }
          } else {
            resolve(false);
          }
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error("检查PDF缓存状态失败:", error);
      return false;
    }
  }

  /**
   * 确保缓存空间足够
   * @param {number} requiredSize - 需要的空间大小
   */
  async ensureCacheSpace(requiredSize) {
    const stats = await this.getCacheStats();

    // 检查文件数量限制
    if (stats.fileCount >= this.maxFiles) {
      await this.cleanupOldFiles(1);
    }

    // 检查空间限制
    if (stats.totalSize + requiredSize > this.maxCacheSize) {
      const spaceToFree = stats.totalSize + requiredSize - this.maxCacheSize;
      await this.cleanupBySize(spaceToFree);
    }
  }

  /**
   * 清理旧文件（LRU策略）
   * @param {number} count - 要清理的文件数量
   */
  async cleanupOldFiles(count) {
    try {
      await this.init();

      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);
      const index = store.index("lastAccessed");

      const files = [];
      const request = index.openCursor();

      await new Promise((resolve, reject) => {
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            files.push({
              url: cursor.value.url,
              lastAccessed: cursor.value.lastAccessed,
              size: cursor.value.size,
            });
            cursor.continue();
          } else {
            resolve();
          }
        };
        request.onerror = () => reject(request.error);
      });

      // 按最后访问时间排序，删除最旧的文件
      files.sort((a, b) => a.lastAccessed - b.lastAccessed);
      const filesToDelete = files.slice(0, count);

      for (const file of filesToDelete) {
        await new Promise((resolve, reject) => {
          const deleteRequest = store.delete(file.url);
          deleteRequest.onsuccess = () => resolve();
          deleteRequest.onerror = () => reject(deleteRequest.error);
        });
      }
    } catch (error) {
      console.error("清理旧文件失败:", error);
    }
  }

  /**
   * 按大小清理缓存
   * @param {number} spaceToFree - 需要释放的空间大小
   */
  async cleanupBySize(spaceToFree) {
    let freedSpace = 0;
    let filesToDelete = 1;

    while (freedSpace < spaceToFree && filesToDelete <= this.maxFiles) {
      await this.cleanupOldFiles(filesToDelete);
      const stats = await this.getCacheStats();

      if (stats.totalSize + spaceToFree <= this.maxCacheSize) {
        break;
      }

      filesToDelete++;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    try {
      await this.init();

      const transaction = this.db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);

      let totalSize = 0;
      let fileCount = 0;
      const files = [];

      await new Promise((resolve, reject) => {
        const request = store.openCursor();
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            totalSize += cursor.value.size;
            fileCount++;
            files.push({
              url: cursor.value.url,
              size: cursor.value.size,
              filename: cursor.value.metadata.filename,
              cachedAt: cursor.value.cachedAt,
              lastAccessed: cursor.value.lastAccessed,
            });
            cursor.continue();
          } else {
            resolve();
          }
        };
        request.onerror = () => reject(request.error);
      });

      return {
        totalSize,
        fileCount,
        maxSize: this.maxCacheSize,
        maxFiles: this.maxFiles,
        usagePercent: Math.round((totalSize / this.maxCacheSize) * 100),
        files,
      };
    } catch (error) {
      console.error("获取缓存统计失败:", error);
      return {
        totalSize: 0,
        fileCount: 0,
        maxSize: this.maxCacheSize,
        maxFiles: this.maxFiles,
        usagePercent: 0,
        files: [],
      };
    }
  }

  /**
   * 清空所有缓存
   */
  async clearAll() {
    try {
      await this.init();

      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);

      await new Promise((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error("清空缓存失败:", error);
      return false;
    }
  }

  /**
   * 删除特定URL的缓存
   * @param {string} url - 要删除的PDF文件URL
   */
  async deleteCached(url) {
    try {
      await this.init();

      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);

      await new Promise((resolve, reject) => {
        const request = store.delete(url);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error("删除PDF缓存失败:", error);
      return false;
    }
  }

  /**
   * 清理特定合同的缓存
   * @param {number} contractId - 合同ID
   */
  async clearContractCache(contractId) {
    try {
      await this.init();

      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);

      let deletedCount = 0;

      return new Promise((resolve, reject) => {
        const request = store.openCursor();

        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            const entry = cursor.value;
            // 检查是否是指定合同的缓存
            if (entry.metadata && entry.metadata.contractId == contractId) {
              cursor.delete();
              deletedCount++;
            }
            cursor.continue();
          } else {
            console.log(`🧹 清理了合同${contractId}的 ${deletedCount} 个PDF缓存`);
            resolve(deletedCount);
          }
        };

        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error(`清理合同${contractId}缓存失败:`, error);
      return 0;
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   */
  formatSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
}

// 创建单例实例
const pdfCacheManager = new PDFCacheManager();

export default pdfCacheManager;
