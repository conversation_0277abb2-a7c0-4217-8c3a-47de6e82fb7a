/**
 * Tailwind CSS 基础样式
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置 */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 选中文本样式 */
  ::selection {
    background-color: #409eff;
    color: white;
  }

  ::-moz-selection {
    background-color: #409eff;
    color: white;
  }
}

/* 组件样式 */
@layer components {
  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .card-header {
    @apply border-b border-gray-200 pb-4 mb-4;
  }

  .card-title {
    @apply text-lg font-semibold text-gray-900;
  }

  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  /* 表单样式 */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }

  .form-error {
    @apply mt-1 text-sm text-red-600;
  }

  /* 状态标签样式 */
  .status-tag {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-pending {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-reviewing {
    @apply bg-blue-100 text-blue-800;
  }

  .status-approved {
    @apply bg-green-100 text-green-800;
  }

  .status-rejected {
    @apply bg-red-100 text-red-800;
  }

  /* 布局样式 */
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .page-header {
    @apply bg-white shadow-sm border-b border-gray-200 px-6 py-4;
  }

  .page-title {
    @apply text-2xl font-bold text-gray-900;
  }

  .page-content {
    @apply p-6;
  }

  /* 表格样式 */
  .table-container {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  }

  .table-header {
    @apply bg-gray-50 px-6 py-4 border-b border-gray-200;
  }

  .table-title {
    @apply text-lg font-semibold text-gray-900;
  }

  /* 侧边栏样式 */
  .sidebar {
    @apply bg-white shadow-sm border-r border-gray-200;
  }

  .sidebar-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .sidebar-menu {
    @apply py-4;
  }

  .sidebar-menu-item {
    @apply flex items-center px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200;
  }

  .sidebar-menu-item.active {
    @apply bg-blue-50 text-blue-700 border-r-2 border-blue-600;
  }

  /* Tab 样式 */
  .tab-container {
    @apply bg-white border-b border-gray-200;
  }

  .tab-list {
    @apply flex space-x-8 px-6;
  }

  .tab-item {
    @apply py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-colors duration-200;
  }

  .tab-item.active {
    @apply border-blue-500 text-blue-600;
  }
}

/* 工具样式 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis {
    @apply truncate;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 动画 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  /* 响应式隐藏 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* 玻璃效果 */
  .glass {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.8);
  }

  .glass-dark {
    backdrop-filter: blur(10px);
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
