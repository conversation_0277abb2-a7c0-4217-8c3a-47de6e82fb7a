<template>
  <div class="forbidden-container">
    <div class="forbidden-content">
      <!-- 403 图标 -->
      <div class="error-icon">
        <el-icon :size="120" color="#f56c6c">
          <Lock />
        </el-icon>
      </div>

      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-code">403</h1>
        <h2 class="error-title">访问被拒绝</h2>
        <p class="error-description">
          抱歉，您没有权限访问此页面。请联系管理员获取相应权限。
        </p>
      </div>

      <!-- 用户信息 -->
      <div v-if="userStore.isAuthenticated" class="user-info">
        <el-card class="info-card">
          <template #header>
            <span>当前用户信息</span>
          </template>
          <div class="user-details">
            <p><strong>用户名：</strong>{{ userStore.userName }}</p>
            <p>
              <strong>角色：</strong
              >{{ getRoleDisplayName(userStore.userRole) }}
            </p>
            <p>
              <strong>权限：</strong>{{ userPermissions.join(", ") || "无" }}
            </p>
          </div>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" :icon="ArrowLeft" @click="goBack">
          返回上页
        </el-button>
        <el-button :icon="House" @click="goHome"> 回到首页 </el-button>
        <el-button :icon="SwitchButton" type="info" @click="logout">
          重新登录
        </el-button>
      </div>

      <!-- 帮助信息 -->
      <div class="help-info">
        <el-alert title="需要帮助？" type="info" :closable="false" show-icon>
          <template #default>
            <p>如果您认为这是一个错误，请：</p>
            <ul>
              <li>检查您的用户角色是否正确</li>
              <li>联系系统管理员申请相应权限</li>
              <li>确认您访问的页面地址是否正确</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Lock, ArrowLeft, House, SwitchButton } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/user";
import { usePermission } from "@/composables/usePermission";

const router = useRouter();
const userStore = useUserStore();
const { userPermissions } = usePermission();

// 角色显示名称映射
const roleDisplayNames = {
  employee: "员工",
  reviewer: "审核员",
  county_reviewer: "县级审核员",
  city_reviewer: "市级审核员",
  admin: "管理员",
};

// 获取角色显示名称
const getRoleDisplayName = (role) => {
  return roleDisplayNames[role] || role || "未知";
};

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    goHome();
  }
};

// 回到首页
const goHome = () => {
  router.push("/dashboard");
};

// 退出登录
const logout = async () => {
  try {
    await userStore.logout();
    ElMessage.success("已退出登录");
    router.push("/login");
  } catch (error) {
    console.error("退出登录失败:", error);
    ElMessage.error("退出登录失败");
  }
};
</script>

<style scoped>
.forbidden-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.forbidden-content {
  max-width: 600px;
  width: 100%;
  text-align: center;
}

.error-icon {
  margin-bottom: 30px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.error-info {
  margin-bottom: 30px;
  color: white;
}

.error-code {
  font-size: 72px;
  font-weight: bold;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 15px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.error-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

.user-info {
  margin-bottom: 30px;
}

.info-card {
  text-align: left;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.user-details p {
  margin: 8px 0;
  font-size: 14px;
}

.action-buttons {
  margin-bottom: 30px;
}

.action-buttons .el-button {
  margin: 0 8px 8px 0;
}

.help-info {
  text-align: left;
}

.help-info ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.help-info li {
  margin: 5px 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forbidden-container {
    padding: 10px;
  }

  .error-code {
    font-size: 48px;
  }

  .error-title {
    font-size: 24px;
  }

  .action-buttons .el-button {
    display: block;
    width: 100%;
    margin: 0 0 10px 0;
  }
}
</style>
