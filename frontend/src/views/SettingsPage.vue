<!--
  系统设置页面
  提供系统配置和个人设置功能
-->
<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-setting"></i>
          系统设置
        </h1>
        <p class="page-description">管理系统配置和个人偏好设置</p>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <el-row :gutter="24">
        <!-- 左侧菜单 -->
        <el-col :span="6">
          <el-card class="settings-menu">
            <el-menu
              :default-active="activeTab"
              class="settings-nav"
              @select="handleMenuSelect"
            >
              <el-menu-item index="profile">
                <i class="el-icon-user"></i>
                <span>个人信息</span>
              </el-menu-item>
              <el-menu-item index="security">
                <i class="el-icon-lock"></i>
                <span>安全设置</span>
              </el-menu-item>
              <el-menu-item v-if="isAdmin" index="notification">
                <i class="el-icon-bell"></i>
                <span>通知设置</span>
              </el-menu-item>
              <el-menu-item v-if="isAdmin" index="system">
                <i class="el-icon-setting"></i>
                <span>系统配置</span>
              </el-menu-item>
            </el-menu>
          </el-card>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :span="18">
          <el-card class="settings-panel">
            <!-- 个人信息设置 -->
            <div v-if="activeTab === 'profile'" class="setting-section">
              <h3 class="section-title">个人信息</h3>
              <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
                class="profile-form"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input
                    v-model="profileForm.username"
                    disabled
                    placeholder="用户名不可修改"
                  />
                </el-form-item>
                <el-form-item label="角色">
                  <el-tag :type="getRoleTagType(userRole)">
                    {{ getRoleDisplayName() }}
                  </el-tag>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input
                    v-model="profileForm.email"
                    placeholder="请输入邮箱地址"
                  />
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                  <el-input
                    v-model="profileForm.phone"
                    placeholder="请输入手机号码"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="loading"
                    @click="updateProfile"
                  >
                    保存修改
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 安全设置 -->
            <div v-if="activeTab === 'security'" class="setting-section">
              <h3 class="section-title">安全设置</h3>
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
                class="password-form"
              >
                <el-form-item label="当前密码" prop="currentPassword">
                  <el-input
                    v-model="passwordForm.currentPassword"
                    type="password"
                    placeholder="请输入当前密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="loading"
                    @click="changePassword"
                  >
                    修改密码
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 通知设置 -->
            <div
              v-if="activeTab === 'notification' && isAdmin"
              class="setting-section"
            >
              <h3 class="section-title">通知设置</h3>
              <el-form label-width="120px">
                <el-form-item label="邮件通知">
                  <el-switch
                    v-model="notificationSettings.email"
                    @change="updateNotificationSettings"
                  />
                  <span class="setting-desc">接收系统邮件通知</span>
                </el-form-item>
                <el-form-item label="短信通知">
                  <el-switch
                    v-model="notificationSettings.sms"
                    @change="updateNotificationSettings"
                  />
                  <span class="setting-desc">接收重要事件短信通知</span>
                </el-form-item>
                <el-form-item label="浏览器通知">
                  <el-switch
                    v-model="notificationSettings.browser"
                    @change="updateNotificationSettings"
                  />
                  <span class="setting-desc">显示浏览器桌面通知</span>
                </el-form-item>
              </el-form>
            </div>

            <!-- 系统配置 -->
            <div
              v-if="activeTab === 'system' && isAdmin"
              class="setting-section"
            >
              <h3 class="section-title">系统配置</h3>
              <el-form label-width="120px">
                <el-form-item label="系统名称">
                  <el-input
                    v-model="systemSettings.name"
                    placeholder="合同审核系统"
                  />
                </el-form-item>
                <el-form-item label="会话超时">
                  <el-select v-model="systemSettings.sessionTimeout">
                    <el-option label="30分钟" value="30" />
                    <el-option label="1小时" value="60" />
                    <el-option label="2小时" value="120" />
                    <el-option label="4小时" value="240" />
                  </el-select>
                  <span class="setting-desc">用户无操作自动退出时间</span>
                </el-form-item>
                <el-form-item label="文件上传限制">
                  <el-input-number
                    v-model="systemSettings.maxFileSize"
                    :min="1"
                    :max="100"
                    controls-position="right"
                  />
                  <span class="setting-desc">MB，单个文件最大上传大小</span>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="loading"
                    @click="updateSystemSettings"
                  >
                    保存配置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useAuth } from "@/composables/useAuth";

// 使用认证状态
const {
  user,
  userRole,
  isAdmin,
  changePassword: authChangePassword,
  getRoleDisplayName,
} = useAuth();

// 响应式数据
const loading = ref(false);
const activeTab = ref("profile");

// 个人信息表单
const profileForm = reactive({
  username: "",
  email: "",
  phone: "",
});

// 密码修改表单
const passwordForm = reactive({
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
});

// 通知设置
const notificationSettings = reactive({
  email: true,
  sms: false,
  browser: true,
});

// 系统设置
const systemSettings = reactive({
  name: "合同审核系统",
  sessionTimeout: "60",
  maxFileSize: 10,
});

// 表单引用
const profileFormRef = ref();
const passwordFormRef = ref();

// 表单验证规则
const profileRules = {
  email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

const passwordRules = {
  currentPassword: [
    { required: true, message: "请输入当前密码", trigger: "blur" },
  ],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, message: "请确认新密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

// 计算属性
const getRoleTagType = (role) => {
  const typeMap = {
    admin: "danger",
    reviewer: "warning",
    employee: "info",
  };
  return typeMap[role] || "info";
};

// 方法
const handleMenuSelect = (key) => {
  activeTab.value = key;
};

const updateProfile = async () => {
  try {
    await profileFormRef.value.validate();
    loading.value = true;

    // 这里应该调用API更新个人信息
    // await userAPI.updateProfile(profileForm)

    ElMessage.success("个人信息更新成功");
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("更新个人信息失败:", error);
    }
  } finally {
    loading.value = false;
  }
};

const changePassword = async () => {
  try {
    await passwordFormRef.value.validate();
    loading.value = true;

    const success = await authChangePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword,
    });

    if (success) {
      // 清空表单
      Object.assign(passwordForm, {
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("修改密码失败:", error);
    }
  } finally {
    loading.value = false;
  }
};

const updateNotificationSettings = () => {
  // 这里应该调用API更新通知设置
  ElMessage.success("通知设置已更新");
};

const updateSystemSettings = () => {
  // 这里应该调用API更新系统设置
  ElMessage.success("系统配置已更新");
};

// 初始化
onMounted(() => {
  if (user.value) {
    profileForm.username = user.value.username;
    profileForm.email = user.value.email || "";
    profileForm.phone = user.value.phone || "";
  }
});
</script>

<style scoped>
.settings-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-title i {
  margin-right: 8px;
  color: #409eff;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.settings-content {
  margin-top: 20px;
}

.settings-menu {
  height: fit-content;
}

.settings-nav {
  border: none;
}

.settings-nav .el-menu-item {
  height: 48px;
  line-height: 48px;
}

.settings-panel {
  min-height: 500px;
}

.setting-section {
  padding: 20px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.profile-form,
.password-form {
  max-width: 400px;
}

.setting-desc {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.el-form-item {
  margin-bottom: 20px;
}
</style>
