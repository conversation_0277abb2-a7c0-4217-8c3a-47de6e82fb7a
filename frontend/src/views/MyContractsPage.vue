<template>
  <div class="my-contracts-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">我的合同</h1>
        <p class="page-subtitle">查看和管理您提交的所有合同</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="goToSubmit">
          <el-icon><Plus /></el-icon>
          提交新合同
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card stat-card--primary">
          <div class="stat-icon">
            <el-icon :size="28"><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total || 0 }}</div>
            <div class="stat-label">总提交数</div>
          </div>
        </div>

        <div class="stat-card stat-card--warning">
          <div class="stat-icon">
            <el-icon :size="28"><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.pending || 0 }}</div>
            <div class="stat-label">待审核</div>
          </div>
        </div>

        <div class="stat-card stat-card--success">
          <div class="stat-icon">
            <el-icon :size="28"><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.approved || 0 }}</div>
            <div class="stat-label">已通过</div>
          </div>
        </div>

        <div class="stat-card stat-card--danger">
          <div class="stat-icon">
            <el-icon :size="28"><Close /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.rejected || 0 }}</div>
            <div class="stat-label">已拒绝</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷筛选 -->
    <div class="filter-section">
      <div class="filter-tabs">
        <el-button
          v-for="filter in statusFilters"
          :key="filter.value"
          :type="activeFilter === filter.value ? 'primary' : ''"
          :plain="activeFilter !== filter.value"
          @click="setActiveFilter(filter.value)"
        >
          {{ filter.label }}
          <el-badge
            v-if="filter.count !== undefined"
            :value="filter.count"
            :hidden="filter.count === 0"
            class="filter-badge"
          />
        </el-button>
      </div>
    </div>

    <!-- 合同列表 -->
    <div class="page-content">
      <el-card class="list-card">
        <ContractList
          ref="contractListRef"
          filter-role="employee"
          @view-contract="handleViewContract"
          @view-contract-tab="handleViewContractInTab"
          @edit-contract="handleEditContract"
          @delete-contract="handleDeleteContract"
          @row-click="handleRowClick"
        />
      </el-card>
    </div>

    <!-- 合同详情对话框 -->
    <ContractDetailDialog
      v-model="showDetailDialog"
      :contract-id="currentContractId"
      @updated="handleContractUpdated"
    />

    <!-- 合同编辑对话框 -->
    <ContractEditDialog
      v-model="showEditDialog"
      :contract="currentContract"
      @updated="handleContractUpdated"
    />

    <!-- 重新提交对话框 -->
    <ResubmitDialog
      v-model="showResubmitDialog"
      :contract="currentContract"
      @resubmitted="handleContractResubmitted"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  Plus,
  Document,
  Clock,
  Check,
  Close,
  View,
} from "@element-plus/icons-vue";

import ContractList from "@/components/contract/ContractList.vue";
import ContractDetailDialog from "@/components/contract/ContractDetailDialog.vue";
import ContractEditDialog from "@/components/contract/ContractEditDialog.vue";
import ResubmitDialog from "@/components/contract/ResubmitDialog.vue";

import { useContracts } from "@/composables/useContracts";
import { useTabs } from "@/composables/useTabs";

const router = useRouter();

// 合同管理
const { stats, getStats } = useContracts();

// Tab管理
const { openTab } = useTabs();

// 组件引用
const contractListRef = ref();

// 对话框状态
const showDetailDialog = ref(false);
const showEditDialog = ref(false);
const showResubmitDialog = ref(false);

// 当前操作的合同
const currentContract = ref(null);
const currentContractId = ref(null);

// 当前筛选条件
const activeFilter = ref("");

// 状态筛选选项
const statusFilters = computed(() => [
  {
    label: "全部",
    value: "",
    count: stats.value.total,
  },
  {
    label: "待审核",
    value: "pending",
    count: stats.value.pending,
  },
  {
    label: "已通过",
    value: "approved",
    count: stats.value.approved,
  },
  {
    label: "已拒绝",
    value: "rejected",
    count: stats.value.rejected,
  },
]);

// 设置活动筛选
const setActiveFilter = (filterValue) => {
  activeFilter.value = filterValue;

  // 更新列表筛选条件
  if (contractListRef.value) {
    contractListRef.value.statusFilter = filterValue;
    contractListRef.value.handleFilter();
  }
};

// 跳转到提交页面
const goToSubmit = () => {
  // 使用Tab系统打开提交合同页面，而不是路由跳转
  openTab({
    key: "submit",
    title: "提交合同",
    path: "/submit",
    component: "SubmitPage",
    icon: "Upload",
  });
};

// 查看合同详情（弹窗方式）
const handleViewContract = (contract) => {
  currentContractId.value = contract.id;
  showDetailDialog.value = true;
};

// 查看合同详情（Tab方式）
const handleViewContractInTab = (contract) => {
  openTab({
    key: `contract-detail-${contract.id}`,
    title: `合同详情 - ${contract.serial_number}`,
    component: "ContractDetailTab",
    icon: "Document",
    params: {
      contractId: contract.id,
      contract: contract,
    },
  });
};

// 编辑合同
const handleEditContract = (contract) => {
  if (contract.status === "rejected") {
    // 如果是被拒绝的合同，打开重新提交对话框
    currentContract.value = contract;
    showResubmitDialog.value = true;
  } else {
    // 否则打开编辑对话框
    currentContract.value = contract;
    showEditDialog.value = true;
  }
};

// 删除合同
const handleDeleteContract = (contract) => {
  // 删除逻辑在 ContractList 组件中处理，统计数据已在组件内部更新
  // 不再调用 refreshStats()，避免不必要的API请求
};

// 处理行点击 - 直接跳转到Tab页面
const handleRowClick = (row) => {
  handleViewContractInTab(row);
};

// 处理合同更新
const handleContractUpdated = () => {
  refreshStats();
  refreshList();
};

// 处理合同重新提交
const handleContractResubmitted = () => {
  refreshStats();
  refreshList();
  ElMessage.success("合同重新提交成功");
};

// 刷新统计数据
const refreshStats = async () => {
  await getStats();
};

// 刷新列表
const refreshList = () => {
  if (contractListRef.value) {
    contractListRef.value.refreshList();
  }
};

// 监听统计数据变化，更新筛选标签
watch(
  stats,
  () => {
    // 统计数据更新后，筛选标签会自动更新
  },
  { deep: true },
);

// 组件挂载时获取数据
onMounted(() => {
  refreshStats();
});
</script>

<style scoped>
.my-contracts-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-section {
  padding: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.stat-card--primary .stat-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
}

.stat-card--warning .stat-icon {
  background: linear-gradient(135deg, #e6a23c, #b88230);
}

.stat-card--success .stat-icon {
  background: linear-gradient(135deg, #67c23a, #529b2e);
}

.stat-card--danger .stat-icon {
  background: linear-gradient(135deg, #f56c6c, #c45656);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.filter-section {
  padding: 0 24px 24px;
}

.filter-tabs {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-badge {
  margin-left: 8px;
}

.page-content {
  padding: 0 24px 24px;
}

.list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-section {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .filter-section {
    padding: 0 16px 16px;
  }

  .filter-tabs {
    padding: 12px;
    justify-content: center;
  }

  .page-content {
    padding: 0 16px 16px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-tabs {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
