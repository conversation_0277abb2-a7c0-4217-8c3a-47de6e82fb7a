<template>
  <div class="login-container">
    <!-- 主登录卡片 -->
    <div class="login-card">
      <!-- 头部区域 -->
      <div class="login-header">
        <div class="logo-section">
          <img src="/logo.png" alt="宿迁烟草" class="company-logo" />
        </div>
        <div class="title-section">
          <h1 class="system-title">合同审核系统</h1>
          <p class="system-subtitle">Contract Review System</p>
        </div>
      </div>

      <!-- 登录表单区域 -->
      <div class="login-form-section">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          size="large"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              clearable
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              show-password
              clearable
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              class="login-btn"
              @click="handleLogin"
            >
              {{ loading ? "登录中..." : "登录" }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 演示账号区域 -->
        <div class="demo-section">
          <div class="demo-header">
            <span class="demo-label">演示账号</span>
          </div>
          <div class="demo-accounts">
            <div class="demo-account" @click="fillAccount('admin', 'admin123')">
              <span class="role-badge admin">管理员</span>
            </div>
            <div
              class="demo-account"
              @click="fillAccount('county_reviewer', '123456')"
            >
              <span class="role-badge county-reviewer">县级审核员</span>
            </div>
            <div
              class="demo-account"
              @click="fillAccount('city_reviewer', '123456')"
            >
              <span class="role-badge city-reviewer">市级审核员</span>
            </div>

            <div
              class="demo-account"
              @click="fillAccount('employee', '123456')"
            >
              <span class="role-badge employee">员工</span>
            </div>

            <div
              class="demo-account"
              @click="fillAccount('legal_officer', '123456')"
            >
              <span class="role-badge legal-officer">法规员</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="footer-info">
      <p class="copyright">&copy; 2024 宿迁烟草专卖局 版权所有</p>
      <p class="version">合同审核管理系统 v1.0</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/user";
import { useRouter } from "vue-router";

// 用户状态管理
const userStore = useUserStore();
const router = useRouter();

// 从store中获取状态和方法
const { login, loading, isAuthenticated } = userStore;

// 表单引用
const loginFormRef = ref();

// 登录表单数据
const loginForm = reactive({
  username: "",
  password: "",
});

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 20,
      message: "用户名长度应在3-20个字符之间",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度至少6个字符", trigger: "blur" },
  ],
};

// 处理登录
const handleLogin = async () => {
  // 验证表单
  const valid = await loginFormRef.value.validate();
  if (!valid) {
    return;
  }

  // 执行登录
  const success = await login(loginForm);

  if (success) {
    // 登录成功，等待状态更新后跳转到仪表盘
    await new Promise((resolve) => setTimeout(resolve, 200)); // 等待200ms确保状态更新
    const redirect = router.currentRoute.value.query.redirect || "/dashboard";

    // 尝试使用router.push跳转
    try {
      await router.push(redirect);
    } catch (error) {
      console.warn("路由跳转失败，使用window.location跳转:", error);
      // 如果路由跳转失败，使用window.location
      window.location.href = redirect;
    }
  } else {
  }
};

// 填充演示账号
const fillAccount = (username, password) => {
  loginForm.username = username;
  loginForm.password = password;
  ElMessage.info(`已填充${username}账号信息，点击登录即可`);
};

// 组件挂载时检查登录状态
onMounted(() => {
  if (isAuthenticated.value) {
    // 如果已经登录，跳转到工作台
    // router.push('/dashboard') // 路由跳转在 useAuth 中处理
  }
});
</script>

<style scoped>
/* 重置和基础样式 */
* {
  box-sizing: border-box;
}

/* 主容器 */
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  padding: 20px;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial,
    sans-serif;
}

/* 登录卡片 */
.login-card {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
}

/* 头部区域 */
.login-header {
  text-align: center;
  padding: 40px 32px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.logo-section {
  margin-bottom: 20px;
}

.company-logo {
  /* 使用相对单位和响应式设计 - 让Logo在大屏幕上显示更大 */
  height: auto;
  width: 100%;
  max-width: min(200px, 40vw);
  min-width: 80px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.system-title {
  font-size: 24px;
  font-weight: 600;
  margin: 16px 0 6px;
  color: #303133;
  line-height: 1.3;
}

.system-subtitle {
  font-size: 13px;
  color: #909399;
  margin: 0;
  font-weight: 400;
}

/* 表单区域 */
.login-form-section {
  padding: 32px;
}

/* 表单样式 */
.login-form-section :deep(.el-form-item) {
  margin-bottom: 20px;
}

.login-form-section :deep(.el-input__wrapper) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.3s;
  box-shadow: none;
  height: 40px;
  background-color: #ffffff;
}

.login-form-section :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.login-form-section :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.login-form-section :deep(.el-input__inner) {
  font-size: 14px;
  color: #303133;
}

.login-form-section :deep(.el-input__prefix) {
  color: #909399;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  background: #409eff;
  border: none;
  border-radius: 4px;
  transition: background-color 0.3s;
  color: white;
}

.login-btn:hover {
  background: #66b1ff;
}

.login-btn:active {
  background: #3a8ee6;
}

/* 演示账号区域 */
.demo-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.demo-header {
  text-align: center;
  margin-bottom: 12px;
}

.demo-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.demo-accounts {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.demo-account {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.demo-account:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.role-badge {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
}

.role-badge.admin {
  background: #f56c6c;
}

.role-badge.county-reviewer {
  background: #409eff;
}

.role-badge.city-reviewer {
  background: #909399;
}

.role-badge.employee {
  background: #67c23a;
}

.role-badge.legal-officer {
  background: #e6a23c;
}

/* 底部版权信息 */
.footer-info {
  margin-top: 32px;
  text-align: center;
}

.copyright {
  font-size: 12px;
  color: #909399;
  margin: 0 0 4px;
}

.version {
  font-size: 11px;
  color: #c0c4cc;
  margin: 0;
}

/* 响应式设计 */
/* 平板设备 */
@media (max-width: 768px) {
  .company-logo {
    max-width: min(160px, 42vw);
    min-width: 70px;
  }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    max-width: 100%;
  }

  .login-header {
    padding: 32px 24px 20px;
  }

  .company-logo {
    max-width: min(140px, 45vw);
    min-width: 60px;
  }

  .system-title {
    font-size: 20px;
  }

  .login-form-section {
    padding: 24px;
  }

  .demo-section {
    margin-top: 20px;
    padding-top: 16px;
  }

  .footer-info {
    margin-top: 24px;
  }
}
</style>
