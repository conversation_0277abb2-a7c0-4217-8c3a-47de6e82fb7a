<template>
  <div class="review-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">{{ pageInfo.title }}</h1>
        <p class="page-subtitle">{{ pageInfo.subtitle }}</p>
      </div>
      <div class="header-right">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card stat-card--primary">
          <div class="stat-icon">
            <el-icon :size="28"><FolderOpened /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total || 0 }}</div>
            <div class="stat-label">分配给我</div>
          </div>
        </div>

        <div class="stat-card stat-card--warning">
          <div class="stat-icon">
            <el-icon :size="28"><Timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.pending || 0 }}</div>
            <div class="stat-label">待审核</div>
          </div>
        </div>

        <div class="stat-card stat-card--success">
          <div class="stat-icon">
            <el-icon :size="28"><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ completedCount }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷筛选 -->
    <div class="filter-section">
      <div class="filter-tabs">
        <el-button
          v-for="filter in statusFilters"
          :key="filter.value"
          :type="activeFilter === filter.value ? 'primary' : ''"
          :plain="activeFilter !== filter.value"
          @click="setActiveFilter(filter.value)"
        >
          {{ filter.label }}
          <el-badge
            v-if="filter.count !== undefined"
            :value="filter.count"
            :hidden="filter.count === 0"
            class="filter-badge"
          />
        </el-button>
      </div>
    </div>

    <!-- 合同列表 -->
    <div class="page-content">
      <el-card class="list-card">
        <ContractList
          ref="contractListRef"
          filter-role="reviewer"
          :default-status-filter="pageType"
          @view-contract="handleViewContract"
          @view-contract-tab="handleViewContractInTab"
          @review-contract="handleReviewContract"
          @row-click="handleRowClick"
        />
      </el-card>
    </div>

    <!-- 合同详情对话框 -->
    <ContractDetailDialog
      v-model="showDetailDialog"
      :contract-id="currentContractId"
      @edit="handleEditContract"
      @updated="handleContractUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import {
  Refresh,
  FolderOpened,
  Clock,
  Loading,
  Check,
  Timer,
} from "@element-plus/icons-vue";

import ContractList from "@/components/contract/ContractList.vue";
import ContractDetailDialog from "@/components/contract/ContractDetailDialog.vue";

import { useContracts } from "@/composables/useContracts";
import { useTabs } from "@/composables/useTabs";
import { useUserStore } from "@/stores/user";

// 路由信息
const route = useRoute();

// 用户信息
const userStore = useUserStore();
const userRole = computed(() => userStore.userRole);

// 合同管理
const { stats, getStats } = useContracts();

// Tab管理
const { openTab } = useTabs();

// 组件引用
const contractListRef = ref();

// 对话框状态
const showDetailDialog = ref(false);

// 当前操作的合同
const currentContractId = ref(null);

// 当前筛选条件
const activeFilter = ref("");

// 统一的审核管理页面，默认显示全部
const pageType = computed(() => {
  return "all";
});

// 页面标题和描述
const pageInfo = computed(() => {
  const role = userRole.value;

  // 根据用户角色返回不同的标题和描述
  switch (role) {
    case "county_reviewer":
      return {
        title: "县级审核",
        subtitle:
          "管理分配给您的县级合同审核任务，使用下方筛选按钮查看不同状态的合同",
      };
    case "city_reviewer":
      return {
        title: "市级审核",
        subtitle:
          "管理分配给您的市级合同审核任务，使用下方筛选按钮查看不同状态的合同",
      };
    case "admin":
      return {
        title: "审核管理",
        subtitle:
          "管理系统中的所有合同审核任务，使用下方筛选按钮查看不同状态的合同",
      };
    default:
      return {
        title: "审核管理",
        subtitle:
          "管理分配给您的合同审核任务，使用下方筛选按钮查看不同状态的合同",
      };
  }
});

// 计算已完成数量
const completedCount = computed(() => {
  return (stats.value.approved || 0) + (stats.value.rejected || 0);
});

// 状态筛选选项 - 审核员显示完整状态
const statusFilters = computed(() => [
  {
    label: "全部",
    value: "",
    count: stats.value.total,
  },
  {
    label: "待审核",
    value: "pending",
    count: stats.value.pending,
  },
  {
    label: "已通过",
    value: "approved",
    count: stats.value.approved,
  },
  {
    label: "已拒绝",
    value: "rejected",
    count: stats.value.rejected,
  },
]);

// 设置活动筛选
const setActiveFilter = (filterValue) => {
  activeFilter.value = filterValue;

  // 更新列表筛选条件
  if (
    contractListRef.value &&
    typeof contractListRef.value.handleFilter === "function"
  ) {
    contractListRef.value.statusFilter = filterValue;
    contractListRef.value.handleFilter();
  } else {
    console.warn("ContractList组件未正确加载或handleFilter方法不存在");
  }
};

// 查看合同详情（弹窗方式）
const handleViewContract = (contract) => {
  currentContractId.value = contract.id;
  showDetailDialog.value = true;
};

// 查看合同详情（Tab方式）
const handleViewContractInTab = (contract) => {
  openTab({
    key: `contract-detail-${contract.id}`,
    title: `合同详情 - ${contract.serial_number}`,
    component: "ContractDetailTab",
    icon: "Document",
    params: {
      contractId: contract.id,
      contract: contract,
    },
  });
};

// 审核合同 - 改为直接打开Tab页面
const handleReviewContract = (contract) => {
  handleViewContractInTab(contract);
};

// 编辑合同（从详情对话框触发）
const handleEditContract = (contract) => {
  // 审核员不能编辑合同，这里可以显示提示
  ElMessage.info("审核员无法编辑合同，请联系提交人修改");
};

// 处理行点击 - 统一使用Tab方式打开
const handleRowClick = (row) => {
  handleViewContractInTab(row);
};

// 处理合同更新
const handleContractUpdated = () => {
  refreshStats();
  refreshList();
};

// 刷新统计数据
const refreshStats = async () => {
  await getStats();
};

// 刷新列表
const refreshList = () => {
  if (contractListRef.value) {
    contractListRef.value.refreshList();
  }
};

// 刷新所有数据
const refreshData = () => {
  refreshStats();
  refreshList();
  ElMessage.success("数据已刷新");
};

// 监听统计数据变化，更新筛选标签
watch(
  stats,
  () => {
    // 统计数据更新后，筛选标签会自动更新
  },
  { deep: true },
);

// 组件挂载时获取数据
onMounted(async () => {
  await refreshStats();

  // 等待下一个tick确保子组件已挂载
  await nextTick();

  // 默认显示全部合同，不设置特定筛选条件
  activeFilter.value = "";
  setActiveFilter("");
});
</script>

<style scoped>
.review-manage-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-section {
  padding: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.stat-card--primary .stat-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
}

.stat-card--warning .stat-icon {
  background: linear-gradient(135deg, #e6a23c, #b88230);
}

.stat-card--info .stat-icon {
  background: linear-gradient(135deg, #909399, #73767a);
}

.stat-card--success .stat-icon {
  background: linear-gradient(135deg, #67c23a, #529b2e);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.filter-section {
  padding: 0 24px 24px;
}

.filter-tabs {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-badge {
  margin-left: 8px;
}

.page-content {
  padding: 0 24px 24px;
}

.list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-section {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .filter-section {
    padding: 0 16px 16px;
  }

  .filter-tabs {
    padding: 12px;
    justify-content: center;
  }

  .page-content {
    padding: 0 16px 16px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-tabs {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
