<template>
  <div class="cache-test-page">
    <el-card header="PDF缓存测试">
      <div class="space-y-4">
        <!-- 测试说明 -->
        <el-alert
          title="PDF缓存Bug修复测试"
          type="info"
          description="此页面用于测试PDF缓存混乱问题的修复效果。请按照以下步骤进行测试："
          show-icon
          :closable="false"
        />

        <!-- 测试步骤 -->
        <el-card header="测试步骤">
          <ol class="test-steps">
            <li>1. 上传几个不同的PDF合同文件</li>
            <li>2. 查看合同详情页面，确认PDF显示正确</li>
            <li>3. 清空数据库（或重置数据）</li>
            <li>4. 重新启动前后端服务</li>
            <li>5. 上传新的PDF文件</li>
            <li>6. 检查新合同的PDF是否显示正确（不应该显示旧的PDF）</li>
          </ol>
        </el-card>

        <!-- 缓存信息 -->
        <el-card header="当前缓存信息">
          <div class="cache-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="PDF缓存数量">
                {{ cacheInfo.count }}
              </el-descriptions-item>
              <el-descriptions-item label="缓存总大小">
                {{ formatSize(cacheInfo.totalSize) }}
              </el-descriptions-item>
              <el-descriptions-item label="最后更新时间">
                {{ cacheInfo.lastUpdated }}
              </el-descriptions-item>
              <el-descriptions-item label="缓存状态">
                <el-tag :type="cacheInfo.count > 0 ? 'success' : 'info'">
                  {{ cacheInfo.count > 0 ? '有缓存' : '无缓存' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 测试操作 -->
        <el-card header="测试操作">
          <div class="test-actions">
            <el-button 
              type="primary" 
              @click="refreshCacheInfo"
              :loading="loading.refresh"
            >
              刷新缓存信息
            </el-button>
            
            <el-button 
              type="warning" 
              @click="simulateDatabaseReset"
              :loading="loading.simulate"
            >
              模拟数据库重置
            </el-button>
            
            <el-button 
              type="danger" 
              @click="clearAllCache"
              :loading="loading.clear"
            >
              手动清空缓存
            </el-button>
          </div>
        </el-card>

        <!-- 测试结果 -->
        <el-card header="测试结果" v-if="testResults.length > 0">
          <el-timeline>
            <el-timeline-item
              v-for="(result, index) in testResults"
              :key="index"
              :timestamp="result.timestamp"
              :type="result.type"
            >
              {{ result.message }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import pdfCacheManager from '@/utils/pdfCacheManager';
import cacheSync from '@/utils/cacheSync';

// 响应式数据
const cacheInfo = ref({
  count: 0,
  totalSize: 0,
  lastUpdated: '未知'
});

const loading = ref({
  refresh: false,
  simulate: false,
  clear: false
});

const testResults = ref([]);

// 格式化文件大小
const formatSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 添加测试结果
const addTestResult = (message, type = 'primary') => {
  testResults.value.unshift({
    message,
    type,
    timestamp: new Date().toLocaleString('zh-CN')
  });
};

// 刷新缓存信息
const refreshCacheInfo = async () => {
  loading.value.refresh = true;
  try {
    const stats = await pdfCacheManager.getCacheStats();
    cacheInfo.value = {
      count: stats.fileCount,
      totalSize: stats.totalSize,
      lastUpdated: new Date().toLocaleString('zh-CN')
    };
    addTestResult(`缓存信息已刷新：${stats.fileCount} 个文件，总大小 ${formatSize(stats.totalSize)}`, 'success');
  } catch (error) {
    console.error('刷新缓存信息失败:', error);
    ElMessage.error('刷新缓存信息失败');
    addTestResult('刷新缓存信息失败', 'danger');
  } finally {
    loading.value.refresh = false;
  }
};

// 模拟数据库重置
const simulateDatabaseReset = async () => {
  loading.value.simulate = true;
  try {
    // 触发缓存同步检查
    await cacheSync.checkAndCleanCache();
    addTestResult('已触发数据库重置检查，如果检测到重置会自动清理缓存', 'warning');
    await refreshCacheInfo();
  } catch (error) {
    console.error('模拟数据库重置失败:', error);
    ElMessage.error('模拟数据库重置失败');
    addTestResult('模拟数据库重置失败', 'danger');
  } finally {
    loading.value.simulate = false;
  }
};

// 清空所有缓存
const clearAllCache = async () => {
  loading.value.clear = true;
  try {
    await cacheSync.forceClearCache();
    addTestResult('所有缓存已清空', 'success');
    await refreshCacheInfo();
  } catch (error) {
    console.error('清空缓存失败:', error);
    ElMessage.error('清空缓存失败');
    addTestResult('清空缓存失败', 'danger');
  } finally {
    loading.value.clear = false;
  }
};

// 组件挂载时获取缓存信息
onMounted(() => {
  refreshCacheInfo();
  addTestResult('缓存测试页面已加载', 'info');
});
</script>

<style scoped>
.cache-test-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.test-steps {
  padding-left: 20px;
}

.test-steps li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.test-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.cache-info {
  margin-top: 16px;
}
</style>
