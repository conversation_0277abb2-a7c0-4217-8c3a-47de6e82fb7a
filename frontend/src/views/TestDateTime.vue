<template>
  <div class="test-datetime">
    <h2>🕐 前端时间处理功能测试</h2>

    <div class="test-section">
      <h3>1. 当前时间测试</h3>
      <p><strong>北京时间:</strong> {{ currentBeijingTime }}</p>
      <p><strong>UTC时间:</strong> {{ currentUTCTime }}</p>
    </div>

    <div class="test-section">
      <h3>2. 时间格式化测试</h3>
      <p><strong>输入UTC时间:</strong> {{ testUTCTime }}</p>
      <p><strong>转换为北京时间:</strong> {{ formatTime(testUTCTime) }}</p>
      <p>
        <strong>自定义格式:</strong>
        {{ formatTime(testUTCTime, "YYYY年MM月DD日 HH:mm") }}
      </p>
    </div>

    <div class="test-section">
      <h3>3. 相对时间测试</h3>
      <p><strong>1小时前:</strong> {{ formatRelativeTime(oneHourAgo) }}</p>
      <p><strong>1天前:</strong> {{ formatRelativeTime(oneDayAgo) }}</p>
    </div>

    <div class="test-section">
      <h3>4. 日期判断测试</h3>
      <p>
        <strong>今天是否为今天:</strong> {{ isToday(todayUTC) ? "是" : "否" }}
      </p>
      <p>
        <strong>昨天是否为今天:</strong>
        {{ isToday(yesterdayUTC) ? "是" : "否" }}
      </p>
      <p>
        <strong>昨天是否为昨天:</strong>
        {{ isYesterday(yesterdayUTC) ? "是" : "否" }}
      </p>
    </div>

    <div class="test-section">
      <h3>5. 时区转换测试</h3>
      <p><strong>北京时间:</strong> {{ beijingTimeInput }}</p>
      <p><strong>转换为UTC:</strong> {{ toUTC(beijingTimeInput) }}</p>
    </div>

    <div class="test-section">
      <h3>6. 日期工具函数测试</h3>
      <p><strong>今天:</strong> {{ getToday() }}</p>
      <p><strong>昨天:</strong> {{ getYesterday() }}</p>
      <p><strong>本周开始:</strong> {{ getWeekStart() }}</p>
      <p><strong>本月开始:</strong> {{ getMonthStart() }}</p>
    </div>

    <div class="test-section">
      <h3>7. 日期差计算测试</h3>
      <p><strong>日期1:</strong> {{ formatTime(date1) }}</p>
      <p><strong>日期2:</strong> {{ formatTime(date2) }}</p>
      <p><strong>相差天数:</strong> {{ daysBetween(date1, date2) }}</p>
    </div>

    <div class="test-section">
      <h3>8. 实时时间更新</h3>
      <p><strong>实时北京时间:</strong> {{ realtimeBeijingTime }}</p>
      <p><em>每秒更新一次</em></p>
    </div>

    <div class="test-result">
      <h3>✅ 测试结果</h3>
      <p>前端时间处理功能正常工作！所有时间都已正确转换为北京时间显示。</p>
    </div>
  </div>
</template>

<script>
import {
  formatTime,
  formatRelativeTime,
  isToday,
  isYesterday,
  getToday,
  getYesterday,
  getWeekStart,
  getMonthStart,
  daysBetween,
  now,
  toUTC,
} from "@/utils/dateUtils";

export default {
  name: "TestDateTime",
  data() {
    return {
      currentBeijingTime: "",
      currentUTCTime: "",
      realtimeBeijingTime: "",
      testUTCTime: "2024-01-15T06:30:00.000Z",
      oneHourAgo: "",
      oneDayAgo: "",
      todayUTC: "",
      yesterdayUTC: "",
      beijingTimeInput: "2024-01-15 14:30:00",
      date1: "2024-01-15T00:00:00.000Z",
      date2: "2024-01-20T00:00:00.000Z",
      timer: null,
    };
  },
  mounted() {
    this.initTestData();
    this.startRealtimeUpdate();
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    formatTime,
    formatRelativeTime,
    isToday,
    isYesterday,
    getToday,
    getYesterday,
    getWeekStart,
    getMonthStart,
    daysBetween,
    toUTC,

    initTestData() {
      const now = new Date();
      this.currentBeijingTime = formatTime(now);
      this.currentUTCTime = now.toISOString();

      this.oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
      this.oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      this.todayUTC = new Date().toISOString();
      this.yesterdayUTC = new Date(
        Date.now() - 24 * 60 * 60 * 1000,
      ).toISOString();
    },

    startRealtimeUpdate() {
      this.updateRealtimeTime();
      this.timer = setInterval(() => {
        this.updateRealtimeTime();
      }, 1000);
    },

    updateRealtimeTime() {
      this.realtimeBeijingTime = now();
    },
  },
};
</script>

<style scoped>
.test-datetime {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.test-section p {
  margin: 8px 0;
  font-family: "Courier New", monospace;
}

.test-result {
  margin-top: 30px;
  padding: 20px;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  color: #155724;
}

.test-result h3 {
  margin-top: 0;
  color: #155724;
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}
</style>
