<template>
  <div class="system-logs-page">
    <div class="page-header">
      <h2>系统日志</h2>
      <div class="header-actions">
        <el-button type="primary" @click="refreshLogs">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="danger" @click="showCleanupDialog">
          <el-icon><Delete /></el-icon>
          清理日志
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="用户">
          <el-select v-model="filters.userId" placeholder="选择用户" clearable>
            <el-option label="全部用户" value="" />
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="操作类型">
          <el-select
            v-model="filters.action"
            placeholder="选择操作类型"
            clearable
          >
            <el-option label="全部操作" value="" />
            <el-option
              v-for="action in actions"
              :key="action"
              :label="getActionLabel(action)"
              :value="action"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="资源类型">
          <el-select
            v-model="filters.resourceType"
            placeholder="选择资源类型"
            clearable
          >
            <el-option label="全部资源" value="" />
            <el-option
              v-for="resourceType in resourceTypes"
              :key="resourceType"
              :label="getResourceTypeLabel(resourceType)"
              :value="resourceType"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option label="全部状态" value="" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>

        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateRangeChange"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="searchLogs">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalLogs || 0 }}</div>
            <div class="stat-label">总操作数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value success">{{ stats.successLogs || 0 }}</div>
            <div class="stat-label">成功操作</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value danger">{{ stats.failedLogs || 0 }}</div>
            <div class="stat-label">失败操作</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ stats.todayLogs || 0 }}</div>
            <div class="stat-label">今日操作</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 日志列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="logs"
        stripe
        border
        height="500"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column label="用户" width="120">
          <template #default="{ row }">
            <div class="user-info">
              <div class="username">{{ row.username }}</div>
              <el-tag size="small" :type="getRoleColor(row.role)">
                {{ getRoleLabel(row.role) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-tag size="small" type="info">
              {{ getActionLabel(row.action) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="资源" width="100">
          <template #default="{ row }">
            <el-tag size="small" type="warning">
              {{ getResourceTypeLabel(row.resource_type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="resource_id" label="资源ID" width="80" />

        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag size="small" :type="getStatusColor(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="IP地址" width="120">
          <template #default="{ row }">
            <span class="ip-address">{{ row.ip_address || "-" }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作时间" width="180">
          <template #default="{ row }">
            <span class="datetime">{{ formatDateTime(row.created_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="showLogDetails(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="操作详情"
      width="60%"
      :before-close="handleDetailDialogClose"
    >
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作ID">{{
            selectedLog.id
          }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{
            selectedLog.username
          }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">{{
            getActionLabel(selectedLog.action)
          }}</el-descriptions-item>
          <el-descriptions-item label="资源类型">{{
            getResourceTypeLabel(selectedLog.resource_type)
          }}</el-descriptions-item>
          <el-descriptions-item label="资源ID">{{
            selectedLog.resource_id || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(selectedLog.status)">
              {{ getStatusLabel(selectedLog.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">{{
            selectedLog.ip_address || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">{{
            formatDateTime(selectedLog.created_at)
          }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>用户代理</h4>
          <pre class="user-agent">{{ selectedLog.user_agent || "-" }}</pre>
        </div>

        <div class="detail-section">
          <h4>详细信息</h4>
          <pre class="details-json">{{
            formatLogDetails(selectedLog.details)
          }}</pre>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 清理日志对话框 -->
    <el-dialog
      v-model="cleanupDialogVisible"
      title="清理日志"
      width="400px"
      :before-close="handleCleanupDialogClose"
    >
      <el-form :model="cleanupForm" label-width="100px">
        <el-form-item label="保留天数">
          <el-input-number
            v-model="cleanupForm.days"
            :min="1"
            :max="365"
            controls-position="right"
          />
          <div class="form-tip">
            将删除 {{ cleanupForm.days }} 天前的所有日志记录
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="cleanupDialogVisible = false">取消</el-button>
        <el-button
          type="danger"
          :loading="cleanupLoading"
          @click="handleCleanupConfirm"
        >
          确认清理
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, ref, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh, Delete } from "@element-plus/icons-vue";
import {
  getOperationLogs,
  getOperationStats,
  getLogActions,
  cleanupOldLogs,
  getActionLabel,
  getResourceTypeLabel,
  getStatusLabel,
  getStatusColor,
  formatLogDetails,
} from "@/api/logs";
import { usersAPI } from "@/api/users";
import { useAuth } from "@/composables/useAuth";

export default {
  name: "SystemLogsPage",
  components: {
    Refresh,
    Delete,
  },
  setup() {
    const { user } = useAuth();

    const loading = ref(false);
    const cleanupLoading = ref(false);
    const logs = ref([]);
    const users = ref([]);
    const actions = ref([]);
    const resourceTypes = ref([]);
    const stats = ref({});
    const selectedLog = ref(null);
    const detailDialogVisible = ref(false);
    const cleanupDialogVisible = ref(false);
    const dateRange = ref([]);

    const filters = reactive({
      userId: "",
      action: "",
      resourceType: "",
      status: "",
      startDate: "",
      endDate: "",
    });

    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0,
    });

    const cleanupForm = reactive({
      days: 90,
    });

    // 获取用户列表
    const loadUsers = async () => {
      try {
        const response = await usersAPI.getList();
        if (response.success) {
          users.value = response.data.users || [];
        }
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("获取用户列表失败:", error);
        }
      }
    };

    // 获取操作类型列表
    const loadActions = async () => {
      try {
        const response = await getLogActions();
        if (response.success) {
          actions.value = response.data.actions || [];
          resourceTypes.value = response.data.resourceTypes || [];
        }
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("获取操作类型失败:", error);
        }
      }
    };

    // 获取统计数据
    const loadStats = async () => {
      try {
        const response = await getOperationStats({ period: "30d" });
        if (response.success) {
          const data = response.data;
          stats.value = {
            totalLogs: data.actionStats.reduce(
              (sum, item) => sum + item.count,
              0,
            ),
            successLogs:
              data.statusStats.find((item) => item.status === "success")
                ?.count || 0,
            failedLogs:
              data.statusStats.find((item) => item.status === "failed")
                ?.count || 0,
            todayLogs:
              data.dailyStats.find(
                (item) => item.date === new Date().toISOString().split("T")[0],
              )?.count || 0,
          };
        }
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("获取统计数据失败:", error);
        }
      }
    };

    // 获取日志列表
    const loadLogs = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.page,
          limit: pagination.limit,
          ...filters,
        };

        // 清理空值
        Object.keys(params).forEach((key) => {
          if (
            params[key] === "" ||
            params[key] === null ||
            params[key] === undefined
          ) {
            delete params[key];
          }
        });

        const response = await getOperationLogs(params);
        if (response.success) {
          logs.value = response.data.logs || [];
          pagination.total = response.data.total || 0;
        }
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error("获取日志列表失败:", error);
        }
        ElMessage.error("获取日志列表失败");
      } finally {
        loading.value = false;
      }
    };

    // 搜索日志
    const searchLogs = () => {
      pagination.page = 1;
      loadLogs();
    };

    // 重置筛选
    const resetFilters = () => {
      Object.keys(filters).forEach((key) => {
        filters[key] = "";
      });
      dateRange.value = [];
      pagination.page = 1;
      loadLogs();
    };

    // 刷新日志
    const refreshLogs = () => {
      loadLogs();
      loadStats();
    };

    // 处理日期范围变化
    const handleDateRangeChange = (value) => {
      if (value && value.length === 2) {
        filters.startDate = value[0];
        filters.endDate = value[1];
      } else {
        filters.startDate = "";
        filters.endDate = "";
      }
    };

    // 处理分页变化
    const handlePageChange = (page) => {
      pagination.page = page;
      loadLogs();
    };

    // 处理页面大小变化
    const handleSizeChange = (size) => {
      pagination.limit = size;
      pagination.page = 1;
      loadLogs();
    };

    // 显示日志详情
    const showLogDetails = (log) => {
      selectedLog.value = log;
      detailDialogVisible.value = true;
    };

    // 关闭详情对话框
    const handleDetailDialogClose = () => {
      detailDialogVisible.value = false;
      selectedLog.value = null;
    };

    // 显示清理对话框
    const showCleanupDialog = () => {
      cleanupDialogVisible.value = true;
    };

    // 关闭清理对话框
    const handleCleanupDialogClose = () => {
      cleanupDialogVisible.value = false;
    };

    // 确认清理日志
    const handleCleanupConfirm = async () => {
      try {
        await ElMessageBox.confirm(
          `确定要清理 ${cleanupForm.days} 天前的日志记录吗？此操作不可恢复。`,
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          },
        );

        cleanupLoading.value = true;
        const response = await cleanupOldLogs({ days: cleanupForm.days });

        if (response.success) {
          ElMessage.success(response.message);
          cleanupDialogVisible.value = false;
          refreshLogs();
        } else {
          ElMessage.error(response.message || "清理失败");
        }
      } catch (error) {
        if (error !== "cancel") {
          if (import.meta.env.DEV) {
            console.error("清理日志失败:", error);
          }
          ElMessage.error("清理日志失败");
        }
      } finally {
        cleanupLoading.value = false;
      }
    };

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return "-";
      return new Date(dateTime).toLocaleString("zh-CN");
    };

    // 获取角色标签
    const getRoleLabel = (role) => {
      const roleMap = {
        admin: "管理员",
        reviewer: "审核员",
        employee: "员工",
      };
      return roleMap[role] || role;
    };

    // 获取角色颜色
    const getRoleColor = (role) => {
      const colorMap = {
        admin: "danger",
        reviewer: "warning",
        employee: "success",
      };
      return colorMap[role] || "info";
    };

    // 初始化
    onMounted(() => {
      loadUsers();
      loadActions();
      loadStats();
      loadLogs();
    });

    return {
      loading,
      cleanupLoading,
      logs,
      users,
      actions,
      resourceTypes,
      stats,
      selectedLog,
      detailDialogVisible,
      cleanupDialogVisible,
      dateRange,
      filters,
      pagination,
      cleanupForm,
      loadLogs,
      searchLogs,
      resetFilters,
      refreshLogs,
      handleDateRangeChange,
      handlePageChange,
      handleSizeChange,
      showLogDetails,
      handleDetailDialogClose,
      showCleanupDialog,
      handleCleanupDialogClose,
      handleCleanupConfirm,
      formatDateTime,
      getRoleLabel,
      getRoleColor,
      getActionLabel,
      getResourceTypeLabel,
      getStatusLabel,
      getStatusColor,
      formatLogDetails,
    };
  },
};
</script>

<style scoped>
.system-logs-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card .stat-content {
  text-align: center;
}

.stat-card .stat-content .stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.stat-card .stat-content .stat-value.success {
  color: #67c23a;
}

.stat-card .stat-content .stat-value.danger {
  color: #f56c6c;
}

.stat-card .stat-content .stat-label {
  font-size: 14px;
  color: #666;
}

.table-card .user-info .username {
  font-weight: bold;
  margin-bottom: 2px;
}

.table-card .ip-address {
  font-family: monospace;
  font-size: 12px;
}

.table-card .datetime {
  font-size: 12px;
  color: #666;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.log-detail .detail-section {
  margin-top: 20px;
}

.log-detail .detail-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.log-detail .detail-section .user-agent,
.log-detail .detail-section .details-json {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.form-tip {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}
</style>
