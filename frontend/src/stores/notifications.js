/**
 * 通知状态管理
 */

import { defineStore } from "pinia";
import { ref, computed } from "vue";
import notificationAPI from "@/api/notifications";

export const useNotificationStore = defineStore("notifications", () => {
  // 状态
  const notifications = ref([]);
  const unreadCount = ref(0);
  const loading = ref(false);
  const error = ref(null);

  // 计算属性
  const unreadNotifications = computed(() =>
    notifications.value.filter((n) => !n.is_read),
  );

  const hasUnreadNotifications = computed(() => unreadCount.value > 0);

  // 获取通知列表
  const fetchNotifications = async (params = {}) => {
    // 检查是否正在登出或用户未认证
    if (window.isLoggingOut) {
      return { data: { data: [], total: 0 } };
    }

    loading.value = true;
    error.value = null;

    try {
      const response = await notificationAPI.getNotifications(params);
      if (response.data) {
        notifications.value = response.data.data || [];
        // 更新未读数量
        await fetchUnreadCount();
      }
      return response;
    } catch (err) {
      // 如果是取消请求的错误，不设置错误状态
      if (err.name === "CanceledError" || err.code === "ERR_CANCELED") {
        return { data: { data: [], total: 0 } }; // 返回空数据而不是抛出错误
      }

      // 如果是登出过程中的错误，不设置错误状态
      if (err.message === "用户正在登出") {
        return { data: { data: [], total: 0 } };
      }

      // 如果是401错误（用户登出时），不设置错误状态
      if (err.status === 401 || err.response?.status === 401) {
        return { data: { data: [], total: 0 } }; // 返回空数据而不是抛出错误
      }

      error.value = err.message || "获取通知失败";
      console.error("获取通知失败:", err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 获取未读通知数量
  const fetchUnreadCount = async () => {
    // 检查是否正在登出
    if (window.isLoggingOut) {
      return { data: { count: unreadCount.value } };
    }

    try {
      const response = await notificationAPI.getUnreadCount();
      if (response.data) {
        unreadCount.value = response.data.count || 0;
      }
      return response;
    } catch (err) {
      // 如果是取消请求的错误或401错误，不记录错误日志
      if (
        err.name === "CanceledError" ||
        err.code === "ERR_CANCELED" ||
        err.status === 401 ||
        err.response?.status === 401 ||
        err.message === "用户正在登出"
      ) {
        return { data: { count: unreadCount.value } }; // 返回当前值
      }
      console.error("获取未读通知数量失败:", err);
      // 不抛出错误，避免影响主要功能
    }
  };

  // 标记通知为已读
  const markAsRead = async (id) => {
    try {
      await notificationAPI.markAsRead(id);

      // 更新本地状态
      const notification = notifications.value.find((n) => n.id === id);
      if (notification && !notification.is_read) {
        notification.is_read = true;
        notification.read_at = new Date().toISOString();
        unreadCount.value = Math.max(0, unreadCount.value - 1);
      }

      return true;
    } catch (err) {
      error.value = err.message || "标记已读失败";
      console.error("标记通知已读失败:", err);
      throw err;
    }
  };

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      await notificationAPI.markAllAsRead();

      // 更新本地状态
      notifications.value.forEach((notification) => {
        if (!notification.is_read) {
          notification.is_read = true;
          notification.read_at = new Date().toISOString();
        }
      });
      unreadCount.value = 0;

      return true;
    } catch (err) {
      error.value = err.message || "标记全部已读失败";
      console.error("标记全部通知已读失败:", err);
      throw err;
    }
  };

  // 删除通知
  const deleteNotification = async (id) => {
    try {
      await notificationAPI.deleteNotification(id);

      // 更新本地状态
      const index = notifications.value.findIndex((n) => n.id === id);
      if (index > -1) {
        const notification = notifications.value[index];
        if (!notification.is_read) {
          unreadCount.value = Math.max(0, unreadCount.value - 1);
        }
        notifications.value.splice(index, 1);
      }

      return true;
    } catch (err) {
      error.value = err.message || "删除通知失败";
      console.error("删除通知失败:", err);
      throw err;
    }
  };

  // 添加新通知（用于实时更新）
  const addNotification = (notification) => {
    notifications.value.unshift(notification);
    if (!notification.is_read) {
      unreadCount.value += 1;
    }
  };

  // 清空错误状态
  const clearError = () => {
    error.value = null;
  };

  // 重置状态
  const reset = () => {
    notifications.value = [];
    unreadCount.value = 0;
    loading.value = false;
    error.value = null;
  };

  // 初始化（获取未读数量）
  const init = async () => {
    try {
      await fetchUnreadCount();
    } catch (err) {
      console.error("初始化通知状态失败:", err);
    }
  };

  return {
    // 状态
    notifications,
    unreadCount,
    loading,
    error,

    // 计算属性
    unreadNotifications,
    hasUnreadNotifications,

    // 方法
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    addNotification,
    clearError,
    reset,
    init,
  };
});
