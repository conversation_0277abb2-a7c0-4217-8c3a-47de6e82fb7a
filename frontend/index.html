<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="合同审核系统 - 高效的合同审核管理平台" />
    <meta name="keywords" content="合同审核,文档管理,审核流程,企业管理" />
    <title>宿迁烟草合同审核系统</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 防止页面闪烁 -->
    <style>
      #app {
        height: 100vh;
        background-color: #f5f5f5;
      }
      
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 16px;
        color: #666;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载动画 -->
      <div class="loading-container" id="initial-loading">
        <div style="text-align: center;">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载合同审核系统...</div>
        </div>
      </div>
    </div>
    
    <script type="module" src="/src/main.js"></script>
    
    <!-- 移除初始加载动画 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('initial-loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.3s ease-out';
            setTimeout(function() {
              loading.remove();
            }, 300);
          }
        }, 500);
      });
    </script>
  </body>
</html>
