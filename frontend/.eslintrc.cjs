module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    "eslint:recommended",
    "plugin:vue/vue3-recommended",
    "@vue/eslint-config-prettier",
  ],
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
  },
  plugins: ["vue"],
  rules: {
    // 禁止使用console语句
    "no-console": process.env.NODE_ENV === "production" ? "error" : "warn",
    // 禁止使用debugger语句
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "warn",
    // 禁止使用alert、confirm、prompt
    "no-alert": "warn",
  },
};
