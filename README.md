# 合同审核系统

一个基于 Vue 3 + Express.js 的现代化合同审核管理系统，提供完整的合同提交、审核、管理功能。

## 🌟 项目特色

- **现代化技术栈**：Vue 3 + Composition API + Element Plus + Express.js + SQLite
- **完整的业务流程**：合同提交 → 审核分配 → 审核处理 → 状态管理
- **权限控制体系**：基于角色的访问控制（RBAC），支持员工、审核员、管理员三种角色
- **响应式设计**：适配桌面和移动设备，提供优秀的用户体验
- **文件管理**：支持 PDF 文件上传、在线预览、安全下载
- **状态管理**：完整的合同状态流转和应用状态管理

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 启动开发服务器

```bash
# 启动后端服务（端口：3000）
cd backend
npm run dev

# 启动前端服务（端口：5173）
cd frontend
npm run dev
```

### 访问应用

- 前端地址：http://localhost:5173
- 后端API：http://localhost:3000/api

### 默认账号

系统初始化时会创建默认管理员账号：

- 用户名：`admin`
- 密码：`admin123`

## 📁 项目结构

```
hetong/
├── backend/                 # 后端服务
│   ├── src/
│   │   ├── routes/         # API 路由
│   │   ├── middleware/     # 中间件
│   │   ├── utils/          # 工具函数
│   │   └── app.js          # 应用入口
│   ├── uploads/            # 文件上传目录
│   └── database.sqlite     # SQLite 数据库
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 通用组件
│   │   ├── composables/    # 组合式函数
│   │   ├── api/            # API 接口
│   │   ├── router/         # 路由配置
│   │   └── utils/          # 工具函数
│   └── public/             # 静态资源
├── prd.txt                 # 产品需求文档
└── README.md              # 项目文档
```

## 🎯 核心功能

### 用户管理
- ✅ 三种用户角色：员工、审核员、管理员
- ✅ JWT 身份认证和权限控制
- ✅ 用户信息管理和密码重置
- ✅ 账号状态管理（正常/禁用/封禁）

### 合同管理
- ✅ 合同提交和流水号自动生成
- ✅ PDF 文件上传、预览、下载
- ✅ 合同状态流转管理
- ✅ 合同修改和重新提交
- ✅ 合同列表查询和筛选

### 审核流程
- ✅ 审核员分配和任务管理
- ✅ 审核状态跟踪和历史记录
- ✅ 审核意见和结果管理
- ✅ 完整的审核工作流

### 系统管理
- ✅ 用户管理（增删改查）
- ✅ 系统统计和监控
- ✅ 数据导出功能
- ✅ 错误处理和日志记录

## 🛠️ 技术架构

### 后端技术栈
- **框架**：Express.js
- **数据库**：SQLite
- **认证**：JWT (JSON Web Token)
- **文件处理**：Multer
- **安全**：bcryptjs, helmet, cors
- **验证**：express-validator

### 前端技术栈
- **框架**：Vue 3 (Composition API)
- **UI 库**：Element Plus
- **路由**：Vue Router 4
- **状态管理**：Pinia / Composition API
- **HTTP 客户端**：Axios
- **构建工具**：Vite

### 开发工具
- **代码规范**：ESLint + Prettier
- **版本控制**：Git
- **测试**：集成测试脚本
- **文档**：Markdown

## 📊 数据库设计

### 用户表 (users)
- id, username, password, role, real_name, email, phone, status, created_at, updated_at

### 合同表 (contracts)
- id, serial_number, filename, file_path, file_size, submitter_id, reviewer_id, status, submit_note, review_comment, created_at, updated_at, reviewed_at

## 🔐 权限设计

### 角色权限矩阵

| 功能 | 员工 | 审核员 | 管理员 |
|------|------|--------|--------|
| 提交合同 | ✅ | ❌ | ✅ |
| 查看自己的合同 | ✅ | ❌ | ✅ |
| 修改自己的合同 | ✅ | ❌ | ✅ |
| 审核合同 | ❌ | ✅ | ✅ |
| 查看分配的合同 | ❌ | ✅ | ✅ |
| 用户管理 | ❌ | ❌ | ✅ |
| 系统管理 | ❌ | ❌ | ✅ |

## 🧪 测试

系统已通过完整的功能测试，包括：

- ✅ 用户认证测试
- ✅ 文件上传测试
- ✅ 合同管理测试
- ✅ 审核流程测试
- ✅ 用户管理测试
- ✅ 系统管理测试

## 📱 响应式设计

系统采用响应式设计，支持以下设备：

- **桌面端**：≥ 1200px
- **平板端**：768px - 1199px
- **手机端**：< 768px

## 🔧 配置说明

### 环境变量

后端支持以下环境变量：

```bash
NODE_ENV=development          # 运行环境
PORT=3000                    # 服务端口
JWT_SECRET=your-secret-key   # JWT 密钥
UPLOAD_PATH=./uploads        # 文件上传路径
```

### 前端配置

前端配置文件位于 `frontend/vite.config.js`：

```javascript
export default defineConfig({
  server: {
    port: 5173,
    proxy: {
      '/api': 'http://localhost:3000'
    }
  }
})
```

## 🚀 部署指南

### 生产环境部署

1. **构建前端**：
```bash
cd frontend
npm run build
```

2. **配置后端**：
```bash
cd backend
npm install --production
```

3. **启动服务**：
```bash
NODE_ENV=production npm start
```

### Docker 部署

```dockerfile
# Dockerfile 示例
FROM node:16-alpine

WORKDIR /app
COPY . .

RUN npm install --production
EXPOSE 3000

CMD ["npm", "start"]
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：[<EMAIL>]

## 🙏 致谢

感谢以下开源项目：

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Element Plus](https://element-plus.org/) - Vue 3 组件库
- [Express.js](https://expressjs.com/) - Node.js Web 框架
- [SQLite](https://www.sqlite.org/) - 轻量级数据库



---

**合同审核系统** - 让合同管理更简单、更高效！
