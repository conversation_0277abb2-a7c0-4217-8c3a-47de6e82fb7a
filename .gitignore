# ================================
# 合同审核系统 .gitignore 配置文件
# ================================

# Node.js 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出目录
dist/
build/
.output/
.nuxt/
.next/
out/

# 环境变量文件
# .env
# .env.local
# .env.development.local
# .env.test.local
# .env.production.local
# .env.*.local

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock



# 数据库文件（保留开发用的示例数据库）
# *.sqlite
# *.sqlite3
# *.db

# 上传文件目录
uploads/*
!uploads/.gitkeep
temp/
tmp/

# 缓存目录
.cache/
.parcel-cache/
.eslintcache
.stylelintcache
.vite/

# IDE 和编辑器配置
.vscode/
.idea/
*.swp
*.swo
*~

# macOS 系统文件
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows 系统文件
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux 系统文件
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Vue.js 相关
*.tsbuildinfo

# TypeScript 相关
*.tsbuildinfo

# npm 缓存
.npm

# Yarn 相关
.yarn-integrity

# 打包文件
*.tgz

# 临时文件夹
tmp/
temp/



# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# Logs
logs
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

.claude/
.cursor
.promptx
.serena

.env

# Task files
# tasks.json
# tasks/ 
.*/