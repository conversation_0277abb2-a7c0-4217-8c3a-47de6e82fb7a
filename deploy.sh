#!/bin/bash

# 简单部署脚本
# 用法: ./deploy.sh 或 ./deploy.sh local 或 ./deploy.sh server

# 如果没有参数，显示选择菜单
if [ $# -eq 0 ]; then
    echo "🚀 合同管理系统 - 部署配置"
    echo
    echo "请选择部署环境:"
    echo "1. 本地开发环境 (localhost)"
    echo "2. 服务器部署环境 (*************)"
    echo
    read -p "请输入选择 (1/2): " choice

    case $choice in
        1)
            echo "选择了本地开发环境"
            DEPLOY_TYPE="local"
            ;;
        2)
            echo "选择了服务器部署环境"
            DEPLOY_TYPE="server"
            ;;
        *)
            echo "❌ 无效选择，退出"
            exit 1
            ;;
    esac
else
    DEPLOY_TYPE="$1"
fi

if [ "$DEPLOY_TYPE" = "local" ]; then
    echo "配置本地环境..."

    # 前端配置 - 使用 .env.development 确保最高优先级
    cat > frontend/.env.development << EOF
# 本地开发环境配置（最高优先级）
VITE_API_BASE_URL=http://localhost:3000/api
VITE_BACKEND_URL=http://localhost:3000
VITE_FRONTEND_URL=http://localhost:5173
EOF

    # 同时配置 .env 作为备用
    cat > frontend/.env << EOF
# 本地开发环境 - 通用配置
VITE_API_BASE_URL=http://localhost:3000/api
NODE_ENV=development
VITE_ENV_TYPE=local
VITE_BACKEND_URL=http://localhost:3000
VITE_FRONTEND_URL=http://localhost:5173
EOF

    # 删除可能存在的 .env.local 文件，避免冲突
    rm -f frontend/.env.local

    # 更新Vite代理配置为本地
    sed -i 's|target: "http://*************:3000"|target: "http://localhost:3000"|g' frontend/vite.config.js

    # 后端配置
    cat > backend/.env << EOF
HOST=0.0.0.0
PORT=3000
NODE_ENV=development
JWT_SECRET=local-dev-jwt-secret-key-2024
CORS_ORIGIN=http://localhost:5173
FRONTEND_URL=http://localhost:5173
EOF
    
    echo "✅ 本地环境配置完成"
    echo "启动PM2服务..."

    # 停止现有服务
    pm2 delete all 2>/dev/null || true

    # 启动服务
    pm2 start "cd backend && npm run dev" --name "hetong-backend"
    pm2 start "cd frontend && npm run dev" --name "hetong-frontend"

    echo "✅ 服务启动完成"
    echo "查看状态: pm2 status"
    echo "查看日志: pm2 logs"
    
elif [ "$DEPLOY_TYPE" = "server" ]; then
    echo "配置服务器环境..."

    # 前端配置 - 使用 .env.development 确保最高优先级
    cat > frontend/.env.development << EOF
# 服务器部署环境 - 开发模式配置（最高优先级）
VITE_API_BASE_URL=http://*************:3000/api
VITE_BACKEND_URL=http://*************:3000
VITE_FRONTEND_URL=http://*************:5173
EOF

    # 同时配置 .env 作为备用
    cat > frontend/.env << EOF
# 服务器部署环境 - 通用配置
VITE_API_BASE_URL=http://*************:3000/api
NODE_ENV=development
VITE_ENV_TYPE=server
VITE_BACKEND_URL=http://*************:3000
VITE_FRONTEND_URL=http://*************:5173
EOF

    # 删除可能存在的 .env.local 文件，避免冲突
    rm -f frontend/.env.local

    # 更新Vite代理配置为服务器IP（备用）
    sed -i 's|target: "http://localhost:3000"|target: "http://*************:3000"|g' frontend/vite.config.js

    # 后端配置 - 绑定0.0.0.0监听所有接口，但显示公网IP
    cat > backend/.env << EOF
HOST=0.0.0.0
PORT=3000
NODE_ENV=development
DISPLAY_HOST=*************
JWT_SECRET=server-prod-jwt-secret-key-2024-secure
CORS_ORIGIN=http://*************:5173
FRONTEND_URL=http://*************:5173
EOF
    
    echo "✅ 服务器环境配置完成"
    echo "启动PM2服务..."

    # 停止现有服务
    pm2 delete all 2>/dev/null || true

    # 启动服务
    pm2 start "cd backend && npm run dev" --name "hetong-backend"
    pm2 start "cd frontend && npm run dev" --name "hetong-frontend"

    echo "✅ 服务启动完成"
    echo "查看状态: pm2 status"
    echo "查看日志: pm2 logs"
    
else
    echo "❌ 无效参数: $DEPLOY_TYPE"
    echo "用法: ./deploy.sh 或 ./deploy.sh local 或 ./deploy.sh server"
    exit 1
fi
