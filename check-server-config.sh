#!/bin/bash

# 服务器配置检查脚本
# 用于诊断 PDF 预览问题和环境配置

echo "🔍 合同管理系统 - 服务器配置检查"
echo "=================================="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_status() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1${NC}"
    fi
}

# 1. 检查后端环境变量配置
echo -e "${BLUE}📋 1. 检查后端环境变量配置${NC}"
echo "----------------------------------------"
if [ -f "backend/.env" ]; then
    echo "后端 .env 文件内容:"
    cat backend/.env
    echo
    
    # 检查关键环境变量
    NODE_ENV=$(grep "NODE_ENV" backend/.env | cut -d'=' -f2)
    HOST=$(grep "HOST" backend/.env | cut -d'=' -f2)
    PORT=$(grep "PORT" backend/.env | cut -d'=' -f2)
    
    echo "关键环境变量:"
    echo "  NODE_ENV: $NODE_ENV"
    echo "  HOST: $HOST"
    echo "  PORT: $PORT"
    
    if [ "$NODE_ENV" = "development" ]; then
        echo -e "${GREEN}✅ NODE_ENV 设置正确 (development)${NC}"
    else
        echo -e "${RED}❌ NODE_ENV 设置错误，应该是 development，当前是: $NODE_ENV${NC}"
    fi
else
    echo -e "${RED}❌ backend/.env 文件不存在${NC}"
fi
echo

# 2. 检查前端环境变量配置
echo -e "${BLUE}📋 2. 检查前端环境变量配置${NC}"
echo "----------------------------------------"
if [ -f "frontend/.env.development" ]; then
    echo "前端 .env.development 文件内容:"
    cat frontend/.env.development
    echo
else
    echo -e "${YELLOW}⚠️ frontend/.env.development 文件不存在${NC}"
fi

if [ -f "frontend/.env" ]; then
    echo "前端 .env 文件内容:"
    cat frontend/.env
    echo
else
    echo -e "${YELLOW}⚠️ frontend/.env 文件不存在${NC}"
fi

# 3. 检查 PM2 服务状态
echo -e "${BLUE}📋 3. 检查 PM2 服务状态${NC}"
echo "----------------------------------------"
pm2 status
echo

# 4. 检查后端启动日志
echo -e "${BLUE}📋 4. 检查后端启动日志${NC}"
echo "----------------------------------------"
echo "最近的后端日志:"
pm2 logs hetong-backend --lines 15 --nostream
echo

# 5. 检查后端 app.js 中的 helmet 配置
echo -e "${BLUE}📋 5. 检查后端安全配置${NC}"
echo "----------------------------------------"
echo "检查 app.js 中的 helmet 配置:"
if [ -f "backend/src/app.js" ]; then
    echo "开发环境 helmet 配置:"
    grep -A 10 -B 2 "SYSTEM_CONFIG.IS_DEVELOPMENT" backend/src/app.js
    echo
    
    echo "frameguard 配置:"
    grep -n "frameguard" backend/src/app.js
    echo
else
    echo -e "${RED}❌ backend/src/app.js 文件不存在${NC}"
fi

# 6. 测试后端健康检查
echo -e "${BLUE}📋 6. 测试后端服务${NC}"
echo "----------------------------------------"
echo "测试健康检查端点:"
HEALTH_RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" http://47.122.89.155:3000/health 2>/dev/null)
HTTP_CODE=$(echo "$HEALTH_RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d':' -f2)
RESPONSE_BODY=$(echo "$HEALTH_RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ 后端服务正常运行${NC}"
    echo "响应内容: $RESPONSE_BODY"
else
    echo -e "${RED}❌ 后端服务异常，HTTP状态码: $HTTP_CODE${NC}"
fi
echo

# 7. 测试文件预览端点的响应头
echo -e "${BLUE}📋 7. 测试文件预览响应头${NC}"
echo "----------------------------------------"
echo "测试根路径的响应头 (检查 X-Frame-Options):"
curl -I http://47.122.89.155:3000/ 2>/dev/null | grep -E "(X-Frame-Options|Content-Security-Policy)"

echo
echo "测试 API 路径的响应头:"
curl -I http://47.122.89.155:3000/api 2>/dev/null | grep -E "(X-Frame-Options|Content-Security-Policy)"
echo

# 8. 检查文件上传目录
echo -e "${BLUE}📋 8. 检查文件上传目录${NC}"
echo "----------------------------------------"
if [ -d "backend/uploads" ]; then
    echo -e "${GREEN}✅ uploads 目录存在${NC}"
    echo "目录内容 (最近5个文件):"
    ls -la backend/uploads | head -6
else
    echo -e "${RED}❌ backend/uploads 目录不存在${NC}"
fi
echo

# 9. 网络连通性测试
echo -e "${BLUE}📋 9. 网络连通性测试${NC}"
echo "----------------------------------------"
echo "测试前端到后端的连接:"
curl -s -o /dev/null -w "前端->后端: HTTP %{http_code}, 耗时: %{time_total}s\n" http://47.122.89.155:3000/health

echo "测试外部访问前端:"
curl -s -o /dev/null -w "外部->前端: HTTP %{http_code}, 耗时: %{time_total}s\n" http://47.122.89.155:5173/ 2>/dev/null || echo "前端连接失败"
echo

# 10. 总结和建议
echo -e "${BLUE}📋 10. 诊断总结${NC}"
echo "----------------------------------------"

# 检查关键问题
ISSUES_FOUND=0

# 检查 NODE_ENV
if [ "$NODE_ENV" != "development" ]; then
    echo -e "${RED}🔧 问题1: NODE_ENV 不是 development${NC}"
    echo "   解决方案: 在 backend/.env 中设置 NODE_ENV=development"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 检查后端服务
if [ "$HTTP_CODE" != "200" ]; then
    echo -e "${RED}🔧 问题2: 后端服务无法访问${NC}"
    echo "   解决方案: 检查 PM2 服务状态，重启后端服务"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

# 检查配置文件
if [ ! -f "backend/.env" ] || [ ! -f "frontend/.env.development" ]; then
    echo -e "${RED}🔧 问题3: 环境配置文件缺失${NC}"
    echo "   解决方案: 运行 ./deploy.sh server 重新配置"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

if [ $ISSUES_FOUND -eq 0 ]; then
    echo -e "${GREEN}🎉 未发现明显配置问题${NC}"
    echo "如果 PDF 预览仍然有问题，可能需要："
    echo "1. 重启后端服务: pm2 restart hetong-backend"
    echo "2. 清理前端缓存: cd frontend && rm -rf node_modules/.vite && pm2 restart hetong-frontend"
else
    echo -e "${YELLOW}⚠️ 发现 $ISSUES_FOUND 个配置问题，请按照上述建议修复${NC}"
fi

echo
echo "🔧 快速修复命令:"
echo "  重新部署: ./deploy.sh server"
echo "  重启服务: pm2 restart all"
echo "  查看日志: pm2 logs"
echo
echo "检查完成！"
